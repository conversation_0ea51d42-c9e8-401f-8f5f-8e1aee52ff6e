var _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
var _ws;
var _connected = false;
var _devInfos = null;
var _supportOcr = 0;
var _devNum = -1;
var _items = null;

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        alert(Lang.WS.disConn);
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}
function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}
/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.func, jsonObj.errorMsg);
        alert(jsonObj.func + "\r\n" + jsonObj.errorMsg);
    } else {
        switch (jsonObj.func) {
            case "GetCameraVideoBuff":
                DisplayVideo(jsonObj);
                break;
            case "GetCameraInfo":
                DisplayDevInfo(jsonObj.devInfo);
                //获取OCR支持信息(语言列表)
                GetOcrInfo();
                break;
            case "CameraVideoParam":
                DisplayCameraVideoParam(jsonObj);
                break;
            case "CameraCapture":
                DisplayCaptureImg(jsonObj);
                break;
            case "GetOcrSupportInfo":
                DisplayOcrSupportInfo(jsonObj);
                break;
            case "FileToBase64":
                DisplayFileView(jsonObj);
                break;
            case "MergeFile":
                DisplayMergeFile(jsonObj);
                break;
            case "Notify":
                // 收到通知
                if (jsonObj.event == "OnUsbKeyPress") {
                    // 收到USB按键按下的通知
                    // {"func":"Notify","event":"OnUsbKeyPress","reqId":1636099207839.0,"result":0,"time":"1636099214"}
                    // DisplayUsbKeyPress(jsonObj);
                } else if (jsonObj.event == "OnDeviceChanged") {
                    // 设备有变化,格式如下:
                    // {"func":"Notify","event":"OnDeviceChanged","devName":"\\\\?\\USB#VID_0C45&PID_6366...","func":"OnDeviceChanged","result":0,"type":1}
                    // type: 1设备插入; 2设备已拔出
                    GetCameraInfo();
                }
                break;
            case "FileOCR":
                DisplayConvertToFile(jsonObj);
                break;
            default:
                console.log(msg);
                break;
        }
    }
}

// 获取设备信息
function GetCameraInfo() {
    SendJson({func: "GetCameraInfo", reqId: new Date().getTime()});
}

// 获取OCR支持信息
function GetOcrInfo() {
    SendJson({func: "GetOcrSupportInfo", reqId: new Date().getTime()});
}

//显示合并成功的PDF文件
function DisplayMergeFile(jsonObj) {
    if (jsonObj.filePath) {
        alert(Lang.MSG.mergePdfOk + "\r\n" + jsonObj.filePath);
        if (location.protocol.substr(0,4) == 'http') {
            var pos = jsonObj.filePath.lastIndexOf('\\');
            pos = jsonObj.filePath.lastIndexOf('\\', pos - 1);
            var url = location.origin + '/tmp/' + jsonObj.filePath.substr(pos + 1).replace('\\','/');
            window.open(url);
        }
    }
}

//显示识别成功的文件
function DisplayConvertToFile(jsonObj) {
    if (jsonObj.filePath) {
        alert(Lang.MSG.mergePdfOk + "\r\n" + jsonObj.filePath);
        var pos = jsonObj.filePath.lastIndexOf('\\');
        pos = jsonObj.filePath.lastIndexOf('\\', pos - 1);
        var url = location.origin + '/tmp/' + jsonObj.filePath.substr(pos + 1).replace('\\','/');
        window.open(url);
    }
}

var _w = 0, _h = 0, _wp = 0, _hp = 0;
function DisplayVideo(v) {
    if (v.imgBase64Str) {
        if (_w != v.width || _h != v.height) {
            //设置视频显示的宽高（高度控制为484px）
            _hp = 484, _wp = _hp * v.width / v.height, _w = v.width, _h = v.height;
            $("#pic").css('width', _wp + 'px').css('height', _hp + 'px');
        }

        //显示视频
        document.getElementById("pic").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    }
}

function DisplayDevInfo(devInfos) {
    _devInfos = devInfos;
    if (_devInfos.length == 0) {
        alert(Lang.cam.doc);
        return;
    }
    //初始化设备信息
    displayCamera();
    switchCameraInfo_Fun();
}

function DisplayOcrSupportInfo(v) {
    if (v.languages) {
        // 支持orc, 允许‘自动文字方向’
        _supportOcr = 1;

        var obj = document.getElementById("languages");
        obj.options.length = 0;
        for (var i = 0; i < v.languages.length; i++) {
            obj.options.add(new Option(v.languages[i], i));
        }
        // 默认选中'Simplified chinese+English'
        obj.selectedIndex = 98;

        obj = document.getElementById("extNames");
        obj.options.length = 0;
        for (var i = 0; i < v.fileTypes.length; i++) {
            obj.options.add(new Option(v.fileTypes[i], i));
        }
    }
}

function buildTr(p) {
    // capsFlag标志: 0不支持，1仅自动，2仅手动，3可设置
    var support = p.capsFlag > 0;
    // flag标志: 1自动，2手动
    var html = '<tr pid="' + p.id + '">';
    html += '<td>' + p.name + '</td>';
    html += '<td><input type="range" class="valAdj" min="' + p.min + '" max="' + p.max + '" value="' + p.val + '" step="' + p.step + '"';
    if (!support || p.flag == 1) {
        html += ' disabled';
    }
    html += '></td>';
    html += '<td><input type="number" class="valDisp" disabled';
    if (support) {
        html += ' value="' + p.val + '"';
    }
    html += '></td>';
    html += '<td><input type="checkbox" class="autoChk"';
    if (support && p.flag == 1) {
        html += ' checked';
    }
    if (!support || p.capsFlag != 3) {
        html += ' disabled';
    }
    html += '></td>';
    html += '</tr>';
    return html;
}

function DisplayCameraVideoParam(v) {
    //console.log(v);
    if (v.items && v.items.length > 0) {
        _items = v.items;
        // 删除[视频设置]与[相机控制]表格的所有行
        $(".ctrlTable tr").remove();

        var tab1 = $("#tab1"), tab2 = $("#tab2");
        for (var i = 0; i < v.items.length; i++) {
            var item = v.items[i];
            var tr = buildTr(item);
            if (item.id == 6 || item.id == 105) {
                // chk6 启用颜色，chk105 低照度补偿
                if (item.capsFlag == 0) {
                    $("#chk" + item.id).val(0).attr("disabled", "true");
                    $("#lb" + item.id).text(item.name).attr("disabled", "true");
                } else {
                    $("#chk" + item.id).val(item.val).removeAttr("disabled");
                    $("#lb" + item.id).text(item.name).removeAttr("disabled");
                }
            } else if (item.id < 100) {
                tab1.append(tr);
            } else {
                tab2.append(tr);
            }
        }
        $("#powerFreq").val(v.powerFreq);

        $(".valAdj").change(function(){
            var tr = this.parentNode.parentNode;
            var pid = parseInt(tr.getAttribute("pid"));
            var val = parseInt(this.value);
            $(tr).find(".valDisp").val(val);
            SendCameraVideoParam([{id: pid, val: val, flag: 2}]); // 2手动
        });
        $(".autoChk").change(function(){
            var tr = this.parentNode.parentNode;
            var pid = parseInt(tr.getAttribute("pid"));
            var objRange = $(tr).find(".valAdj");
            var val = parseInt(objRange.val());
            if (this.checked) {
                $(tr).find(".valAdj").attr("disabled", "true");
            } else {
                $(tr).find(".valAdj").removeAttr("disabled");
            }
            SendCameraVideoParam([{id: pid, val: val, flag: (this.checked ? 1 : 2)}]); // flag标志: 1自动，2手动
        });
    }
}

function restoreDefault(v) {
    if (_items && v > 0) {
        var ary = [];
        for (var i = 0; i < _items.length; i++) {
            var item = _items[i];
            if (item.capsFlag > 0 && (v == 1 && item.id < 100 || v > 1 && item.id >= 100)) {
                var objTr = $("tr[pid=" + item.id + "]");
                var autoCheked = objTr.find(".autoChk").prop("checked");
                objTr.find(".valAdj").val(item.defVal);
                objTr.find(".valDisp").val(item.defVal);
                ary.push({id: item.id, val: item.defVal, flag: (autoCheked ? 1 : 2)});
            }
        }
        SendCameraVideoParam(ary);
    }
}

function SendCameraVideoParam(ary) {
    SendJson({func:"CameraVideoParam", reqId: new Date().getTime(), devNum: _devNum, items: ary});
}

//初始化信息
$(function () {
    //获取摄像头设备列表
    GetCameraInfo();
})

function cameraCapture_Fun() {
    SendJson({func:"CameraCapture", reqId: new Date().getTime(), devNum: _devNum, mode: "path"});
}

//设备信息的onchange事件
function switchCameraInfo_Fun() {
    var reqId = new Date().getTime();
    if (_devNum >= 0) {
        //关闭之前的摄像头
        SendJson({func:"CloseCamera", reqId: reqId, devNum: _devNum});
        reqId++;
    }

    //开启摄像头
    _devNum = $("#cameraInfo").val() == null ? 0 : parseInt($("#cameraInfo").val());
    SendJson({func:"OpenCamera", reqId: reqId, devNum: _devNum, mediaNum:0, resolutionNum:0, fps:5});
    reqId++;

    // 获取预览视频
    SendJson({func:"GetCameraVideoBuff", reqId: reqId, devNum: _devNum, enable: true});
    reqId++;

    // 读取摄像头的 相机视频设置信息
    SendJson({func:"CameraVideoParam", reqId: reqId, devNum: _devNum});
}

function onChangePowerFreq() {
    SendJson({func:"CameraVideoParam", reqId: new Date().getTime(), devNum: _devNum, powerFreq: parseInt($("#powerFreq").val())});
}

// 刷新显示 设备名称 下拉列表
function displayCamera() {
    var obj = document.getElementById("cameraInfo");
    var lastSelectedIndex = obj.options.selectedIndex;
    obj.options.length = 0;
    for (var i = 0; i < _devInfos.length; i++) {
        obj.options.add(new Option(_devInfos[i].devName, _devInfos[i].id));
    }
    if (lastSelectedIndex >= 0 && lastSelectedIndex < _devInfos.length) {
        obj.options.selectedIndex = lastSelectedIndex;
    }
}

function DisplayCaptureImg(v) {
    if (v.imagePath) {
        for (var i = 0; i < v.imagePath.length; i++) {
            displayFile(v.imagePath[i]);
        }
        audioCapture.play();
    }
}

var _path = null;
var _th = '<tr><th>文件名</th><th>操作</th></tr>';
var _tdOpration = '<td><a onclick="vf()">查看</a> <a onclick="df()">删除</a></td>';
function displayFile(file) {
    var pos = file.lastIndexOf('\\');
    if (_path == null) {
        _path = file.substring(0, pos);
    }
    var html = '<tr><td>' + file.substring(pos + 1) + '</td>' + _tdOpration + '</tr>';
    $("#tbFile").append(html);
}

// 清空图片展示列表
function clearImageFile() {
    $("#image_data").empty();
    $("#tbFile").empty().append(_th);
}

function ocrFile() {
    var reqId = new Date().getTime();
    var fileArray = new Array();
    var trs = $('#tbFile tr');
    for (i = 1; i < trs.length; i++) {
        fileArray.push(_path + '\\' + trs[i].children[0].innerText);
    }
    var lang = $("#languages").find("option:selected").text();
    var extName = $("#extNames").find("option:selected").text();
    SendJson({func: 'FileOCR', reqId: reqId, language: lang, extName: extName, DetectTextOrientation: false, imagePath:fileArray});
}
function mergePDF() {
    var reqId = new Date().getTime();
    var fileArray = new Array();
    var trs = $('#tbFile tr');
    for (i = 1; i < trs.length; i++) {
        fileArray.push(_path + '\\' + trs[i].children[0].innerText);
    }
    // fileType: 0 PDF; 1-TIFF(暂不支持)
    SendJson({func: 'MergeFile',reqId:reqId, fileType:0, imagePath:fileArray, mode:'path;base64'});
}

function vf() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    var file = _path + '\\' + $(target).parent().prev().text();

    // 把文件转为base64,用于显示查看
    SendJson({func:'FileToBase64', reqId:new Date().getTime(), filePath:file});
}

function DisplayFileView(v) {
    var img = document.getElementById("imgView");
    img.src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    $("#divView").css('display', '').css('width', '').css('height', '');
    if (img.height > $("#divView").innerHeight()) {
        $("#divView").css('height', img.height + 'px');
    }
    if (img.width > $("#divView").innerWidth()) {
        $("#divView").css('width', img.width + 'px');
    }
}

// 删除拍照文件
function df() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    $(target).parent().parent().remove();
}

// 关闭图片查看
function closeView() {
    $("#divView").css('display', 'none');
    document.getElementById("imgView").src = "";
}
