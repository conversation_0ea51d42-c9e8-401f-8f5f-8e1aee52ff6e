<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>高拍仪演示 - CamSDK 江苏一体化人口信息管理平台(NeuSoft)
    </title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../JS/common/html5shiv.min.js"></script>
    <script src="../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">CamSDK HTTP访问方式(NeuSoft)演示<span id="verInfo">Ver: 20191015</span></a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li class="active"><a href="#">高拍仪演示</a></li>
<!--                <li><a href="CameraVice.html">高拍仪副头</a></li>-->
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/camera.jpg">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>高拍仪演示</h1>
        <p>这个页面演示了按照东软集团(NeuSoft)提出的HTTP交互协议，如何打开高拍仪的摄像头、显示摄像头视频、设置分辨率、拍照、全并PDF文件等功能。同时也支持websocket方式的访问。</p>
    </div>

    <div class="row">
        <div class="col-md-5" style="text-align: center">
            <div class="row">
                <label>主头 分辨率:</label>
                <select id="resolution1" onchange="switchResolution(0)"></select>
                <label class="lbn">FPS:</label>
                <input type="number" id="fps1" min="1" max="30" value="10" onchange="switchFPS(0)">
                <label class="lbn">旋转:</label>
                <select id="rotate1" onchange="doRotate(0)">
                    <option value="0">0</option>
                    <option value="90">90度</option>
                    <option value="180">180度</option>
                    <option value="270">270度</option>
                </select>
                <br>
                <img id="videoMain" src="">
            </div>
            <div class="page-header">
            </div>
            <div class="row">
                <label>副头 分辨率:</label>
                <select id="resolution2" onchange="switchResolution(1)"></select>
                <label class="lbn">FPS:</label>
                <input type="number" id="fps2" min="1" max="30" value="10" onchange="switchFPS(1)">
                <label class="lbn">旋转:</label>
                <select id="rotate2" onchange="doRotate(1)">
                    <option value="0">0</option>
                    <option value="90">90度</option>
                    <option value="180">180度</option>
                    <option value="270">270度</option>
                </select>
                <br>
                <img id="videoVice" src="">
            </div>
        </div>

        <div class="col-md-6" style="text-align: left">
            <label class="fieldItem">颜色:
                <select id="fieldColor" style="width:100px;">
                    <option value="0">彩色</option>
                    <option value="1">灰度</option>
                    <option value="2">黑白</option>
                </select>
            </label>
            <label class="fieldItem">裁切方式:
                <select id="fieldCrop" style="width:100px;">
                    <option value="0">不裁切</option>
                    <option value="1">自动切边</option>
                    <option value="2">自定义切边</option>
                    <option value="3">书刊展平</option>
                    <option value="4">人脸切边</option>
                </select>
            </label>
            <label class="fieldItem"><input type="checkbox" id="fieldMulti">多图输出</label>
            <label class="fieldItem"><input type="checkbox" id="fieldSplitPage">书刊分页</label>
            <p/>
            <label class="fieldItem">旋转:
                <select id="rotate3" style="width:65px;">
                    <option value="0" selected>0</option>
                    <option value="90">90度</option>
                    <option value="180">180度</option>
                    <option value="270">270度</option>
                </select>
            </label>
            <label class="fieldItem">固定宽:
                <input type="number" id="fieldWidth" min="0" max="6000" value="0" style="width: 60px;">
            </label>
            <label class="fieldItem">固定高:
                <input type="number" id="fieldHeight" min="0" max="6000" value="0" style="width: 60px;">
            </label>
            <label class="fieldItem"><input type="checkbox" id="fieldReduceShadow">去除阴影</label>
            <label class="fieldItem"><input type="checkbox" id="fieldRemoveBlackEdge">去除黑边</label>
            <label class="fieldItem"><input type="checkbox" id="fieldRectCorrect">形变校正</label>
            <label class="fieldItem"><input type="checkbox" id="fieldTextDir">自动文字方向</label>

            <label title="暂未实现,与现有SDK对应不上">文档类型:</label>
            <select id="fieldDocType" style="width: 100px;">
                <option value="0">白底文档</option>
                <option value="1" selected>彩页</option>
                <option value="2">证件卡片</option>
            </select>
            <label class="fieldItem">DPI:
                <input type="number" id="fieldDPI" min="100" max="600" value="200">
            </label>
            <label class="fieldItem">JPEG图像压缩质量:
                <input type="number" id="fieldQuality" min="50" max="100" value="80">
            </label>
            <label class="fieldItem"><input type="checkbox" id="fieldCreateUUID">生成uuid</label>
            <p/><p/>

            <label class="fieldItem" title="在OCR识别时不要使用水印功能，否则会影响识别效果">
                <input type="checkbox" id="watermark" value="0" onchange="OnChgWatermark()"><b>拍照打水印</b>
            </label>
            <label class="fieldItem">颜色:
                <input type="color" value="#7A7A7A" id="wmColor">
            </label>
            <label class="fieldItem">透明度:
                <input type="number" id="transparency" min="0" max="255" value="150">
            </label>
            <label class="fieldItem">位置:
                <select id="wmLayout" style="width:80px;">
                    <option value="0">左上角</option>
                    <option value="1">右上角</option>
                    <option value="2">左下角</option>
                    <option value="3">右下角</option>
                    <option value="4">中间</option>
                </select>
            </label>
            <label class="fieldItem">字号:
                <input type="number" min="20" max="200" value="60" id="wmSize" style="width:45px;">
            </label>
            <label class="fieldItem">水印文本:
                <input type="text" style="width:162px;" value="机密文件" id="wmText">
            </label>
            <button type="button" class="btn btn-primary" onclick="doCaptureHttp()" title="以http方式进行拍照">HTTP拍照</button>
            <button type="button" class="btn btn-primary" onclick="doCaptureWs()" title="以websocket方式进行拍照">WS拍照</button>
            <br><br>

            <label>拍照文件列表:</label>
            <button type="button" class="btn btn-primary" id="btnMergePDF" onclick="mergePDF()">合并PDF</button>
            <button type="button" class="btn btn-primary" id="btnClearFile" onclick="clearImageFile()">清空文件</button>
            <div class="col-sm-12" style="padding: 0;margin: 10px 0;overflow-y: scroll;height:300px;">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th width="60%" style="text-align: center;">文件名</th>
                        <th style="text-align: center;">操作</th>
                    </tr>
                    </thead>
                    <tbody id="tabBody">
                    </tbody>
                </table>
            </div>
        </div>

    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none;">
    </div>
</div>

<audio id="audioCapture">
    <source src="../audio/Capture.wav" type="audio/wav">
</audio>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">拍照文件查看</h4>
            </div>
            <div class="modal-body">
                <img id="imgView" src="" style="max-width:860px">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../bootstrap/js/bootstrap.min.js"></script>
<script src="../langs/zhCN.js"></script>
<script src="JS/Camera.js"></script>
</body>
</html>