import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询问题分类列表
export function listClass(query) {
    return request({
      url: '/problemclass/doQuery',
      method: 'post',
      data: query
    })
  }

// 查询问题列表
export function listProblem(query) {
    return request({
      url: '/problem/doQuery',
      method: 'post',
      data: query
    })
  }


  // 查询分类详细
export function getClass(problemclassId) {
    return request({
      url: '/problemclass/' + parseStrEmpty(problemclassId),
      method: 'get'
    })
}


  // 查询问题详细
  export function getProblem(problemId) {
    return request({
      url: '/problem/' + parseStrEmpty(problemId),
      method: 'get'
    })
}

// 新增分类
export function addClass(data) {
    return request({
      url: '/problemclass/doAdd',
      method: 'post',
      data: data
    })
}

// 新增问题
export function addProblem(data) {
    return request({
      url: '/problem/doAdd',
      method: 'post',
      data: data
    })
}


// 修改分类
export function updateClass(data) {
    return request({
      url: '/problemclass/doEdit',
      method: 'post',
      data: data
    })
}


// 修改问题
export function updateProblem(data) {
    return request({
      url: '/problem/doEdit',
      method: 'post',
      data: data
    })
}

