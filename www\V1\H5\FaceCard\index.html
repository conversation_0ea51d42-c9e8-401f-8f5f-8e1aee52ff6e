<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- utf-8编码-->
    <meta charset="utf-8">
    <title>CamSDK</title>
</head>
<body >
  
    <div style="width:1080px;margin:0px auto;">
		
		<div style="float:left">
		
			<div id="video1">
				<img id="video" style="width:506px;height:380px" >
			</div>
			
			 <div style="margin-left:5px;margin-top:10px;float:left">
				<label >设备</label>
				<select id="devicList" ></select>
			</div>
			<div style="margin-left:280px;margin-top:10px;">
				<label >分辨率</label>
				<select id="reslutionList" ></select>
			</div>
		</div>

		<div style="margin-left:560px">
				<label >比对方式</label>
				<select id="compareType" style="width:150px" onchange="IDCardFaceRecogin.SelectCompareType()">
					<option>身份证</option>
					<!--option>视频</option-->
					<option>文件</option>	
				</select>
				<button type="button"  style="margin-left: 20px" onclick="IDCardFaceRecogin.Capture()">采集</button>
				
				<div  id="compareGroupImg" style="margin-top:10px;">
					<img style="width:200px;height:180px;background:black" id="comparePicImg">
					<img style="width:200px;height:180px;background:black" id="realPicImg">
				
				 <div >
					<button style="width:200px;height:180px;text-align:center;" onclick="IDCardFaceRecogin.StartCompare()">开始比对</button>
					<input type="text" style="width:200px;height:180px;text-align:center;" disabled="disabled" value="比对分数" id="compareReslut"></input>
				</div>
			</div>
				
		</div>
			
		
	</div>

     <!-- SDK封装 -->
    <script src="js/globeVar.js"></script>
    <script src="js/mainH5.js"></script>
    <script src="js/WebSocket.js"></script>

    <!--页面逻辑 -->
    <script src="js/IDCardFaceRecogin.js"></script>


</body>
</html>