<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="责任单位" prop="blackspotOrgId">
          <treeselect v-model="queryParams.blackspotOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择责任单位"  style="width:200px"/>
      </el-form-item>
      <el-form-item label="状态" prop="state">
       <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="blackspotList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="责任单位" align="center" prop="organization.organName" :show-overflow-tooltip="true" />
      <el-table-column label="责任人" align="center" prop="worker.workerName" :show-overflow-tooltip="true" />
      <el-table-column label="检查项目" align="center" prop="problemclass.className" :show-overflow-tooltip="true" />
      <el-table-column label="发现时间" align="center" prop="detectTime" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">待确认</span>
          <span v-if="scope.row.state==2">待整改</span>
          <span v-if="scope.row.state==3">待验收</span>
          <span v-if="scope.row.state==4">验收完成</span>
          <span v-if="scope.row.state==5">整改中</span>
          <span v-if="scope.row.state==6">驳回</span>
          <span v-if="scope.row.state==7">验证失败</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.state==1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button v-if="scope.row.state==1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDel(scope.row)"
          >删除</el-button>
          <el-button v-if="scope.row.state!=1"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="检查项目" prop="problemclassId">
         <el-select v-model="form.problemclassId" placeholder="请选择检查项目" clearable style="width:100%" @change="handleClassChange">
          <el-option
            v-for="item in classList"
            :key="item.problemclassId"
            :label="item.className"
            :value="item.problemclassId">
          </el-option>
        </el-select>
        </el-form-item>

        <el-form-item label="责任单位" prop="blackspotOrgId">
          <treeselect v-model="form.blackspotOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择责任单位"  @input="orgchange" />
        </el-form-item>
        <el-form-item label="责任人" prop="liableerId">
          <el-select v-model="form.liableerId" placeholder="请选择责任人" clearable>
            <el-option
              v-for="item in liableers"
              :key="item.worker.workerId"
              :label="item.worker.workerName"
              :value="item.worker.workerId">
            </el-option>
          </el-select>
        </el-form-item> 
        <el-form-item label="发现时间" prop="detectTime">
          <el-date-picker v-model="form.detectTime" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="现场图片" prop="imgPath">
            <image-upload v-model="imgPath" :limit="5"  :fileType="['png', 'jpg', 'jpeg']"/>
        </el-form-item>
        <el-form-item label="隐患描述" prop="blackspotDesc">
          <el-input  type="textarea" v-model="form.blackspotDesc" placeholder="请输入风险描述" maxlength="250"/>
        </el-form-item>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

     <el-dialog  :visible.sync="viewopen" width="800px" append-to-body>
        <el-form ref="blackspostvo" :model="blackspostvo" label-width="100px" size="mini">
          <el-row>
            <el-col :span="8">
              <el-form-item label=" 责任单位："> <span v-if="blackspostvo.organization!==undefined">{{blackspostvo.organization.organName}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" 分类："> {{blackspostvo.problemclass.className}}</el-form-item>
            </el-col>
           <el-col :span="8">
              <el-form-item label=" 发现时间："> {{blackspostvo.detectTime}}</el-form-item>
            </el-col>
           
            <el-col :span="8">
              <el-form-item label=" 状态：">  <span v-if="blackspostvo.state==1">待确认</span>
            <span v-if="blackspostvo.state==2">待整改</span>
            <span v-if="blackspostvo.state==3">待验收</span>
            <span v-if="blackspostvo.state==4">验收完成</span>
            <span v-if="blackspostvo.state==5">整改中</span>
            <span v-if="blackspostvo.state==6">驳回</span>
            <span v-if="blackspostvo.state==7">验证失败</span>
            </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label=" 描述：">  {{blackspostvo.blackspotDesc}}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label=" 现场图片：">   
                <span v-for="(imgpath,index) in blackspostvo.imgPaths" :key="index" style="paddingRight :5px">
                <image-preview :src="imgpath" :width="120" :heigth="120"/>
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
       <div slot="footer" class="dialog-footer">
        <el-button @click="viewopen = false">关 闭</el-button>
        </div>
     </el-dialog>
  </div>
</template>

<script>
import { listClass, listProblem} from "@/api/patrol/problem";
import { listblackspothand, getblackspothand,addblackspothand,updateblackspothand,delblackspothand} from "@/api/patrol/blackspot";
import {  listDeptExcludeChild } from "@/api/system/dept";
import {queryOrgWork} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Blackspot",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分类数据
      classList: [],
      //问题列表数据
      blackspotList:[],
      //部门
      deptOptions:[],
      //问题列表
      problems:[],
      //图片地址
      imgPath:undefined,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示弹出显示层
      viewopen:false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      //责任人
      liableers:[],
      // 表单参数
      form: {},
      //详情
      blackspostvo:{
        organization:{organName:''},
        orgarea:{areaName:''},
        worker:{workerName:''},
        problemclass:{className:''},
        problem:{perform:''}
      },
      // 表单校验
      rules: {
        problemclassId: [
          { required: true, message: "分类不能为空", trigger: "blur" }
        ],
        perform: [
          { required: true, message: "现象不能为空", trigger: "blur" }
        ],
        state: [
          { required: true, message: "状态不能为空", trigger: 'change' }
        ],
        blackspotOrgId:[
           { required: true, message: "责任单位不能为空", trigger: 'change' }
        ],
        liableerId:[
           { required: true, message: "责任人不能为空", trigger: 'change' }
        ]
      },
         //状态
      options: [{
          value: 1,
          label: '待整改'
        }, {
          value: 2,
          label: '整改中'
      }, {
          value: 3,
          label: '整改完成'
      }, {
          value: 4,
          label: '验收成功'
      }, {
          value: 5,
          label: '验收失败'
      }],
    };
  },
  created() {
    this.getDeptTree();
    this.getList();
    this.getClassList();
  },
  methods: {
    /** 查询问题分类 */
    getClassList() {
      let classparam = {}
      classparam.state=1
      listClass(classparam).then(response => {
          this.classList = response.data;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      let queryparams1 = {};
      listDeptExcludeChild(queryparams1).then(response => {
        let datas = response.data;
        for(let i=0; i<datas.length; i++){
          datas[i].children=[];
        }
        this.deptOptions = this.handleTree(datas, "organId");
      });
    },
       /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    //问题描述
    getList(){
      this.loading = true;
       this.queryParams.orderBy="creationtime desc"
      listblackspothand(this.queryParams).then(response => {
          this.blackspotList = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },
    //选择问题分类
    handleClassChange(data){
      let problemparams = {}
      problemparams.problemclassId=data
      problemparams.state=1
      listProblem(problemparams).then(response => {
          this.problems = response.data;
        }
      );
    },
    handleProblemChange(data){
      this.problems.forEach(b =>{
        if(b.problemId===data){
          this.form.blackspotDesc=b.riskDesc
          this.form.blackspotLevel=b.problemLevel
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        blackspothandId: undefined,
        blackspotOrgId: undefined,
        corp: undefined,
        areaId: undefined,
        liableerId:undefined,
        problemclassId:undefined,
        problemshowId:undefined,
        state:undefined,
        blackspotDesc:undefined,
        blackspotLevel:undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.imgPath=undefined
      this.open = true;
      this.title = "隐患上报";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.problemId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.imgPath=undefined
      const blackspothandId = row.blackspothandId || this.ids
      getblackspothand(blackspothandId).then(response => {
        this.form = response.data;
        if(typeof(this.form.imgPaths)!="undefined"){
          this.imgPath=this.form.imgPaths.join(",")
        }
        //查询责任单位人员
        queryOrgWork(this.form.blackspotOrgId).then(res=>{
           if(res.code==200){
              this.liableers=res.data;
            }
        })
        this.open = true;
        this.title = "修改隐患";
      });
    },
    /**删除按钮操作 */
    handleDel(row){
      const blackspothandId = row.blackspothandId || this.ids
       getblackspothand(blackspothandId).then(response => {
        if(response.data.state!==1){
          this.$alert('该隐患已经处理，不可删除', '提示', {
          confirmButtonText: '确定'
        });
        }else{
           this.$modal.confirm('是否确认删除隐患信息？').then(function() {
              return delblackspothand(blackspothandId);
            }).then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
        }

      });
    },
    /**查看按钮操作 */
    handleView(row){
       const blackspothandId = row.blackspothandId || this.ids
       getblackspothand(blackspothandId).then(response => {
        this.blackspostvo = response.data;
        if(typeof(this.blackspostvo.imgPaths)!="undefined"){
          this.imgPath=this.blackspostvo.imgPaths.join(",")
        }
        this.viewopen = true;
        this.title = "查看隐患";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(typeof(this.imgPath) !=="undefined" && this.imgPath!=="" ){
            this.form.imgPaths=this.imgPath.split(",")
          }else{
            this.form.imgPaths = undefined
          }
          console.log(JSON.stringify(this.form))
          if (this.form.blackspothandId != undefined) {
            updateblackspothand(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.state=1 //待整改
            addblackspothand(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    //选择责任单位
    orgchange(value){
        if(typeof(value) != "undefined"){
          queryOrgWork(value).then(response=>{
            if(response.code==200){
              this.liableers=response.data;
            }
          })
        }
       
    }



  }
};
</script>