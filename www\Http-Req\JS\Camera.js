function MyWsConnect(port, onTxtMsg, onError) {
    this.port = port;
    this.ws = null;
    this.connected = false;
    this.onTxtMsg = onTxtMsg;
    this.onError = onError;
    this.waitSendMsg = null;

    this.Open = function () {
        if (!this.connected) {
            var that = this;
            let url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
            if ('WebSocket' in window) {
                this.ws = new WebSocket(url);
            } else if (window.WebSocket) {
                this.ws = new WebSocket(url);
            } else if ('MozWebSocket' in window) {
                this.ws = new MozWebSocket(url);
            } else {
                alert(Lang.WS.lowVer);
            }
            this.ws.onopen = function (e) {
                that.connected = true;
                console.log(e.target.url + ' connected', e);
                if (that.waitSendMsg) {
                    that.SendJson(that.waitSendMsg);
                    that.waitSendMsg = null;
                }
            }
            this.ws.onclose = function (e) {
                that.connected = false;
            }
            this.ws.onmessage = function (e) {
                if (typeof e.data === "string") {
                    that.onTxtMsg(e.data, that.port);
                } else {
                    console.log('onBinMessage', e);
                }
            }
            this.ws.onerror = function (e) {
                console.log('onerror', e);
                if (typeof (that.onError) === "function") {
                    that.onError(e, Lang.WS.disConn);
                }
            };
        }
    };

    this.Close = function () {
        if (this.connected) {
            this.ws.close();
        }
    };

    this.SendJson = function (jsonMsg) {
        if (this.connected) {
            this.ws.send(JSON.stringify(jsonMsg));
        } else {
            this.waitSendMsg = jsonMsg;
            this.Open();
        }
    };
};

// 处理接收到的信令消息
function onTxtCmdMessage(msg, myWsObj) {
    var jv = JSON.parse(msg);
    if (jv.code == 0) {
        switch (jv.function) {
            case "grab_image":
                // 收到拍照回应:
                //console.log(msg);
                audioCapture.play();
                displayOutputInfo(2, "Websocket方式拍照成功.");
                for (var i = 0; i < jv.images.length; i++) {
                    displayFile(jv.images[i].filepath);
                }
                break;

            default:
                console.log(msg);
                displayOutputInfo(1, "收到未处理的消息:<br>" + msg);
                break;
        }
    } else {
        // 统一显示错误信息
        let info = "回应命令ID: " + jv.id + ", 错误代码: " + jv.error;
        if (jv.result) {
            info += "<br>维山错误代码: " + jv.result + ", 错误信息: " + jv.errorMsg;
        }
        displayOutputInfo(3, info);
    }
}

// 处理连接错误
function onError(e, msg) {
    displayOutputInfo(3, msg);
}

// websocket连接
var _myWs1 = null;

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

// 获取应用程序版本信息
function ReadAppVersion() {
    displayOutputInfo(1, "获取应用程序版本信息......");
    $.ajax({
        type: 'POST',
        url: "/param=get",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify({type: "app_version"}),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "获取应用程序版本信息 " + (r.message ? r.message : "请求成功."));
                $('#verInfo').text("Ver: " + r.app_version);
            } else {
                displayOutputInfo(3, "获取应用程序版本信息 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// 获取可用摄像头数量
function ReadAllCamName() {
    displayOutputInfo(1, "获取可用摄像头数量......");
    $.ajax({
        type: 'GET',
        url: "/video=caminfo",
        dataType: "json",
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "获取可用摄像头数量 " + (r.message ? r.message : "请求成功."));
                var count = r.caminfo.length;
                if (count > 0) {
                    ReadDevResolutions(0);
                    if (count > 0) {
                        ReadDevResolutions(1);
                    }
                }
            } else {
                displayOutputInfo(3, "获取可用摄像头数量 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// 获取视频分辨率
function ReadDevResolutions(devNum) {
    displayOutputInfo(1, "获取视频分辨率......");
    $.ajax({
        type: 'POST',
        url: "/param=get",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify({type: "resolution", camidx: devNum}),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "获取视频分辨率 " + (r.message ? r.message : "请求成功."));
                var id = r.camidx == 0 ? "resolution1" : "resolution2";
                var obj = document.getElementById(id);
                var ary = r.resolution.split("|");
                obj.options.length = 0;
                for (var i = 0; i < ary.length; i++) {
                    var op = new Option(ary[i], i);
                    obj.options.add(op);
                }
                var idImg = r.camidx == 0 ? "videoMain" : "videoVice";
                document.getElementById(idImg).src = "/video=stream&camidx=" + r.camidx;
            } else {
                displayOutputInfo(3, "获取视频分辨率 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// 设置分辨率
function switchResolution(devNum) {
    var id = devNum == 0 ? "#resolution1" : "#resolution2";
    var txt = $(id).find("option:selected").text();
    var pos = txt.indexOf('x');
    var w = parseInt(txt), h = parseInt(txt.substring(pos + 1));
    displayOutputInfo(1, "设置分辨率......");
    $.ajax({
        type: 'POST',
        url: "/param=set",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify({resolution: {width: w, height: h}, camidx: devNum}),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "设置分辨率 " + (r.message ? r.message : "请求成功."));
            } else {
                displayOutputInfo(3, "设置分辨率 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// 设置预览帧率
function switchFPS(devNum) {
    var id = devNum == 0 ? "#fps1" : "#fps2";
    var fps = parseInt($(id).val());
    displayOutputInfo(1, "设置预览帧率......");
    $.ajax({
        type: 'POST',
        url: "/param=set",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify({framerate: fps, camidx: devNum}),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "设置预览帧率 " + (r.message ? r.message : "请求成功."));
            } else {
                displayOutputInfo(3, "设置预览帧率 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// 设置旋转角度
function doRotate(devNum) {
    var id = devNum == 0 ? "#rotate1" : "#rotate2";
    var angle = parseInt($(id).val());
    displayOutputInfo(1, "设置旋转角度......");
    $.ajax({
        type: 'POST',
        url: "/video=direction",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify({direction: angle, camidx: devNum}),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "设置旋转角度 " + (r.message ? r.message : "请求成功."));
            } else {
                displayOutputInfo(3, "设置旋转角度 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// Websocket方式拍照
function doCaptureWs() {
    var val = GetCaptureJson(0);
    displayOutputInfo(1, "Websocket方式拍照......");
    if (_myWs1 == null) {
        _myWs1 = new MyWsConnect(location.port, onTxtCmdMessage, onError);
    }
    _myWs1.SendJson({"function": "grab_image", params: val});
}

// Http方式拍照
function doCaptureHttp() {
    var val = GetCaptureJson(0);
    displayOutputInfo(1, "Http方式拍照......");
    $.ajax({
        type: 'POST',
        url: "/video=grabimage",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify(val),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "Http方式拍照 " + (r.message ? r.message : "请求成功."));
                audioCapture.play();
                // console.log(r);
                for (var i = 0; i < r.images.length; i++) {
                    displayFile(r.images[i].filepath);
                }
            } else {
                displayOutputInfo(3, "Http方式拍照 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// 合并PDF
function mergePDF() {
    var aryFile = getImgFileArray();
    displayOutputInfo(1, "PDF文件合并中......");
    $.ajax({
        type: 'POST',
        url: "/imagepro=merge2pdf",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify(aryFile),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "PDF文件合并中 " + (r.message ? r.message : "请求成功."));
                console.log(r);
                OpenFileByPath(r.filepath);
            } else {
                displayOutputInfo(3, "PDF文件合并中 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

function OpenFileByPath(filePath) {
    if (location.protocol.substr(0,4) == 'http') {
        var pos = filePath.lastIndexOf('\\');
        pos = filePath.lastIndexOf('\\', pos - 1);
        var url = location.origin + '/tmp/' + filePath.substr(pos + 1).replace('\\','/');
        window.open(url);
    }
}

function getImgFileArray() {
    var fileArray = new Array();
    var trs = $('#tabBody tr');
    for (i = 0; i < trs.length; i++) {
        var filePath = trs[i].children[0].getAttribute('p');
        fileArray.push({filepath: filePath});
    }
    return fileArray;
}


var _th = '<tr><th>文件名</th><th>操作</th></tr>';
var _tdOpration = '<td><a onclick="vf()">查看</a> <a onclick="df()">删除</a></td>';
function displayFile(file) {
    var pos = file.lastIndexOf('\\');
    var html = '<tr><td p="' + file + '">' + file.substring(pos + 1) + '</td>' + _tdOpration + '</tr>';
    $("#tabBody").append(html);
}

function vf() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    var file = $(target).parent().prev().attr('p');

    // 把文件转为base64,用于显示查看
    displayOutputInfo(1, "文件转为base64......");
    $.ajax({
        type: 'POST',
        url: "/imagepro=file2base64",
        contentType: 'application/json',
        dataType: "json",
        data: JSON.stringify({filepath: file}),
        success: function (r) {
            if (r.code == "0") {
                displayOutputInfo(2, "文件转为base64 " + (r.message ? r.message : "请求成功."));
                document.getElementById("imgView").src = "data:image/jpeg;base64," + r.base64;
                $('#myModal').modal();
            } else {
                displayOutputInfo(3, "文件转为base64 请求失败: " + r.message);
            }
        },
        error: function (msg) {
            console.log(msg);
        }
    });
}

// 删除拍照文件
function df() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    $(target).parent().parent().remove();
}

// 删除全部拍照文件
function clearImageFile() {
    $("#tabBody").empty();
}

function GetCaptureJson(devNum) {
    var json = {camidx:devNum, make_uuid:$('#fieldCreateUUID').is(':checked') ? 1 : 0,
        image_process_info: {
            cut_type: $("#fieldCrop").val(),
            multi_object: $('#fieldMulti').is(':checked') ? 1 : 0,
            rotate: $("#rotate3").val(),
            fixed_width: $("#fieldWidth").val(),
            fixed_height: $("#fieldHeight").val(),
            split_pag: $('#fieldSplitPage').is(':checked') ? 1 : 0,
            reduce_shadow: $('#fieldReduceShadow').is(':checked') ? 1 : 0,
            color_type: $("#fieldColor").val(),
            remove_black_edge: $('#fieldRemoveBlackEdge').is(':checked') ? 1 : 0,
            rect_correc: $('#fieldRectCorrect').is(':checked') ? 1 : 0,
            correct_idcard_direction: $('#fieldTextDir').is(':checked') ? 1 : 0,
            document_type: $("#fieldDocType").val(),
            // custom_rect: {},
        },
        image_storage_info: {
            xdpi: $("#fieldDPI").val(),
            ydpi: $("#fieldDPI").val(),
            jpg_quality: $("#fieldQuality").val(),
        }
    };
    if ($('#watermark').is(':checked')) {
        json.watermark = {
            pos: $("#wmLayout").val(),
            content: $("#wmText").val(),
            transparency: $("#transparency").val(),
            fontsize: $("#wmSize").val(),
            font: "宋体",
            //color: $("#wmColor").val(),
            color: "darkGray",
        };
    }
    return json;
}

$(function () {
    ReadAppVersion();
    ReadAllCamName();
});

$(window).unload(function () {
    // 关闭页面之前,关闭摄像头,关闭连接
    if (_myWs1 && _myWs1.connected) {
        _myWs1.Close();
        _myWs1 = null;
    }
});
