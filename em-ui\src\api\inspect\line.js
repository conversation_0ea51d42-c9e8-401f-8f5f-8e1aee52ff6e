import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询区域列表
export function listline(query) {
    return request({
      url: '/inspectroute/doQuery',
      method: 'post',
      data: query
    })
}

  // 查询区域详细
export function getline(routeId) {
    return request({
      url: '/inspectroute/' + parseStrEmpty(routeId),
      method: 'get'
    })
}

  // 查询巡检人员列表详细
export function listuser(query) {
    return request({
      url: '/routeuser/doQuery',
      method: 'post',
      data: query
    })
}

// 新增巡检人员
export  function adduser(data) {
  return  request({
    url: '/routeuser/doAdd',
    method: 'post',
    data: data
  })
}

export  function addusers(data) {
  return  request({
    url: '/routeuser/saves',
    method: 'post',
    data: data
  })
}

// 新增临时任务
export function addtemptask(data) {
  return request({
    url: '/inspecttask/doAdd',
    method: 'post',
    data: data
  })
}

// 删除巡检人员
export function deluser(routeUserId) {
  return request({
    url: '/routeuser/doDelete/' + routeUserId,
    method: 'post'
  })
}

  // 查询巡检详细
export function getuser(routeUserId) {
    return request({
      url: '/routeuser/' + parseStrEmpty(routeUserId),
      method: 'get'
    })
}

  //复制路线
export function copyById(id) {
    return request({
      url: '/inspectroute/copyById?id=' + parseStrEmpty(id),
      method: 'get'
    })
}

// 修改巡检人员
export function updateuser(data) {
  return request({
    url: '/routeuser/doEdit',
    method: 'post',
    data: data
  })
}


// 新增区域
export function addline(data) {
    return request({
      url: '/inspectroute/doAdd',
      method: 'post',
      data: data
    })
}

// 修改区域
export function updateline(data) {
    return request({
      url: '/inspectroute/doEdit',
      method: 'post',
      data: data
    })
}


// 删除路线
export function delline(routeId) {
    return request({
      url: '/inspectroute/removeroute/' + routeId,
      method: 'get'
    })
}


  //绑定区域
export function bindSite(data){
    return request({
        url: '/inspectroute/bindSite',
        method: 'post',
        data: data
      })
}

  // 查询巡检计划
export function getplan(routeId) {
    return request({
      url: '/inspectplan/getByRoute/' + parseStrEmpty(routeId),
      method: 'get'
    })
}

// 新增巡检计划
export function addplan(data) {
  return request({
    url: '/inspectplan/doAdd',
    method: 'post',
    data: data
  })
}

// 修改巡检计划
export function updateplan(data) {
  return request({
    url: '/inspectplan/doEdit',
    method: 'post',
    data: data
  })
}