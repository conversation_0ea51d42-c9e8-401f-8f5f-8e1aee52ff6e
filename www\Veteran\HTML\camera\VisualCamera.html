<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Camera</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/zhCN.js"></script>
    <script src="../../JS/camera/camera.js"></script>
    <script src="../../JS/camera/Message.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/camera.css"/>
    <link rel="stylesheet" href="../../CSS/camera/Message.css"/>
</head>
<body>
    <table class="ifram">
        <tr>
            <td class="left" valign="top">
                <div class="left-one">
                    <div class="left-one-left">
                        <img id="cameraImage" src="../../image/camera.png"/>
                    </div>
                    <div class="left-one-right">
                        <span>基本功能演示</span>
                    </div>
                </div>
                <div class="left-two">
                    <div>
                        <label class="left-two-lable">设备名称:</label>
                        <select id="cameraInfo" class="left-two-select" onchange="switchCameraInfo_Fun()">
                            <option value="0">素材</option>
                            <option value="1">人像</option>
                        </select><br>
                        <input type="checkbox" value="0" checked id="chkLiveCheck"><label for="chkLiveCheck">压缩</label><br>
                        <label class="left-two-lable">结果显示:</label>
                        <textarea id="resultArea"></textarea>
                    </div>
                </div>
            </td>
            <td class="mindle">
                <div class="mindle-top">
                    <div class="mindle-top-img"> 
                        <img id="pic"/>
                    </div>
                    <div id="vertical-line" class="mindle-top-line" hidden>
                    </div>
                </div>
                <div class="mindle-botom" >
                    <button id="RecogBarCode" onclick="readIdCard()">读取证件</button>
                    <button id="CaptureBase64" onclick="cameraCapture_Fun()">拍照</button>
                    <button id="clearImage" onclick="clearImageFile()">清空照片</button>
                </div>
            </td>
            <td class="right">
                <div class="right-div" id="right_div">
                    <table>
                        <tbody id="image_data" align="center"></tbody>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
