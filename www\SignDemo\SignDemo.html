<!doctype html>
<html lang="zh-CN" xmlns="http://www.w3.org/1999/html" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>高拍仪演示 - CamSDK
    </title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../JS/common/html5shiv.min.js"></script>
    <script src="../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">CamSDK 演示<span id="verInfo">Ver: 20220926</span></a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li class="active"><a href="#">签字演示</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/camera.jpg">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>签字演示</h1>
        <p>这个页面演示了如何设置签字页面、获取签字等。</p>
    </div>
</div>

<div class="row">
    <div class="col-md-10" style="text-align: center">
        <div class="row">

            <button type="button" class="btn btn-primary" onclick="ZCStartDeviceAB()" title="启动签字设备">启动签字设备</button>
            <button type="button" class="btn btn-primary" onclick="ZCStopDeviceAB()" title="停止设备">停止设备</button>
            </p>

            </p>
            <button type="button" class="btn btn-primary" onclick="ZCShowHtmlAB()" title="推送网页">推送网页</button>
            <label class="fieldItem">网页URL:
                <input type="text" id="signURL" style="width:500px;" value="https://y.qq.com/">
            </label>
            </p>
            <label class="fieldItem">x:
                <input type="number" id="signx" min="0" max="10000" style="width:60px;" value="100">
            </label>
            <label class="fieldItem">y:
                <input type="number" id="signy"  min="0" max="10000" style="width:60px;" value="100">
            </label>
            <label class="fieldItem">width:
                <input type="number" id="signwidth" min="0" max="10000" style="width:60px;" value="1200">
            </label>
            <label class="fieldItem">height:
                <input type="number" id="signheight" min="0" max="10000" style="width:60px;" value="600">
            </label>
            <button type="button" class="btn btn-primary" onclick="ZCBeginSignAB()" title="进入签名模式">进入签名模式</button>
            <button type="button" class="btn btn-primary" onclick="ZCEndSignAB()" title="退出签名模式">退出签名模式</button>

            </p>
            <button type="button" class="btn btn-primary" onclick="ZCOutputImageBase64AB()" title="获取签名">获取签名</button>
            <button type="button" class="btn btn-primary" onclick="ZCGetSerialNumAB()" title="签名板序列号">签名板序列号</button>
            <button type="button" class="btn btn-primary" onclick="ZCSetModeAB()" title="设置笔的模式">设置笔的模式</button>
            <select id="penMode" onchange="iCamOpen()">
                <option value="0">毛笔</option>
                <option value="1">签字笔</option>
                <option value="2">定宽</option>
            </select>

        </div>
        </br></br>
        <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none;">
        </div>

        </br></br></br>
        <div class="row">
            <label>签名图片:</label>
            <table>
                <tbody id="image_data" align="center"></tbody>
            </table>
            <!/div-->
        </div>

    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../bootstrap/js/bootstrap.min.js"></script>
<script src="JS/Sign.js"></script>
</body>
</html>