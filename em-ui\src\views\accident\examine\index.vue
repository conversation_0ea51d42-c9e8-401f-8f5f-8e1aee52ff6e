<template>
  <div class="app-container">

    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="事故单位" prop="orgId">
           <treeselect v-model="queryParams.accidentOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择事故发生单位" style="width:200px"/>
      </el-form-item>
      <el-form-item label="事故地点" prop="accidentaddress">
             <el-select v-model="queryParams.accidentaddress" placeholder="请选择事故地点" clearable>
                  <el-option v-for="dict in dict.type.system_accsite" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
        </el-form-item>
       <el-form-item label="事故时间" prop="accidenttime">
           <el-date-picker v-model="queryParams.gtdate" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
          -
          <el-date-picker v-model="queryParams.ltdate" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
      
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="handList" @selection-change="handleSelectionChange">
      <el-table-column label="上报人" align="center" prop="worker.workerName" :show-overflow-tooltip="true"/>
      <el-table-column label="是否调查" align="center"  prop="accidentexamine" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.accidentexamine===undefined">未调查</span>
          <span v-if="scope.row.accidentexamine!==undefined && scope.row.accidentexamine.isarchive">已归档</span>
          <span v-else-if="scope.row.accidentexamine!==undefined">已调查</span>
          
        </template>
      </el-table-column>
      <el-table-column label="所属组织" align="center" prop="organization.organName" />
      <el-table-column label="上报日期" align="center" prop="creationtime" width="100">
        <template slot-scope="scope">
         <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="事故地点" align="center"  prop="accidentaddress" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.system_accsite" :value="scope.row.accidentaddress"/>
        </template>
      </el-table-column>
      
     <el-table-column label="事故时间" align="center" prop="accidenttime" />
     <el-table-column label="事故单位" align="center" prop="accidentOrg.organName" />
     <el-table-column label="当事人" align="center" prop="clientUser" />
     <el-table-column label="相关人" align="center" prop="relatedUser" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
         
          >事故简述</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleExamine(scope.row)" >事故调查</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="980px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
            <el-form-item label="所属部门" prop="orgId">
              <el-select v-model="form.orgId" placeholder="请选择部门" clearable>
                  <el-option v-for="dict in userorgList" :key="dict.organId" :label="dict.organization.organName" :value="dict.organId"/>
              </el-select>
            </el-form-item>
        </el-row>
         <el-form-item label="事故发生部门" prop="accidentOrgId">
              <treeselect v-model="form.accidentOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择事故发生部门" />
        </el-form-item>
        <el-form-item label="事故分类" prop="accidentclassId">
             <el-select v-model="form.accidentclassId" placeholder="请选择事故分类" clearable>
                  <el-option v-for="dict in dict.type.system_accclass" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
        </el-form-item>
        <el-form-item label="事故地点" prop="accidentaddress">
             <el-select v-model="form.accidentaddress" placeholder="请选择事故地点" clearable>
                  <el-option v-for="dict in dict.type.system_accsite" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
        </el-form-item>
        <el-form-item label="事故类型" prop="accidenttypeId">
             <el-select v-model="form.accidenttypeId" placeholder="请选择事故类型" clearable>
                  <el-option v-for="dict in dict.type.system_acctype" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
        </el-form-item>
        <el-form-item label="事故至因物" prop="accidentcause">
             <el-select v-model="form.accidentcause" placeholder="请选择事故至因物" clearable>
                  <el-option v-for="dict in dict.type.system_acclead" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
        </el-form-item>
         <el-form-item label="事故时间" prop="accidenttime">
          <el-date-picker v-model="form.accidenttime" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
       <el-form-item label="目击证人" prop="witness">
          <el-input v-model="form.witness" placeholder="请输入目击证人" />
        </el-form-item>
         <el-form-item label="当班班长" prop="dutymonitor">
          <el-input v-model="form.dutymonitor" placeholder="请输入当班班长" />
        </el-form-item>
         <el-form-item label="车间主任" prop="workshopheader">
          <el-input v-model="form.workshopheader" placeholder="请输入车间主任" />
        </el-form-item>
        <el-row>
            <el-form-item label="事故过程描述"  prop="course">
               <el-input  type="textarea" v-model="form.course" placeholder="请输入事故过程描述" maxlength="1000"/>
            </el-form-item>
        </el-row>
        <el-row>
            <el-form-item label="事故原因分析"  prop="causeAnal">
               <el-input  type="textarea" v-model="form.causeAnal" placeholder="请输入事故原因分析" maxlength="1000"/>
            </el-form-item>
        </el-row>
        <el-row>
            <el-form-item label="责任划分"  prop="dutyClassify">
               <el-input  type="textarea" v-model="form.dutyClassify" placeholder="请输入责任划分" maxlength="1000"/>
            </el-form-item>
        </el-row>
        <el-row>
            <el-form-item label="人员伤害及经济损失情况"  prop="hurtLoss">
               <el-input  type="textarea" v-model="form.hurtLoss" placeholder="请输入责任划分" maxlength="1000"/>
            </el-form-item>
        </el-row>
        <el-row>
        <el-form-item label="已采取的措施"  prop="measure">
               <el-input  type="textarea" v-model="form.measure" placeholder="请输入责任划分" maxlength="1000"/>
            </el-form-item>
        </el-row>
        <el-row>
        <el-form-item label="整改措施"  prop="correction">
               <el-input  type="textarea" v-model="form.correction" placeholder="请输入整改措施" maxlength="1000"/>
        </el-form-item>
        </el-row>
        <el-row>
        <el-form-item label="验证"  prop="verifyInfo">
               <el-input  type="textarea" v-model="form.verifyInfo" placeholder="请输入验证" maxlength="1000"/>
        </el-form-item>
        </el-row>
        <el-form-item label="相关资料" prop="infofilepaths">
            <file-upload v-model="infofilepaths" :limit="3"  />
        </el-form-item>
        <el-form-item label="调查报告" prop="reportfilepaths">
            <file-upload v-model="reportfilepaths" :limit="3"  />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm" v-if="sureview">确 定</el-button>
        <el-button @click="cancel" >取 消</el-button>
         <el-button type="success" @click="submitArchive" v-if="archiveview">归 档</el-button>
      </div>
    </el-dialog>

     <el-dialog :title="title" :visible.sync="viewopen" width="780px" append-to-body>
      <el-form ref="form" :model="form"  label-width="80px">
            <el-form-item label="事故简述"  >
              {{form.briefcourse}}
            </el-form-item>
      </el-form>
     </el-dialog>
  </div>
</template>

<script>
import { listHand,loadExamine,addHand,addExamine,updateExamine,getHand } from "@/api/accident/accident";
import { getOrgByWork} from "@/api/system/user";
import {listDeptExcludeChild } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "AccidentHand",
  components: { Treeselect },
  dicts: ['system_accsite','system_acctype','system_acclead','system_accclass'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
       // 部门树选项
      deptOptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 上报数据
      handList: [],
      //用户组织
      userorgList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示查看弹出层
      viewopen:false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      //资料文档路径
      infofilepaths:[],
      //报告文档路径
      reportfilepaths:[],
      //按钮是否显示
      sureview:true,
      archiveview:true,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        orgId: [
          { required: true, message: "部门不能为空", trigger: "change" }
        ],
        accidentOrgId: [
          { required: true, message: "部门不能为空", trigger: "change" }
        ],
        accidentaddress: [
          { required: true, message: "地点不能为空", trigger: "change" }
        ],
        accidenttime: [
          { required: true, message: "时间不能为空", trigger: "change" }
        ],
        accidenttypeId: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ],
        accidentclassId: [
          { required: true, message: "分类不能为空", trigger: "change" }
        ],
        accidentcause: [
          { required: true, message: "至因物不能为空", trigger: "change" }
        ],
        course: [
          { required: true, message: "描述不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {

    this.getList();
    this.getuserorglist();
    this.listDept();
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      this.queryParams.ishand=true
      this.queryParams.orderBy="creationtime desc"
      listHand(this.queryParams).then(response => {
        this.handList = response.data;
        this.total = response.totalCount;
        this.loading = false;
      });
    },
        //查询用户组织
    getuserorglist(){
        getOrgByWork().then(response => {
          this.userorgList = response.data
          console.log(JSON.stringify(this.userorgList))
        })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        accidentexamineId: undefined,
        accidenthandId: undefined,
        orgId: undefined,
        accidenttime: undefined,
        accidentaddress: undefined,
        accidenttypeId: undefined,
        accidentclassId:undefined,
        accidentcause: undefined,
        course: undefined,
        causeAnal: undefined,
        dutyClassify: undefined,
        hurtLoss: undefined,
        measure: undefined,
        correction: undefined,
        verifyInfo: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if(this.queryParams.accidentOrgId == ''){
        this.queryParams.accidentOrgId=undefined
      }
      if(this.queryParams.accidentaddress == ''){
        this.queryParams.accidentaddress=undefined
      }
      if(this.queryParams.accidenttime == ''){
        this.queryParams.accidenttime=undefined
      }
      if(this.queryParams.gtdate == '' || this.queryParams.gtdate===null){
        this.queryParams.gtdate=undefined
      }
      if(this.queryParams.ltdate == '' || this.queryParams.ltdate===null){
        this.queryParams.ltdate=undefined
      }
      
      if(this.queryParams.gtdate != undefined){
        if(this.queryParams.ltdate == undefined ){
           this.$modal.msgError("时间范围不正确");
           return
        }
      }
      if(this.queryParams.ltdate != undefined){
        if(this.queryParams.gtdate == undefined ){
           this.$modal.msgError("时间范围不正确");
           return
        }
      }
      this.getList();
    },
        /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cmsId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },

    /** 查看按钮操作 */
    handleView(row) {

       this.form = row;
        this.viewopen = true;
        this.title = "查看";
    },
    //事故调查
    handleExamine(row){
       const accidenthandId = row.accidenthandId || this.ids
       this.sureview=true
       this.archiveview=true
       this.infofilepaths=[]
       this.reportfilepaths=[]
       this.form = {};
       loadExamine(accidenthandId).then(response=>{
          if(typeof(response.data) != "undefined"){
            this.form=response.data
            if(typeof(this.form.infofilepaths)!="undefined"){
              for(let i=0; i<this.form.infofilepaths.length; i++){
                 let filepath = {}
                filepath.name=this.form.infofilepaths[i].fileName
                filepath.url=this.form.infofilepaths[i].filePath
                this.infofilepaths.push(filepath);
              }
            }
            if(typeof(this.form.reportfilepaths)!="undefined"){
              for(let i=0; i<this.form.reportfilepaths.length; i++){
                let filepath = {};
                filepath.name=this.form.reportfilepaths[i].fileName
                filepath.url=this.form.reportfilepaths[i].filePath
                this.reportfilepaths.push(filepath);
              }
            }
            if(typeof(this.form.isarchive)!="undefined" && this.form.isarchive == true){
               this.sureview=false
               this.archiveview=false
            }
          }else{
            this.archiveview=false
            console.log('1111')
           getHand(accidenthandId).then(res => {
              console.log(res.data)
              this.form = res.data
              // this.form.orgId=accident.orgId
              // this.form.accidenttime=accident.accidenttime
              // console.log(this.form.orgId)
           })
          }
       })
      //  let queryparams1 = {};
      //  listDeptExcludeChild(queryparams1).then(response => {
      //     let datas = response.data;
      //     for(let i=0; i<datas.length; i++){
      //       datas[i].children=[];
      //     }
      //     this.deptOptions = this.handleTree(datas, "organId");
      //   });
       this.form.accidenthandId=accidenthandId
       this.open=true;
       this.title="事故调查"
    },
    listDept(){
      let queryparams1 = {};
      listDeptExcludeChild(queryparams1).then(response => {
          let datas = response.data;
          for(let i=0; i<datas.length; i++){
            datas[i].children=[];
          }
          this.deptOptions = this.handleTree(datas, "organId");
        });
    },
    submitArchive:function(){
        this.$refs["form"].validate(valid => {
        if (valid) {

          if(typeof(this.infofilepaths) !=="undefined" && this.infofilepaths.length > 0){
             this.form.infofilepaths=[]
            for(let i=0; i<this.infofilepaths.length; i++){
              let filepath = {};
              filepath.filePath=this.infofilepaths[i].url;
              filepath.fileName=this.infofilepaths[i].name;
              this.form.infofilepaths.push(filepath);
            }
          }
          if(typeof(this.reportfilepaths) !=="undefined" && this.reportfilepaths.length > 0){
             this.form.reportfilepaths=[]
            for(let i=0; i<this.reportfilepaths.length; i++){
              let filepath = {};
              filepath.filePath=this.reportfilepaths[i].url;
              filepath.fileName=this.reportfilepaths[i].name;
              this.form.reportfilepaths.push(filepath);
            }
          }

         
          if (this.form.accidentexamineId != undefined) {
            this.form.isarchive=true;
            updateExamine(this.form).then(response => {
              this.$modal.msgSuccess("归档成功");
              this.open = false;
              this.getList();
            });
          } 
        }
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(typeof(this.infofilepaths) !=="undefined" && this.infofilepaths.length > 0){
             this.form.infofilepaths=[]
            for(let i=0; i<this.infofilepaths.length; i++){
              let filepath = {};
              filepath.filePath=this.infofilepaths[i].url;
              filepath.fileName=this.infofilepaths[i].name;
              this.form.infofilepaths.push(filepath);
            }
          }
          if(typeof(this.reportfilepaths) !=="undefined" && this.reportfilepaths.length > 0){
             this.form.reportfilepaths=[]
            for(let i=0; i<this.reportfilepaths.length; i++){
              let filepath = {};
              filepath.filePath=this.reportfilepaths[i].url;
              filepath.fileName=this.reportfilepaths[i].name;
              this.form.reportfilepaths.push(filepath);
            }
          }
          if (this.form.accidentexamineId != undefined) {
            updateExamine(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExamine(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const cmsId = row.cmsId || this.ids
      this.$modal.confirm('是否确认删除资讯？').then(function() {
        return delCms(cmsId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
