<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Capture the page from book</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/en.js"></script>
    <script src="../../JS/camera/CaptureBook_en.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/captureBook.css"/>
</head>
<body>
    <table class="fullTable">
        <tr height="500">
            <td id="td1" class="left" valign="top" align="center" rowspan="2">
                <div class="left-one">
                    <div class="left-one-left">
                        <img id="cameraImage" src="../../image/camera.png"/>
                    </div>
                    <div class="left-one-right">
                        <span>Book</span>
                    </div>
                </div>
                <div class="left-two">
                    <div>
                        <label class="left-two-lable">Device:</label>
                        <select id="cameraInfo" class="left-two-select" onchange="switchCameraInfo_Fun()"></select>
                        <br>
                        <label class="left-two-lable">Video compression:</label>
                        <select id="mediaType" class="left-two-select" onchange="setCameraInfo_Fun()"></select>
                        <br>
                        <label class="left-two-lable">Resolution:</label>
                        <select id="resolution" class="left-two-select" onchange="setCameraInfo_Fun()"></select>
                    </div>
                    <br><br>
                    <button id="btnCaptureBook" onclick="captureBook()">Capture</button>
                    <br><br>
                    <label title="Detection of image changes, automatic continuous capture">
                        <input id="autoCapture" type="checkbox" value="0" onchange="OnAutoCaptureChange()"/>Auto continuous capture
                    </label>
                    <br><br>
                    <label>The continuous capture speed: <span id="speedNum">6</span>
                        <input id="speed" type="range" min="4" max="12" value="6" onchange="onSpeedChange()"/>
                    </label>
                </div>
                <p>Operation guidance:</p>
                <p>Align the middle seam of the book with the green line</p>
                <img id="tutorial" src="../../image/BookAnimatedTutorial.gif" width="250"/>
                <audio id="audioCapture">
                    <source src="../../audio/Capture.wav" type="audio/wav">
                </audio>
            </td>
            <td class="middle">
                <img id="pic"/>
                <div id="vertical-line" style="display: none" ></div>
            </td>
            <td class="right" valign="top" rowspan="2">
                <label class="left-two-lable" style="width: 110px;">Photo list:</label>
                <button id="btnMergePDF" onclick="mergePDF()">Merge PDF</button>
                <button id="btnClearFile" onclick="clearImageFile()">Clear</button>
                <table id="tbFile"><tr><th>File name</th><th>Operation</th></tr></table>
            </td>
        </tr>
        <tr>
            <td id="td4" class="bottom" valign="top" >
                <div>Left page<br><img id="imgL"></div>
                <div>Right page<br><img id="imgR"></div>
            </td>
        </tr>
    </table>
    <div id="divView" style="display:none;">
        <div>
            <button onclick="closeView()">Close</button>
        </div>
        <img id="imgView">
    </div>
</body>
</html>
