﻿
//开启webSocket
function StartWebSocket(){
	var url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
	if ('WebSocket' in window) {
        ws = new WebSocket(url);
    } else if ('MozWebSocket' in window){
        ws = new MozWebSocket(url);
    } else {
		alert(Lang.WS.lowVer);
	}
   
	ws.onopen = function() {
      g_bConnectWS = true;
	  sendInitMsg();//初始化
	  g_bClosed = false;
   };
	
   ws.onmessage = function (evt) {
   	if(typeof(evt.data)=="string"){
   		
   		var str = evt.data;
   		
   		if(str.length <= 0){
   			
   			return;
   		}

		if(str.indexOf("@|@") > 0)
		{
			initDev(str);
			return ;
		}
		
		//读取身份证显示结果
		if(str.indexOf("@I@") >= 0)
		{
			var strs= new Array();
			strs=str.split("@I@");
			alert(strs[1]);
			
			return ;
		}


		//条码识别结果
		if(str.indexOf("@ReadBarcode@") >= 0)
		{
			var strs= new Array();
			strs=str.split("@ReadBarcode@");
			alert(strs[1]);
			
			return ;
		}

		//图片转base64结果
		if(str.indexOf("@Img2Base64@") >= 0)
		{
			var strs= new Array();
			strs=str.split("@Img2Base64@");
			alert(strs[1]);
			
			return ;
		}

		//通过pid和vid获取设备索引的返回值
		if(str.indexOf("@GetCamIndexByPID@") >= 0)
		{
			var strs= new Array();
			strs=str.split("@GetCamIndexByPID@");
			alert(strs[1]);
			
			return ;
		}





		//上传返回值
		if(str.indexOf("@Cap2HttpServer@") >= 0)
		{
			var strs= new Array();
			strs=str.split("@Cap2HttpServer@");
			alert(Lang.main.upload + strs[1]);
			return ;
		}

		//拍照为Base64返回值
		if(str.indexOf("@Cap2Base64@") >= 0)
		{
			var strs= new Array();
			strs=str.split("@Cap2Base64@");
			alert(Lang.main.base64 + strs[1]);
			return ;
		}

		//识别银行卡返回值
		if(str.indexOf("@OCRBankCard@") >= 0)
		{
			var strs= new Array();
			strs=str.split("@OCRBankCard@");
			alert(strs[1]);
			
			return ;
		}
		
		if(str.indexOf(g_splitFlag)>=0){
			//视频帧
			var strs= new Array();
			strs=str.split(g_splitFlag); 
			setImageWithBase64(strs[1]);
		}else{

		}
   	}
 	};
	
   ws.onclose = function() {
      g_bConnectWS = false;
      alert(Lang.main.notOpen);
   };
}

function sendWsMessage(jsonObj){
	var jsonStr = JSON.stringify(jsonObj);
	ws.send(jsonStr);
}




function load(){
	//连接服务
	StartWebSocket();
	
}

function unload(){
	alert(Lang.main.disconnect);
	if (g_bClosed){
		return;
	}
	clearInterval();
	//反初始化
	var jsonObj = {FuncName:'UnInitCamLib'};
	sendWsMessage(jsonObj);
}

//初始化
function sendInitMsg(){
	var jsonObj = {FuncName:'InitCamLib'};
	sendWsMessage(jsonObj);
}


//显示视频帧
function setImageWithBase64(str){

	var myimg = document.getElementById("myCanvas"); 
	myimg.src = "data:image/png;base64,"+str;
	
}



//拍照
function Capture(){
	
	var filepath = document.getElementById("saveText").value;
	var jsonObj = {FuncName:'camCaptureImageFile',argument:{filePath:filepath}};
	sendWsMessage(jsonObj);
	
}


function addoptionDev(s)   
{
	var obj=document.getElementById("curDev"); 
	var opt = new Option(s, obj.length ); 
	obj.options.add(opt);   
}

function initDev(str)
{
	//设备名称
	var strs = new Array();
	strs = str.split("@|@");

	for(var i = 0; i < strs.length; i++)
	{
		if(strs[i].length > 0)
		{
			addoptionDev(window.atob(strs[i]));
		}
	}
	document.getElementById("curDev").value = 0;
}

function changedev()
{
	var obj=document.getElementById("curDev"); 
	
	var idx = obj.selectedIndex;    
	if(idx != g_index)
	{
		stop_preview();
		g_index = idx;
		start_preview();
	}
}

function start_preview()     
{   
    var jsonObj = {FuncName:'StartPreview', index:g_index};
	sendWsMessage(jsonObj);
}   

function stop_preview()
{
	var jsonObj = {FuncName:'StopPreview', index:g_index};
	sendWsMessage(jsonObj);
}

var num = 1;
function FormatNum(Source){ 
	var strTemp=""; 
	var len = 5;
	if(Source < 10)
	{
		len = 4;
	}
	else if(Source < 100)
	{
		len = 3;
	}
	else if(Source < 1000)
	{
		len = 2;
	}
	for(i=0; i < len;i++)
	{ 
		strTemp += "0"; 
	} 
	return strTemp+Source; 
} 


function capture()
{
	var val = num;
	var filename = FormatNum(val);
	var ext = "jpg";
	var path="D:\\Img"+filename +"."+ext;             //get_name()
	num++;
	var jsonObj = {FuncName:'DoCapture', index:g_index, filepath:path};
	sendWsMessage(jsonObj);
}

//纠偏裁边
function RotateCrop(obj)
{
	if(obj.checked)
	{
		var jsonObj = {FuncName:'SetAutoCrop', index:g_index, flag:1};
		sendWsMessage(jsonObj);
	}
	else
	{
		var jsonObj = {FuncName:'SetAutoCrop', index:g_index, flag:0};
		sendWsMessage(jsonObj);
	}
}


function readidcard()
{
	var jsonObj = {FuncName:'ReadIDCard'};
	sendWsMessage(jsonObj);
}

function setJpgQuality(idx, val)
{
	var jsonObj = {FuncName:'SetJPGQuality', index:idx, flag:val};
	sendWsMessage(jsonObj);
}

function cap2HttpServer(idx, url)
{
	var jsonObj = {FuncName:'Cap2HttpServer', index:idx, flag:'"'+url+'"'};
	sendWsMessage(jsonObj);
}


function readBarcode()
{
	var jsonObj = {FuncName:'ReadBarcode', index:g_index};
	sendWsMessage(jsonObj);
}

function image2Base64(str)
{
	var jsonObj = {FuncName:'Image2Base64', index:str};
	sendWsMessage(jsonObj);
}

function getcamidx(str)
{
	var jsonObj = {FuncName:'GetCamIndexByPID', index:str};
	sendWsMessage(jsonObj);
}



function setVideoColor(idx, val)
{
	var jsonObj = {FuncName:'VideoColor', index:idx, flag:val};
	sendWsMessage(jsonObj);
}

function cap2base64(idx)
{
	var jsonObj = {FuncName:'Cap2Base64', index:idx};
	sendWsMessage(jsonObj);
}

//放大
function zoomin()
{
	var obj1=document.getElementById("curDev"); 
	var idx = obj1.selectedIndex;    
	var jsonObj = {FuncName:'VideoZoom', index:idx, flag:1};
	sendWsMessage(jsonObj);
}

//缩小
function zoomout()
{
	var obj1=document.getElementById("curDev"); 
	var idx = obj1.selectedIndex;    
	var jsonObj = {FuncName:'VideoZoom', index:idx, flag:-1};
	sendWsMessage(jsonObj);
}

//左转
function rotleft()
{
	var obj1=document.getElementById("curDev"); 
	var idx = obj1.selectedIndex;    
	var jsonObj = {FuncName:'VideoRotate', index:idx, flag:1};
	sendWsMessage(jsonObj);
}


//左转
function rotright()
{
	var obj1=document.getElementById("curDev"); 
	var idx = obj1.selectedIndex;    
	var jsonObj = {FuncName:'VideoRotate', index:idx, flag:2};
	sendWsMessage(jsonObj);
}

//删除指定目录下所有图片
function deleteImages(str)
{
	var jsonObj = {FuncName:'DeleteImages', index:str};
	sendWsMessage(jsonObj);
}

//显示指定图片
function openImage(str)
{
	var jsonObj = {FuncName:'ShowImage', index:str};
	sendWsMessage(jsonObj);
}

function ocrbankcard()
{
	var jsonObj = {FuncName:'OCRBankCard'};
	sendWsMessage(jsonObj);	
}
