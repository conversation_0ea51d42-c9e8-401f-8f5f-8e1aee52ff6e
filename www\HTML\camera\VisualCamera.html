<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Camera</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/zhCN.js"></script>
    <script src="../../JS/cropBox.js"></script>
    <script src="../../JS/camera/Message.js"></script>
    <script src="../../JS/camera/camera.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/Message.css"/>
    <link rel="stylesheet" href="../../CSS/cropBox.css"/>
    <link rel="stylesheet" href="../../CSS/camera/camera.css"/>
</head>
<body>
    <table class="ifram">
        <tr>
            <td class="left" valign="top">
                <div class="left-one">
                    <div class="left-one-left">
                        <img id="cameraImage" src="../../image/camera.png"/>
                    </div>
                    <div class="left-one-right">
                        <span>基本功能演示</span>
                    </div>
                </div>
                <div class="left-two">
                    <div>
                        <label class="left-two-lable">设备名称:</label>
                        <select id="cameraInfo" class="left-two-select" onchange="switchCameraInfo_Fun()"></select><br>
                        <!--<label class="left-two-lable">文件格式:</label>
                        <select id="file_format" class="left-two-select">
                            <option value="0">jpg</option>
                            <option value="1">tif</option>
                            <option value="2">png</option>
                            <option value="3">bmp</option>
                        </select><br>-->
                        <label class="left-two-lable">视频格式:</label>
                        <select id="mediaType" class="left-two-select" onchange="setCameraInfo_Fun()"></select><br>
                        <label class="left-two-lable">分辨率:</label>
                        <select id="resolution" class="left-two-select" onchange="setCameraInfo_Fun()"></select><br>
                        <label class="left-two-lable">旋转方式:</label>
                        <select id="rotateType" class="left-two-select" onchange="OnRotateChange()">
                            <option value="0">不旋转</option>
                            <option value="90">旋转90度</option>
                            <option value="180">旋转180度</option>
                            <option value="270">旋转270度</option>
                            <option value="361" id="opOcrDirection" disabled>自动文字方向</option>
                        </select><br>
                        <div style="display: none">
                        <label class="left-two-lable">OCR语言:</label>
                        <select id="language" class="left-two-select" onchange="setCameraInfo_Fun()"></select><br>
                        </div>
                        <label class="left-two-lable">裁切方式:</label>
                        <select id="crop_type" class="left-two-select" onchange="setCameraImageInfo_Fun()">
                            <option value="0">不裁切</option>0-
                            <option value="1">单图裁切</option>
                            <option value="2">多图裁切</option>
                            <!--<option value="3">曲面矫正</option>-->
                            <option value="6">桌面裁切</option>
                            <option value="9">自定义裁切</option>
                        </select><br>
                        <label class="left-two-lable">图像效果:</label>
                        <select id="image_type" class="left-two-select">
                            <option value="0">彩色原色</option>
                            <option value="1">灰度图片</option>
                            <option value="2">黑白文档</option>
                            <option value="3">彩色文档</option>
                            <option value="4">红印文档</option>	
                            <option value="5">蓝印文档</option>
                            <option value="6">彩色照片</option>
                            <option value="7">票据增强</option>
                        </select><br>
                        <label class="left-two-lable">补边:</label>
                        <select id="fill_border" class="left-two-select">
                            <option value="0">不填充</option>
                            <option value="1">映射填充</option>
                            <option value="2">白色填充</option>
                            <option value="3">自动纯色填充</option>
                        </select><br>
                        <label class="left-two-lable">异物去除:</label>
                        <select id="foreign_remove" class="left-two-select">
                            <option value="0">无</option>
                            <!--<option value="1">去除手指(拍书时)</option>-->
                            <option value="2">去除装订孔</option>
                            <!--<option value="3">去除装订方孔</option>-->
                        </select>
                        <label class="left-two-lable">USB按键:</label>
                        <select id="selExtButton" class="left-two-select" onchange="OnExtButtonChange()">
                            <option value="None" selected>不启用</option>
                            <option value="Notify">发通知</option>
                            <option value="CameraCapture">触发拍照</option>
                            <option value="CameraCaptureBook" disabled title="在此页面中不适用">触发拍书</option>
                            <option value="RecogBarCode">触发条码识别</option>
                        </select>
                        <label class="left-two-lable">曝光:</label>
                        <input id="export" class="left-two-input" type="range" value="0" max="0" min="0" onchange="SetVideoParam()"><br />
                        <input type="checkbox" checked id="expCheck" onchange="SetVideoParam()"><label for="expCheck">自动曝光</label><br>
                        <label class="left-two-lable">DPI:</label>
                        <input id="dpi"  type="text" style="width:100px;" value="0">
                        <label class="left-two-lable">结果显示:</label>
                        <textarea id="resultArea"></textarea>
                    </div>
                </div>
            </td>
            <td class="mindle">
                <div class="mindle-top">
                    <div class="mindle-top-img">
                        <img id="pic"/>
                    </div>
                    <div id="vertical-line" class="mindle-top-line" hidden>
                    </div>
                </div>
                <div class="mindle-botom" >
                    <button id="RecogBarCode" onclick="recogBarCode_Fun()">条码识别</button>
                    <button id="CaptureBase64" onclick="cameraCapture_Fun()">拍照</button>
                    <button id="strRecognition" style="margin-right:10px" onclick="layoutRecognition()">版面识别</button>
                    证件照背景色:<input type="color" value="#FF0000" id="bgColor">
                    <button id="CaptureIdPhone" onclick="cameraCaptureIdPhone()" title="对视频中的人像自动拍照成为证件照片(358*441像素)">拍证件照</button>
                    <button id="clearImage" onclick="clearImageFile()">清空照片</button>
                </div>
            </td>
            <td class="right">
                <div class="right-div" id="right_div">
                    <table>
                        <tbody id="image_data" align="center"></tbody>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
