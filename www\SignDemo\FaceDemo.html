<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>双目演示</title>


	<!-- Bootstrap -->
	<link rel="stylesheet" href="bootstrap/css/bootstrap.min.css">
	<link rel="stylesheet" href="CSS/common.css">

</head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<body onload ="InitCamera();" onunload = "UnInitCamera();">
<p>
<div class="row">
	<div class="col-md-5" style="text-align: center">
		<div class="row">
			<div id="videoPreviewDiv1">
				<img id="videoPreview" style = "width:400px; height:300px; text-align:center">
				<p>
			</div>
        </div>
	</div>
	<div class="col-md-5" style="text-align: left">
        <div class="row">
			<input type = "button" value = "打开双目" onClick="OpenDualFaceDevice()" style = "width:120px; text-align:center">
			<input type = "button" value = "关闭双目" onClick="CloseDualFaceDevice()" style = "width:120px; text-align:center">
			<input type = "button" value = "获取活体检测结果" onClick="GetFaceLivingBodyStatus()" style = "width:120px; text-align:center">
			<input type = "button" value = "获取人脸图片" onClick="GetFaceImageFromCam()" style = "width:120px; text-align:center">
			<p>
			<label>base64图片列表:</label>
			<table>
				<tbody id="image_data" align="center"></tbody>
			</table>
		</div>
	</div>
</div>
<script src="jquery-2.0.3.min.js"></script>
<script src="bootstrap/js/bootstrap.min.js"></script>
</body>
</html>
<script type="text/javascript">



var FileBase64;
//初始化，页面加载时执行
function InitCamera() 
{
	var VideoImage  = document.getElementById("videoPreview");
	var VideoImage  = document.getElementById("videoPreview");
	// VideoImage.style.width = "800px";
    // VideoImage.style.height = "600px";
	StartWebSocket();
 }
 

  //H5-开启webSocket
function StartWebSocket()
{
	 var url = "ws://localhost:12345/";

	if('WebSocket' in window){
            ws = new WebSocket(url);
    }
    else if('MozWebSocket' in window){
        ws = new MozWebSocket(url);
    }else{
		alert("浏览器版本过低，请升级您的浏览器。\r\n浏览器要求：IE10+/Chrome14+/FireFox7+/Opera11+");
	}
   
	ws.onopen = function()
   {
	  InitCameraByServer();//初始化
   };
	
	//OnMessage
   ws.onmessage = function (evt) 
   {
		if(typeof(evt.data)=="string"){

			var str = evt.data;

			if(str.length <= 0){

				return;
			}

			handleJsonStrMessage(str);
		}
 	};
	
}

//H5-HandsonStrMessage，接收server端的Message，并且解析。
function handleJsonStrMessage(str)
{
	var jsonOBJ = JSON.parse(str);
	var name = jsonOBJ.functionName;
	var re = jsonOBJ.errorMsg;
	if(name =="iCamGetCameraImage")
	{
		setImageWithBase64(jsonOBJ.imgBase64Str);
	}
	else if(name =="OpenDualFaceDevice")
	{
		if( re!=undefined)
		{
			alert("OpenDualFaceDevice失败："+re);
			return;
		}

	}
	else if(name =="CloseDualFaceDevice")
	{
		if( re!=undefined)
		{
			alert("CloseDualFaceDevice失败:"+re);
		}
	}
	else if(name =="GetFaceLivingBodyStatus")
	{
		if( re!=undefined)
		{
			alert("活体检测失败："+re);
		}
		else{
			alert("活体检测成功");
		}
	}
	else if(name =="GetFaceImageFromCam")
	{
		if( re!=undefined)
		{
			alert("GetFaceImageFromCam失败："+re);
		}
		else{
			DisplayImg(jsonOBJ.Base64);

		}
	}

}

 
 //打开双目人脸摄像头
 function OpenDualFaceDevice()
 {
	//打开摄像头
	var jsonObj = {function:'OpenDualFaceDevice'};
	sendWsMessage(jsonObj);
 }
 //关闭双目人脸摄像头
 function CloseDualFaceDevice()
 {
	//打开摄像头
	var jsonObj = {function:'CloseDualFaceDevice'};
	sendWsMessage(jsonObj);
 }
 //获取活体检测结果
 function GetFaceLivingBodyStatus()
 {
	//打开摄像头
	var jsonObj = {function:'GetFaceLivingBodyStatus'};
	sendWsMessage(jsonObj);
 }
 //获取检测到的人脸图片
 function GetFaceImageFromCam()
 {
	//打开摄像头
	var jsonObj = {function:'GetFaceImageFromCam'};
	sendWsMessage(jsonObj);
 }

//H5-接收图像Base64数据显示。
function setImageWithBase64(str)
{
	var myimg = document.getElementById("videoPreview");
	myimg.src = "data:image/png;base64,"+str;
}

//H5-发送消息给Server
function sendWsMessage(jsonObj){
	var jsonStr = JSON.stringify(jsonObj);
	ws.send(jsonStr);
}
//添加图片项
function DisplayImg(base64Img) {
	var mime = "jpg";
	var imgBase64Str = base64Img;
	var h = '<div><img width="360" style="padding:5px;" src="' + 'data:' + mime + ';base64,' + imgBase64Str + '"></div>';
	$("#image_data").prepend(h);
}

//页面关闭IE或者H5反初始化。
function UnInitCamera()
{
	CloseDualFaceDevice();
}

</script>