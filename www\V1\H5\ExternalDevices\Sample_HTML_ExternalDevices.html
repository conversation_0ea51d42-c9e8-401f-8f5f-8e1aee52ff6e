﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>ExternalDevices_CamSDKOCX_Demo</title>
</head>
<body >	
	
<table border="0" style = "margin-left:5%; margin-top:5%;" width="800" height="500">
	<tr height="20">
		<td colspan="3" >
			存储路径：
			<input style="margin-left:50; width:280" type = "text" name="path" id="path" value="C:\tmp" onchange="SetSaveFolder()" >
		</td>
	
	</tr>
	<tr height="20">
	</tr>
	<tr height="20">
		<td colspan="4">
			身份证读卡器 
		</td>
	</tr>
	<tr width="100%" height="35%">
		<td colspan="2" >
			
			<textarea id="idMsg" style="width:100%;height:100%;">
			</textarea>
		</td>
		
		<td width="100px" height="100%">
			<img id="imgPreview" border="1" src="" width="100%" height="100%"/> 
		</td>
		<td width="100">
			<textarea id="IDCardFP" style="width:100%;height:100%;">
			</textarea>
		</td>
	</tr>
	<tr>
		<td colspan="4">
		<input type = "button" value = "读卡" onClick="ReadIDCard()" style = "width:100; text-align:center">	
		<input type = "button" value = "获取指纹" onClick="ReadIDCardFingerprint()" style = "width:100; text-align:center">	
		<input type = "button" value = "读取银行卡" onClick="ReadBank()" style = "width:100; text-align:center">	
		<input type = "button" value = "读取社保卡" onClick="ReadSse()" style = "width:100; text-align:center">
		<br>
		<br>
		<label >身份证合并类型</label>
	
		<select id = "CombineType">
			<option value = "0">不合并</option>
			<option value = "1">左右合并</option>
			<option value = "2">上下合并</option>
			<option value = "3">上下合并有空隙</option>
		</select>
		<input type = "button" value = "身份证正反面合并" onClick="CombineIDCardImage()" style = "width:150 ; text-align:center">	
		<br>
		<img id ="CombinePic" style="display:none" width = "200px" height="180px"/>	
		<img id ="CombinePic1" style="display:none" width = "200px" height="180px"/>	
		</td>
	</tr>	
	<tr height="20">
	</tr>
	<tr height="20">
		<td colspan="4">
			指纹采集器
		</td>
	</tr>

	<tr>
		<td width="200">
		<table>
			<tr>
				<td>
					指纹1
				</td>
			</tr>
			<tr>
				<td>
					<img ID="fingerprint1" src="" border="1" width="100" height="100"/> 
				</td>
			</tr>
			<tr>
				<td>
					<input type = "button" value = "采集" onClick="GetFingerprint(1)" style = "width:100; text-align:center">	
				</td>
			</tr>
		</table>			
		</td>
		<td width="200">
			<table>
				<tr>
					<td>
						指纹2
					</td>
					</tr>
				<tr>
					<td>
						<img ID="fingerprint2" src="" border="1" width="100" height="100"/> 
					</td>
				</tr>
				<tr>
					<td>
						<input type = "button" value = "采集" onClick="GetFingerprint(2)" style = "width:100; text-align:center">		
					</td>
				</tr>
			</table>
		</td>
		<td colspan="2">
			<table>
				<tr><td>
					<input type = "button" value = "指纹1与2对比" onClick="Contrast(1)" style = "width:200; text-align:center">		
				</td></tr>
				<tr><td>
					<input type = "button" value = "base64指纹1与2对比" onClick="Contrast(2)" style = "width:200; text-align:center">		
				</td></tr>
				<tr><td>
					<input type = "button" value = "身份证指纹与指纹1对比" onClick="Contrast(3)" style = "width:200; text-align:center">		
				</td></tr>
				<tr><td>
					<input type = "button" value = "Base64身份证指纹与指纹1对比" onClick="Contrast(4)" style = "width:200; text-align:center">		
				</td></tr>
			</table>
		</td>
	</tr>
	

</table>
 
    <script src="js/globeVar.js"></script>
    <script src="js/mainH5.js"></script>
	<script src="js/WebSocket.js"></script>
	<script src="js/ExternalDevice.js"></script>

</body>
</html>
