import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询区域列表
export function listsite(query) {
    return request({
      url: '/inspectsite/doQuery',
      method: 'post',
      data: query
    })
}

  // 查询区域详细
  export function getsite(siteId) {
    return request({
      url: '/inspectsite/' + parseStrEmpty(siteId),
      method: 'get'
    })
}

// 新增区域
export function addsite(data) {
    return request({
      url: '/inspectsite/doAdd',
      method: 'post',
      data: data
    })
}

// 修改区域
export function updatesite(data) {
    return request({
      url: '/inspectsite/doEdit',
      method: 'post',
      data: data
    })
}


// 删除区域
export function delsite(siteId) {
    return request({
      url: '/inspectsite/doDelete/' + siteId,
      method: 'post'
    })
  }

// 解绑路线
export function unbindSite(id) {
  return request({
    url: '/inspectsite/unbindSite/' + id,
    method: 'get'
  })
}