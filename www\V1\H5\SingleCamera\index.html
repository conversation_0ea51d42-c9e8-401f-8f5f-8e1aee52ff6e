<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- utf-8编码-->
    <meta charset="utf-8">
    <title>CamSDK</title>
</head>
<body>
<div>
    <div style="float:left;margin-right:20px;">
        <div id="video1">
            <img id="video" style="width:506px;height:380px">
        </div>

        <p>
            <button onclick="ZoonMin()">+</button>
            <button onclick="ZoonMOut()">-</button>
            <button onclick="Preview11()">1:1</button>
            <button onclick="Best()">Best</button>
        <p>
            <label>设备控制/参数设置</label>
        <div style="margin-left:5px;margin-top:10px;float:left">
            <label>设备</label>
            <select id="devicList"></select>
            <p>
                <label>视频格式</label>
                <select id="videoStyle"></select>
            <p>
                <label>分辨率</label>
                <select id="reslutionList"></select>
            <p>
                <label>蜂鸣器设置</label>
            <p>
                <button onclick="Buzzer()" style="margin-left: 10px;">蜂鸣器</button>
                <button onclick="AutoFoucs()" style="margin-left: 10px;">自动对焦</button>
        </div>
        <button onclick="ShowDevSettingWindow()" style="margin-left: 10px;">设备设置</button>
        <button onclick="ShowImageSettingWindow()" style="margin-left: 10px;">图像设置</button>

    </div>
    <div style="margin-left: 20px;">
        <p>
            <span>存储路径:</span>
            <input type="text" id="saveText" value="C:\tmp\" style="margin-left:5px;width: 150px;text-align: left;"/>
            <button onclick="OpenFile()" style="margin-left: 10px;">查看文件</button>
        <p>
            <label>拍照及图像设置</label>
        <p>
            <span>颜色类型</span>
            <select onchange="SetColorStyle()" id="colorStyle">
                <option>彩色</option>
                <option>灰度</option>
                <option>黑白</option>
            </select>
            <span>PS特效</span>
            <select onchange="setImageAdjust()" id="imageAdjust">
                <option>无效果</option>
                <option>文档增强</option>
                <option>彩色增强</option>
                <option>灰色</option>
                <option>黑白</option>
                <option>油画</option>
                <option>怀旧</option>
                <option>素描</option>
                <option>边缘照亮</option>
                <option>蓝冷</option>
                <option>马赛克</option>
                <option>模糊</option>
            </select>
        <p>
            <span>图像类型</span>
            <select onchange="SetImageType()" id="imageType">
                <option>jpg</option>
                <option>bmp</option>
                <option>png</option>
                <option>tif</option>
                <option>ico</option>
                <option>pdf</option>
            </select>
            <span style="margin-left: 0px;">JPG压缩值:</span>
            <input type="text" id="jpg" value="75"/>
            <button onclick="JPGQSet()">设置JPG压缩率</button>
        <p>
            <span>裁切类型</span>
            <select onchange="SetCutStyle()" id="cutStyle">
                <option value="0">不裁切</option>0-
                <option value="1">单图裁切</option>
                <option value="2">多图裁切</option>
                <option value="6">桌面裁切</option>
            </select>
            <span>旋转类型</span>
            <select onchange="SetRotationStyle()" id="rotationStyle">
                <option>不旋转</option>
                <option>90</option>
                <option>180</option>
                <option>270</option>
            </select>
        <p>
            <span>设置DPI</span>
            <input type="text" id="dpix" value="300" style="width: 50px;"/>
            <button onclick="DPISet()" style="margin-left: 0px;">设置DPI</button>
        <td>
            <input type="checkbox" name="Denoise" id="Denoise" onClick="SetDenoise(Denoise)"/>
            去噪
        </td>
        <td>
            <input type="checkbox" name="ImageFileSign" id="ImageFileSign" onClick="SetImageFileSign(ImageFileSign)"/>
            防篡改
        </td>
        <td>
            <!--
            <input type = "checkbox" name = "CurCrop" id = "CurCrop" onClick="SetCusCrop(CurCrop)"/>
            手动裁切
            </td>
            -->
            <p>
            <div class="sideDiv">
                <span>手动裁切</span>
                <span style="margin-left: 10px;">左</span>
                <input type="text" value="0" id="left" style="margin-left:5px;width:50px;"/>
                <span style="margin-left: 10px;">上</span>
                <input type="text" value="0" id="top" style="margin-left:5px;width: 50px;"/>
                <span style="margin-left: 10px;">右</span>
                <input type="text" value="100" id="right" style="margin-left:5px;width: 50px;"/>
                <span style="margin-left: 10px;">下</span>
                <input type="text" value="100" id="bottom" style="margin-left:5px;width: 50px;"/>
                <button onclick="CropZoneSet()" style="margin-left: 10px;">设置裁切区域</button>
            </div>
            <p>
                <button onclick="Capture()">拍照</button>
                <button onclick="CaptureBase64Ex()">拍照（Base64）</button>
                <button onclick="CaptureBarcode()">拍照（条码识别）</button>
                <button onclick="CaptureBook()">拍书</button>
            <p>

            <div style="margin-top: 20px; margin-left: 20px; ">
                <button onclick="continuCapture()">连拍</button>
                <button onclick="timeCapture()">定时连拍</button>
                <button onclick="StopAutoCapture()">停止连拍</button>
                <br/>
                <br/>
                <progress id="autoCaptureProgress" value="0" max="100">
                </progress>

            </div>

            <div style="margin-top: 20px; margin-left: 20px; ">
                <span>麦克风</span>
                <select id="Microphone">
                </select>
                <span>录像格式</span>
                <select id="VideoType">
                </select>
                <!--span>音量</span>
                <input type = "text" id = "voice"  value = "0" style="margin-left:5px;width:50px;"-->
                <br/>
                <br/>
                <span>存储路径</span>
                <input type="text" id="SaveVieoText" value="C:\tmp\Video.AVI"
                       style="margin-left:5px;width: 150px;text-align: left;"/>
                <button onclick="StartVideo()">开始录像</button>
                <button onclick="StopVideo()">停止录像</button>
            </div>


            <ul id="parentUl"></ul>
    </div>

</div>

<!-- SDK封装 -->
<script src="js/globeVar.js"></script>
<script src="js/mainH5.js"></script>
<script src="js/WebSocket.js"></script>

<!--页面逻辑-->
<script src="js/SingleCamera.js"></script>


</body>
</html>