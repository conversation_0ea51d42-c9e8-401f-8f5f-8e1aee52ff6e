/*
    Version: 1.0 Timestamp: 2021.4.23
*/
html,body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
.ifram{
    width: 100%;
    height: 100%;
}

.left{
    width: 250px;
    min-width: 250px;
    height: 100%;
    border:1px solid #000;
}
.mindle{
    /*width: calc(100%);*/
    height: 100%;
    border-top:1px solid #000;
    border-bottom:1px solid #000;
}
.right{
    width: 400px;
    min-width: 150px;
    height: 100%;
    border: 1px solid #000;
}

.mindle-top {
    height: calc(100% - 50px);
    display: -webkit-box;
    -webkit-box-pack:center;
    -webkit-box-orient:vertical;
    text-align: center;
}
.mindle-top-img{
    position: relative;	
    background-color:white;
}

.mindle-botom {
    height: 50px;
    line-height: 50px; /* 设置垂直居中 */
    text-align:center;
    border-top:1px solid #000;
}
.mindle-botom button{
    width: 100px;
    height: 30px;  
}
.left-one{
    height: 80px;
}
.left-one-left{
    float: left;
    width: 35%;
    margin: 5px 5px 0 15px;
}
.left-one-left img{
    width: 80%;
}
.left-one-right{
    float: left;
    width: 55%;
    margin: 30px 0 0 0;
    min-height: 67px;
    font-size: 20px;
}
.left-one-right select{
    width: 131px;
    height: 25px;
    margin-top: 3px;
}

.left-two{
    height: 85%;
}
.left-two-lable{
    width: 72px;
    height: 30px;
    margin: 3px 3px 0px 3px;
    display:inline-block;
}
.left-two-lable-set{
    width: 35%;
    height: 30px;
    text-align: center;
    margin: 3px 3px 0px 3px;
    display:inline-block;
}
.left-two-select{
    width: 155px;
    height: 30px;
    margin: 3px 3px 3px 3px;
}    

.right-div{
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: scroll;
}

.mindle-top-line{
    position: absolute;
    height: calc(100% - 60px);
    left: 254px;
    top: 4px;
    border-right:2px solid green;
    z-index: 999;
}

#resultArea {
    width: 95%;
    height: 40px;
}
#clearImage {
    float:right;
    margin-top: 10px;
}