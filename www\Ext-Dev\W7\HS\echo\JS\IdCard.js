function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function DisplayIdCardInfo(v) {
    if (v.result == 0) {
        if (v.cardInfo) {
            displayOutputInfo(2, "身份证信息回显成功");
            var ci = v.cardInfo;
            $("#name").text(ci.name);
            $("#gender").text(ci.gender);
            $("#folk").text(ci.folk);
            $("#birth").text(ci.birth);
            $("#address").text(ci.address);
            $("#ID").text(ci.id);
            $("#issue").text(ci.issue);
            $("#startEndDate").text(ci.startDate + " - " + ci.endDate);
            document.getElementById("pic").src = "data:" + ci.mime + ";base64," + ci.imageBase64;
        }
    } else {
        displayOutputInfo(3, "身份证读取失败。" + jsonObj.func + "<br>" + jsonObj.errorMsg);
    }
}

function GetSerachParam() {
    var params = new Map();;
    var s = location.search;
    if (s.length > 0) {
        var ary1 = s.substring(1).split("&");
        for (var i = 0; i < ary1.length; ++i) {
            var ary2 = ary1[i].split("=");
            params.set(ary2[0], ary2[1]);
        }
    }
    return params;
}

function ReadIdInfo() {
    // 提示信息显示
    displayOutputInfo(1, "正在获取身份证信息......");

    var p = GetSerachParam();
    var url = location.origin + "/tmp/" + p.get("client") + "/" + p.get("file");
    $.ajax({
        type: 'GET',
        url: url,
        dataType: "json",
        success: function (r) {
            DisplayIdCardInfo(r);
        },
        error: function (msg) {
            console.log(msg);
            displayOutputInfo(3, "获取身份证信息。" + "<br>" + msg);
        }
    });
}

$(function () {
    ReadIdInfo();
});