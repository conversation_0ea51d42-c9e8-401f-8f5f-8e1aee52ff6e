function DisplaySignInfo(v) {
    // 返回的json格式：{"signImageBase64:"字节码png","func":"Sign","reqId":"2132131","result":0}
    if (v.result == 0) {
        document.getElementById("pic").src = "data:" + v.mime + ";base64," + v.signImageBase64;
        displayOutputInfo(2, "回显签名成功");
    } else {
        displayOutputInfo(3, "回显签名失败：" + v.errorMsg);
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function GetSerachParam() {
    var params = new Map();
    var s = location.search;
    if (s.length > 0) {
        var ary1 = s.substring(1).split("&");
        for (var i = 0; i < ary1.length; ++i) {
            var ary2 = ary1[i].split("=");
            params.set(ary2[0], ary2[1]);
        }
    }
    return params;
}

function ReadSignInfo() {
    // 提示信息显示
    displayOutputInfo(1, "正在获取签名信息......");

    var p = GetSerachParam();
    var url = location.origin + "/tmp/" + p.get("client") + "/" + p.get("file");
    $.ajax({
        type: 'GET',
        url: url,
        dataType: "json",
        success: function (r) {
            DisplaySignInfo(r);
        },
        error: function (msg) {
            console.log(msg);
            displayOutputInfo(3, "获取身份证信息。" + "<br>" + msg);
        }
    });
}

$(function () {
    ReadSignInfo();
});