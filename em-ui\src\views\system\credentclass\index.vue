<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="分类名称" prop="className">
        <el-input
          v-model="queryParams.className"
          placeholder="请输入分类名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
   
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="classList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="分类名称" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
            <span>{{ scope.row.className }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检测周期类型" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
            <span>{{ timeText(scope.row.checkType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="检测周期" align="center" prop="checkCycle" :show-overflow-tooltip="true" />
      <el-table-column label="提醒周期类型" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
            <span>{{ timeText(scope.row.remindType) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="提醒周期" align="center" prop="remindCycle" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">正常</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="类型名称" prop="className">
          <el-input v-model="form.className" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="检测周期类型" prop="checkType">
          <el-select v-model="form.checkType" placeholder="请选择类型" >
            <el-option v-for="item in timeTypes" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检测周期" prop="checkCycle">
          <el-input type="number" v-model="form.checkCycle" placeholder="请输入检测周期" />
        </el-form-item>
        <el-form-item label="提醒周期类型" prop="remindType">
          <el-select v-model="form.remindType" placeholder="请选择类型" >
            <el-option v-for="item in timeTypes" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提醒周期" prop="remindCycle">
          <el-input type="number" v-model="form.remindCycle" placeholder="请输入提醒周期" />
        </el-form-item>
        <el-form-item label="提醒内容" prop="remind">
          <el-input  v-model="form.remind" placeholder="请输入提醒内容" />
        </el-form-item>
        <el-form-item label="状态">
              <el-radio-group v-model="form.state">
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listClass, getClass, addClass, updateClass } from "@/api/credent/credent";

export default {
  name: "Dict",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分类表格数据
      classList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        className: undefined,
      },
      // 表单参数
      form: {
        state:1
      },
      // 表单校验
      rules: {
        className: [
          { required: true, message: "分类名称不能为空", trigger: "blur" }
        ],
        checkType: [
          { required: true, message: "检测周期类型不能为空", trigger: "change" }
        ],
        checkCycle: [
          { required: true, message: "检测周期不能为空", trigger: "blur" }
        ],
      },
         //状态
      options: [{
          value: 1,
          label: '正常'
        }, {
          value: 2,
          label: '停用'
      }],
      //时间类型
      timeTypes:[
        {
          value: 8,
          label: '年'
        }, {
          value: 6,
          label: '季度'
        }, {
          value: 5,
          label: '月'
        }, {
          value: 4,
          label: '周'
        }, {
          value: 3,
          label: '天'
        }
      ],
    };
  },
  computed: {
			timeText() {
				return (type) => {
					switch (type) {
						case 8:
							return '年';
							break;
						case 6:
							return '季度';
							break;
            case 5:
							return '月';
							break;
            case 4:
							return '周';
							break;
            case 3:
							return '天';
							break;
						default:
							return '未知';
							break;
					}
				}
			}
	},
  created() {
    this.getList();
  },
  methods: {
    /** 查询资质类型列表 */
    getList() {
      this.loading = true;
      listClass(this.queryParams).then(response => {
          this.classList = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        institutionclassId: undefined,
        className: undefined,
        classCode: undefined,
        state: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加资质类型";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cmsclassId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      
      const credentclassId = row.credentclassId || this.ids
      getClass(credentclassId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改资质类型";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.credentclassId != undefined) {
            updateClass(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addClass(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },



  }
};
</script>