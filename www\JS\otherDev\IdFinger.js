var _headBase64 = null;
var _fingerFeature1 = null, _fingerFeature2 = null, _fingerFeature3 = null, _fingerFeature4 = null;
var _devInfos = null;
var _devNum = -1;
var _myWs = new MzWsConnect(location.port, onTxtMessage, onError, ClearOutputInfo);

// 处理连接错误
function onError(e, msg) {
    displayOutputInfo(3, msg);
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

function ClearOutputInfo() {
    displayOutputInfo(0);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 23) {
        // 返回result=0表示执行成功,其他值表示异常。23表示指纹匹配不成功
        console.log(jsonObj.func, jsonObj.errorMsg);
        displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
    } else {
        switch (jsonObj.func) {
            case "ReadIDCardInfo":
                DisplayIDCardInfo(jsonObj);
                break;
            case "ReadFingerInfo":
                DisplayFingerInfo(jsonObj);
                break;
            case "FingerMatch":
                DisplayMatchInfo(jsonObj);
                break;
            default:
                console.log(msg);
                if (jsonObj.result != 0) {
                    displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
                }
                break;
        }
    }
}

var _fpMap = {11:'右手拇指',12:'右手食指',13:'右手中指',14:'右手环指',15:'右手小指',16:'左手拇指',17:'左手食指',18:'左手中指',19:'左手环指',20:'左手小指',97:'右手不确定指位',98:'左手不确定指位',99:'其他不确定指位'};

// 显示从身份证读取到的信息
function DisplayIDCardInfo(v) {
    var ci = v.cardInfo;
    // {"address":"长沙市...","birth":"19900101","endDate":"20301031","fingerFeature":"","folk":"汉","gender":"男","id":"430.....",
    //  "imageBase64":"...","imagePath":"F:\\...\\1628740497.bmp","issue":"长沙市公安局","mime":"image/bmp","name":"XXX","startDate":"20101030"}
    if (ci) {
        _headBase64 = ci.imageBase64;
        var obj = document.getElementById('imgHead');
        obj.className = "";
        obj.src = "data:" + v.mime + ";base64," + _headBase64;

        var s2 = "身份证没有指纹信息";
        var s = Lang.IDC.name + ci.name + "\r\n";
        s += Lang.IDC.folk + ci.folk + "\r\n";
        s += Lang.IDC.gender + ci.gender + "\r\n";
        s += Lang.IDC.birthday + ci.birth + "\r\n";
        s += Lang.IDC.id + ci.id + "\r\n";
        s += Lang.IDC.address + ci.address + "\r\n";
        s += Lang.IDC.issue + ci.issue + "\r\n";
        s += Lang.IDC.usefulLife + ci.startDate + " - " + ci.endDate + "\r\n";
        //s += Lang.IDC.fpPath + ci.fpPath + "\r\n";
        if (ci.fingerFeature) {
            var typeName = _fpMap[ci.fingerFeature[0].type];
            s2 = "身份证有指纹信息: " + typeName;
            s += Lang.IDC.fingerFeature1 + typeName + "\r\n";
            _fingerFeature3 = ci.fingerFeature[0].featureBase64;
            EnabledOption(3);
            if (ci.fingerFeature.length > 1) {
                typeName = _fpMap[ci.fingerFeature[1].type];
                s2 += ", " + typeName;
                s += Lang.IDC.fingerFeature2 + typeName;
                _fingerFeature4 = ci.fingerFeature[1].featureBase64;
                EnabledOption(4);
            } else {
                s += Lang.IDC.fingerFeature2 + "无";
            }
        } else {
            s += Lang.IDC.fingerFeature1 + "无\r\n";
            s += Lang.IDC.fingerFeature2 + "无";
        }
        document.getElementById('idCardInfo').textContent = s;
        document.getElementById('fpIdCard').textContent = s2;
        displayOutputInfo(2, "身份证读取成功.");
    }
    if (v.simulateImage && v.simulateImage.base64s) {
        DisplayImg('imgIdCard1', v.simulateImage.mime, v.simulateImage.base64s[0]);
        if (v.simulateImage.base64s.length > 1) {
            DisplayImg('imgIdCard2', v.simulateImage.mime, v.simulateImage.base64s[1]);
        }
    }
}

function DisplayImg(id, mime, imgBase64Str) {
    var obj = document.getElementById(id);
    if (obj) {
        obj.src = "data:" + mime + ";base64," + imgBase64Str;
        obj.style.display = "";
        var w = $("#simulateImageType").val() == 2 ? 800 : 400;
        obj.style.width = w + "px";
    }
}

function DisplayFingerInfo(v) {
    if (v.reqId.substr(1,1) == ',') {
        var pos = v.reqId.substr(0,1);
        var obj = document.getElementById('imgFP' + pos);
        if (obj) {
            obj.src = "data:" + v.mime + ";base64," + v.fingerImageBase64;
        }
        eval('_fingerFeature' + pos + ' = v.fingerFeatureBase64');
        EnabledOption(parseInt(pos));
        displayOutputInfo(2, "采集指纹" + pos + "采集成功.");
    }
}

function EnabledOption(i) {
    $("#fp1 option").eq(i-1).removeAttr("disabled");
    $("#fp2 option").eq(i-1).removeAttr("disabled");
}

function onChangeFP() {
    var v1 = $("#fp1").val(), v2 = $("#fp2").val(), v = "disabled";
    if (v1 !== null && v2 !== null &&  v1 != v2) {
        $("#btnMatch").removeAttr(v);
    } else {
        $("#btnMatch").attr(v, v);
        if (v1 !== null && v1 == v2) {
            alert("指纹比对要选择两个不同指纹。");
        }
    }
}

function DisplayMatchInfo(v) {
    var ary = v.reqId.split(',');
    var str = "指纹" + ary[0] + "与指纹" + ary[1] + ", ";
    var type = 2;
    if (v.result == 0) {
        str += "匹配成功";
    } else {
        str += v.errorMsg;
        type = 3;
    }
    str += "。相似度: " + v.similarity + ", 指定相似度阈值: " + v.threshold;
    displayOutputInfo(type, str);
}

function displayOutputInfo(disp, s, add) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        var h = "<b>" + s + "</b>";
        if (add) {
            var h0 = $('#outInfo').html();
            if (h0.length > 0) {
                h = h0 + "<br>" + h;
            }
        }
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html(h);
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

// 读取二代身份证的信息及人像
function readIdCard() {
    $('#imgIdCard1').css("display", "none");
    $('#imgIdCard2').css("display", "none");
    // idType 读取ID卡的类型：0-读身份证信息；1-读社保卡信息；2-读银行卡信息
    // fpNumber 返回指纹的个数，缺省值为1；0-不返回指纹信息；1-返回第一个指纹；2-返回第一和第二个指纹
    // simulateImageType 返回身份证模拟照片的类型
    _myWs.SendJson({func:"ReadIDCardInfo", idType:0, simulateImageType:parseInt($("#simulateImageType").val()), mode:"base64", fpNumber:2, reqId: new Date().getTime()});
}

// 读取指纹
function ReadFP(pos) {
    var posAndReqId = pos + ',' + new Date().getTime();
    _myWs.SendJson({func:"ReadFingerInfo", reqId: posAndReqId});
}

// 指纹比对
function MatchFP() {
    var pos1 = $("#fp1").val(), pos2 = $("#fp2").val();
    var posAndReqId = pos1 + ',' + pos2 + ',' + new Date().getTime();
    var cmd = {func:"FingerMatch", threshold:0.85, reqId: posAndReqId};
    cmd.fingerFeature1 = eval('_fingerFeature' + pos1);
    cmd.fingerFeature2 = eval('_fingerFeature' + pos2);
    _myWs.SendJson(cmd);
}
