<template>
  <div class="app-container">
   
 <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="事故单位" prop="orgId">
           <treeselect v-model="queryParams.accidentOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择事故发生单位" style="width:200px"/>
      </el-form-item>
      <el-form-item label="事故地点" prop="accidentaddress">
             <el-select v-model="queryParams.accidentaddress" placeholder="请选择事故地点" clearable>
                  <el-option v-for="dict in dict.type.system_accsite" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
        </el-form-item>
       <el-form-item label="事故时间" prop="accidenttime">
          <el-date-picker v-model="queryParams.gtdate" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
          -
          <el-date-picker v-model="queryParams.ltdate" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
      
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="handList" @selection-change="handleSelectionChange">
      <el-table-column label="上报人" align="center" prop="worker.workerName" :show-overflow-tooltip="true"/>
      <el-table-column label="所属组织" align="center" prop="organization.organName" />
      <el-table-column label="上报日期" align="center" prop="creationtime" >
        <template slot-scope="scope">
         <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否上报" align="center" prop="ishand" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.ishand==true">已上报</span>
        </template>
      </el-table-column>
      <el-table-column label="事故地点" align="center"  prop="accidentaddress" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.system_accsite" :value="scope.row.accidentaddress"/>
        </template>
      </el-table-column>
      
     <el-table-column label="事故时间" align="center" prop="accidenttime" />
     <el-table-column label="事故单位" align="center" prop="accidentOrg.organName" />
     <el-table-column label="当事人" align="center" prop="clientUser" />
     <el-table-column label="相关人" align="center" prop="relatedUser" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" >修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" >删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-position" @click="handleReport(scope.row)" >上报</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改事故对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
            <el-form-item label="所属部门" prop="orgId">
              <el-select v-model="form.orgId" placeholder="请选择部门" clearable>
                  <el-option v-for="dict in userorgList" :key="dict.organId" :label="dict.organization.organName" :value="dict.organId"/>
              </el-select>
            </el-form-item>
        </el-row>
        <el-form-item label="事故发生部门" prop="accidentOrgId">
              <treeselect v-model="form.accidentOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择事故发生部门" />
        </el-form-item>
        <el-form-item label="事故地点" prop="accidentaddress">
             <el-select v-model="form.accidentaddress" placeholder="请选择事故地点" clearable>
                  <el-option v-for="dict in dict.type.system_accsite" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
        </el-form-item>
         <el-form-item label="事故时间" prop="accidenttime">
          <el-date-picker v-model="form.accidenttime" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
       <el-form-item label="当事人" prop="clientUser">
          <el-input v-model="form.clientUser" placeholder="请输入当事人" />
        </el-form-item>
        <el-form-item label="相关人" prop="relatedUser">
          <el-input v-model="form.relatedUser" placeholder="请输入相关人" />
        </el-form-item>
        <el-row>
            <el-form-item label="事故简述"  prop="briefcourse">
               <el-input  type="textarea" v-model="form.briefcourse" placeholder="请输入事故简述" maxlength="500"/>
            </el-form-item>

        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" v-show="unreport">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

     <!-- 上报人员列表-->
  <el-dialog :title="title" :visible.sync="openuser" width="700px" append-to-body>
    <el-form :model="userform" ref="userform" size="small" :inline="true"  label-width="68px">
      <el-form-item label="人员" prop="workerId">
       <el-select v-model="userform.workerId" placeholder="请选择人员" clearable>
        <el-option
          v-for="item in useroptions"
          :key="item.worker.workerId"
          :label="item.worker.workerName"
          :value="item.worker.workerId">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleadduser">新增</el-button>
        <el-button type="primary" icon="el-icon-s-promotion" size="mini" @click="handlepush">上报</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="userList"  >
      <el-table-column label="姓名" align="center"  prop="worker.workerName"  :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="工号" align="center"  prop="worker.workerCode"  :show-overflow-tooltip="true" >
      </el-table-column>
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template slot-scope="scope">
         <el-button 
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDeluser(scope.row)"
          >删除</el-button>
      </template>
    </el-table-column>
    </el-table>
 </el-dialog>



  </div>
</template>

<script>
import { listHand,getHand,addHand,updateHand,delHand,listPushuser,addPushuser, delPush} from "@/api/accident/accident";
import { getOrgByWork,listWorkorg} from "@/api/system/user";
import {listDeptExcludeChild } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "AccidentHand",
  components: { Treeselect },
  dicts: ['system_accsite'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
       // 部门树选项
      deptOptions: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      //显示上报人员
      openuser:false,
       //人员
      useroptions:[],
      // 总条数
      total: 0,
      // 上报数据
      handList: [],
      //用户组织
      userorgList:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示查看弹出层
      viewopen:false,
      //是否上报
      unreport:true,
      //上报人员form
      userform:{},
      //人员列表
      userList:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },

      // 表单参数
      form: {},
      // 表单校验
      rules: {
        orgId: [
          { required: true, message: "部门不能为空", trigger: "change" }
        ],
        accidentOrgId: [
          { required: true, message: "部门不能为空", trigger: "change" }
        ],
        accidentaddress: [
          { required: true, message: "地点不能为空", trigger: "change" }
        ],
        accidenttime: [
          { required: true, message: "时间不能为空", trigger: "change" }
        ],
        briefcourse: [
          { required: true, message: "描述不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {

    this.getList();
    this.getuserorglist();
  },
  methods: {
    /** 查询列表 */
    getList() {
      this.loading = true;
      this.queryParams.orderBy="creationtime desc"
      listHand(this.queryParams).then(response => {
        this.handList = response.data;
        this.total = response.totalCount;
        this.loading = false;
      });
    },
    //查询用户组织
    getuserorglist(){
        getOrgByWork().then(response => {
          this.userorgList = response.data
          console.log(JSON.stringify(this.userorgList))
        })
         let queryparams1 = {};
       listDeptExcludeChild(queryparams1).then(response => {
          let datas = response.data;
          for(let i=0; i<datas.length; i++){
            datas[i].children=[];
          }
          this.deptOptions = this.handleTree(datas, "organId");
        });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        accidenthandId: undefined,
        workerId: undefined,
        orgId: undefined,
        accidentOrgId: undefined,
        accidenttime: undefined,
        accidentaddress: undefined,
        briefcourse: undefined,
        causeanalysis: undefined,
        currentdeal: undefined,
        description: undefined,
        advice: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      console.log(JSON.stringify(this.queryParams))
      if(this.queryParams.accidentOrgId == ''){
        this.queryParams.accidentOrgId=undefined
      }
      if(this.queryParams.accidentaddress == ''){
        this.queryParams.accidentaddress=undefined
      }
      if(this.queryParams.accidenttime == ''){
        this.queryParams.accidenttime=undefined
      }
      if(this.queryParams.gtdate == '' || this.queryParams.gtdate===null){
        this.queryParams.gtdate=undefined
      }
      if(this.queryParams.ltdate == '' || this.queryParams.ltdate==null){
        this.queryParams.ltdate=undefined
      }
      if(this.queryParams.gtdate != undefined){
        if(this.queryParams.ltdate == undefined ){
           this.$modal.msgError("时间范围不正确");
           return
        }
      }
      if(this.queryParams.ltdate != undefined){
        if(this.queryParams.gtdate == undefined ){
           this.$modal.msgError("时间范围不正确");
           return
        }
      }
      this.getList();
    },
        /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cmsId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    //查询组织内人员
    listworkerorg(organId){
      let query = {};
      query.organId=organId
      listWorkorg(query).then(response => {
        console.log(JSON.stringify(response.data))
          this.useroptions = response.data;
        }
      );
    },
     //发送人员列表
    getUserList(accidenthandId){
      this.loading = true;
      let query = {}
      query.accidenthandId = accidenthandId
      query.workerType=6
      listPushuser(query).then(response => {
          this.userList = response.data;
          this.loading = false;
        }
      );
    },
    /**新增发送人员 */
    handleadduser(){
        if(typeof(this.userform.workerId)!=="undefined" && this.userform.workerId != ''){
          this.userform.workerType=6

           for(let i=0; i<this.userList.length; i++){
              let userhand = this.userList[i];
              if(userhand.workerId == this.userform.workerId){
                this.$modal.msgError("人员已添加");
                return;
              }
           }
           addPushuser(this.userform).then(response => {
             this.$modal.msgSuccess("新增成功");
             this.getUserList(this.userform.accidenthandId);
          });
      }
    },
        /**删除巡检人员 */
    handleDeluser(row){
      const accidentuserId = row.accidentuserId
      const accidenthandId= row.accidenthandId
      this.$modal.confirm('是否确认发送人员？').then(function() {
        return delPush(accidentuserId);
      }).then(() => {
        this.getUserList(accidenthandId);
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "事故上报";
       this.unreport=true
      let queryparams1 = {};
      // if (typeof(row.organId) != undefined) {
      //   queryparams1.organId=row.organId
      // }
       listDeptExcludeChild(queryparams1).then(response => {
          let datas = response.data;
          for(let i=0; i<datas.length; i++){
            datas[i].children=[];
          }
          this.deptOptions = this.handleTree(datas, "organId");
        });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {

      this.reset();
      const accidenthandId = row.accidenthandId || this.ids
      getHand(accidenthandId).then(response => {
        this.form = response.data;
        if(typeof(this.form.ishand) !="undefined" && this.form.ishand==true){
          this.unreport=false
        }else{
          this.unreport=true
        }
        this.open = true;
        this.title = "修改事故";

        let queryparams1 = {};
        // if (typeof(row.accidentOrgId) != undefined) {
        //   queryparams1.organId=row.accidentOrgId
        // }
        listDeptExcludeChild(queryparams1).then(response => {
          this.deptOptions = this.handleTree(response.data, "organId");
          if (this.deptOptions.length == 0) {
            console.log(JSON.stringify(this.form))
            const noResultsOptions = { organId: this.form.accidentOrgId, organName: this.form.accidentOrg.organName, children: [] };
            this.deptOptions.push(noResultsOptions);
          }
        });


      });
    },
    //上报
    handlepush(){
      if(this.userList.length==0){
        this.$modal.msgError("没有上报人员");
      }else{
          getHand(this.userform.accidenthandId).then(response=>{
              let accident = response.data
              accident.ishand=true
              this.$modal.confirm('是否确认上报事故？').then(function() {
                return updateHand(accident);
              }).then(() => {
                this.openuser=false
                this.getList();
                this.$modal.msgSuccess("上报成功");
              }).catch(() => {}); 
          })
          
      }
    },
    //上报按钮
    handleReport(row){
      const accidenthandId = row.accidenthandId || this.ids
      getHand(accidenthandId).then(response=>{
        if(response.data.ishand == undefined || response.data.ishand == false){
            this.userList=[]
            this.userform={}
            this.userform.accidenthandId=accidenthandId
            this.listworkerorg(response.data.orgId)
            this.getUserList(accidenthandId)
            this.openuser=true
        }else{
            this.$modal.msgError("已上报");
         
         
        }
        

      })
       
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {

          if (this.form.accidenthandId != undefined) {
            updateHand(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            this.form.ishand=false
            addHand(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const accidenthandId = row.accidenthandId || this.ids
       getHand(accidenthandId).then(response=>{
          // if(typeof(response.data.ishand)=="undefined" || response.data.ishand==false){
            this.$modal.confirm('是否确认删除事故？').then(function() {
              return delHand(accidenthandId);
            }).then(() => {
              this.getList();
              this.$modal.msgSuccess("删除成功");
            }).catch(() => {});
          // }else{
          //   this.$modal.msgError("已上报不可删除")
          // }
       })
      
    }
  }
};
</script>
