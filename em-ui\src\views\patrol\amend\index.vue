<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="责任单位" prop="blackspotOrgId">
          <treeselect v-model="queryParams.blackspotOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择责任单位"  style="width:200px"/>
      </el-form-item>
      <el-form-item label="状态" prop="state">
       <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

  

    <el-table v-loading="loading" :data="blackspotList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="责任单位" align="center" prop="organization.organName" :show-overflow-tooltip="true" />
      <el-table-column label="检查项目" align="center" prop="problemclass.className" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">待确认</span>
          <span v-if="scope.row.state==2">待整改</span>
          <span v-if="scope.row.state==3">待验收</span>
          <span v-if="scope.row.state==4">验收完成</span>
          <span v-if="scope.row.state==5">整改中</span>
          <span v-if="scope.row.state==6">驳回</span>
          <span v-if="scope.row.state==7">验证失败</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >整改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


     <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
        <el-form ref="blackspostvo" :model="blackspostvo" label-width="100px" size="mini">
          <el-row>
            <el-col :span="8">
              <el-form-item label=" 责任单位："> <span v-if="blackspostvo.organization!==undefined">{{blackspostvo.organization.organName}}</span></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" 分类："> {{blackspostvo.problemclass.className}}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label=" 状态：">  <span v-if="blackspostvo.state==1">待确认</span>
            <span v-if="blackspostvo.state==2">待整改</span>
            <span v-if="blackspostvo.state==3">待验收</span>
            <span v-if="blackspostvo.state==4">验收完成</span>
            <span v-if="blackspostvo.state==5">整改中</span>
            <span v-if="blackspostvo.state==6">驳回</span>
            <span v-if="blackspostvo.state==7">验证失败</span>
            </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label=" 描述：">  {{blackspostvo.blackspotDesc}}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label=" 现场图片：">   
                <span v-for="(imgpath,index) in blackspostvo.imgPaths" :key="index" style="paddingRight :5px">
                <image-preview :src="imgPath" :width="120" :heigth="120"/>
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-form ref="form" :model="form" label-width="100px" size="mini" :rules="rules">
         
           <el-col :span="12">
            <el-form-item label="状态" prop="state">
               <el-radio-group v-model="form.state" >
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="配合单位" prop="assistOrg">
               <treeselect v-model="form.assistOrg" :options="deptOptions" :normalizer="normalizer" placeholder="选择配合单位"  />
            </el-form-item>
          </el-col>
          <el-col :span="24">
             <el-form-item label="整改措施" prop="amendDesc">
              <el-input  type="textarea" v-model="form.amendDesc" placeholder="请输入整改措施" maxlength="500"/>
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="开始整改时间" prop="begindate">
              <el-date-picker v-model="form.begindate" type="date" placeholder="选择日期" format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
           </el-col>
           <el-col :span="12">
            <el-form-item label="结束整改时间" prop="enddate">
              <el-date-picker v-model="form.enddate" type="date" placeholder="选择日期" format="yyyy-MM-dd">
              </el-date-picker>
            </el-form-item>
           </el-col>
          <el-col :span="24">
             <el-form-item label="原因分析" prop="causeAnalysis">
              <el-input  type="textarea" v-model="form.causeAnalysis" placeholder="请输入原因分析" maxlength="500"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="整改图片" prop="amendimgPath">
                <image-upload v-model="amendimgPath" :limit="5"  :fileType="['png', 'jpg', 'jpeg']"/>
            </el-form-item>
          </el-col>
          <el-col :span="24">
           <el-form-item label="验收人" prop="verifyerId">
              <el-select v-model="form.verifyerId" placeholder="请选择验收人" clearable>
                <el-option
                  v-for="item in verifyers"
                  :key="item.worker.workerId"
                  :label="item.worker.workerName"
                  :value="item.worker.workerId">
                </el-option>
              </el-select>
          </el-form-item>
        </el-col>
        </el-form>
       <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
     </el-dialog>
  </div>
</template>

<script>
import { listClass, listProblem} from "@/api/patrol/problem";
import { listblackspothand, getblackspothand,getAmendByHand,addblackspotamend,updateblackspotamend} from "@/api/patrol/blackspot";
import {  listDeptExcludeChild } from "@/api/system/dept";
import {queryOrgWork} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Amend",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分类数据
      classList: [],
      //问题列表数据
      blackspotList:[],
      //部门
      deptOptions:[],
      //问题列表
      problems:[],
      //图片地址
      imgPath:undefined,
      //整改图片
      amendimgPath:undefined,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示弹出显示层
      viewopen:false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: {},
      //验收人
      verifyers:[],
      //详情
      blackspostvo:{
        organization:{organName:''},
        orgarea:{areaName:''},
        worker:{workerName:''},
        problemclass:{className:''},
      },
      // 表单校验
      rules: {
        amendDesc: [
          { required: true, message: "整改措施不能为空", trigger: "blur" }
        ],
      
        state: [
          { required: true, message: "状态不能为空", trigger: 'change' }
        ],
       
      },
         //状态
      options: [{
          value: 2,
          label: '整改中'
      }, {
          value: 3,
          label: '整改完成'
      }, {
          value: 5,
          label: '验证失败'
      }],
      //整改方式
      amendTypes:[{
          value: 1,
          label: '立刻整改'
        },{
          value: 2,
          label: '停业停产整改'
        }]
    };
  },
  created() {
    this.getDeptTree();
    this.getList();
    this.getClassList();
  },
  methods: {
    /** 查询问题分类 */
    getClassList() {
      let classparam = {}
      classparam.state=1
      listClass(classparam).then(response => {
          this.classList = response.data;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      let queryparams1 = {};
      listDeptExcludeChild(queryparams1).then(response => {
        let datas = response.data;
        for(let i=0; i<datas.length; i++){
          datas[i].children=[];
        }
        this.deptOptions = this.handleTree(datas, "organId");
      });
    },
       /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    //问题描述
    getList(){
      this.loading = true;
       this.queryParams.orderBy="creationtime desc"
       if(typeof(this.queryParams.state)!="undefined" && this.queryParams.state!==''){

       }else{
        this.queryParams.state=[2,3,5]
       }
       console.log(JSON.stringify(this.queryParams))
      listblackspothand(this.queryParams).then(response => {
          this.blackspotList = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },


    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        blackspotamendId:undefined,
        blackspothandId:undefined,
        amenderId:undefined,
        amendOrg:undefined,
        assistOrg:undefined,
        assisterId:undefined,
        amendPlan:undefined,
        begindate:undefined,
        enddate:undefined,
        amendDesc:undefined,
        causeAnalysis:undefined,
        state:undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.problemId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },


    /**整改按钮操作 */
    handleView(row){
      this.reset();
      this.amendimgPath=undefined
       const blackspothandId = row.blackspothandId || this.ids
      getblackspothand(blackspothandId).then(response => {
        this.blackspostvo = response.data;
       if(typeof(this.blackspostvo.imgPaths)!="undefined"){
          this.imgPath=this.blackspostvo.imgPaths.join(",")
        }
         //查询责任单位人员
        queryOrgWork(this.blackspostvo.blackspotOrgId).then(res=>{
           if(res.code==200){
              this.verifyers=res.data;
            }
        })

        getAmendByHand(blackspothandId).then(response =>{
          console.log(JSON.stringify(response))
          if(typeof(response.data)!="undefined"){
            this.form = response.data
            // this.form.state=this.blackspostvo.state
            if(typeof(this.form.imgPaths)!="undefined"){
              this.amendimgPath=this.form.imgPaths.join(",")
            }
          }
          this.form.blackspothandId=blackspothandId
        })

        this.open = true;
        this.title = "整改";
      });
      
      
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(typeof(this.amendimgPath) !=="undefined" && this.amendimgPath!=="" ){
            this.form.imgPaths=this.amendimgPath.split(",")
          }else{
            this.form.imgPaths = undefined
          }
          console.log(JSON.stringify(this.form))
          if (this.form.blackspotamendId != undefined) {
            updateblackspotamend(this.form).then(response => {
              this.$modal.msgSuccess("整改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addblackspotamend(this.form).then(response => {
              this.$modal.msgSuccess("整改成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },



  }
};
</script>