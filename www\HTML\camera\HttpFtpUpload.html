<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>文件HTTP/FTP上传演示</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../JS/common/html5shiv.min.js"></script>
    <script src="../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>文件HTTP/FTP上传 演示</h1>
        <p>页面演示了如何将拍照的文件，以HTTP或FTP方式，上传到指定的服务器地址。页面会自动打开高拍仪。按【拍照】按钮拍照后，设置好相应的上传URL与参数，就可以上传了。</p>
    </div>

    <div class="col-md-8" style="text-align: center; width:66%;">
        <div style="margin-left:60px; text-align: left; width:66%;">
            <label>设备:</label>
            <select id="cameraInfo" style="width:160px;" disabled onchange="switchCamera()"></select>
            <label style="margin-left:20px">视频格式:</label>
            <select id="mediaType" style="width:95px;" disabled onchange="ReOpenCamera()"></select>
            <button type="button" class="btn btn-primary" id="btnCapture" style="margin-left:20px" onclick="DoCapture()">拍照</button>
            <br>
            <label>分辨率:</label>
            <select id="resolution" style="width:145px;" onchange="ReOpenCamera()"></select>
            <label style="margin-left:20px">裁切方式:</label>
            <select id="crop_type" class="left-two-select" onchange="setCropType()">
                <option value="0">不裁切</option>
                <option value="1" selected>单图裁切</option>
                <option value="2">多图裁切</option>
                <option value="6">桌面裁切</option>
            </select>
        </div>
        <img id="video" src="" style="width:640px;height:480px;">
        <p>文档摄像头视频</p>
    </div>

    <div class="col-md-4" style="text-align:left; padding-left:0; padding-right:0;">
        <div class="boxArea">
            <label>URL:</label>
            <input id="url1" type="text" value="https://www.viisan.com/file/upload" style="width:320px"/><br>
            <label>cookie:</label>
            <input id="cookieVal1" type="text" style="width:305px"/><br>
            <span id="spanParam">
                <label>参数名:</label><input name="ParamN" type="text" class="PN"/><label>参数值:</label><input name="ParamV" type="text" class="PV"/><br>
            </span>
            <button type="button" class="btn btn-xs btn-primary" id="btnAddParam" onclick="addP()" style="margin-left:26%;">增加参数</button>
            <button type="button" class="btn btn-primary" id="btnUpload1" onclick="httpUpload()">HTTP上传</button>
        </div>

        <div class="boxArea">
            <label>URL:</label>
            <input id="url2" type="text" value="ftp://123.57.223.145/myfile/" style="width:320px"/><br>
            <label>用户名:</label>
            <input id="userName" type="text" value="ftptest" style="width:307px"/><br>
            <label>密码:</label>
            <input id="passWd" type="password" value="viisanftptest" style="width:321px"/><br>
            <button type="button" class="btn btn-primary" id="btnUpload2" onclick="ftpUpload()" style="margin-left:45%;">FTP上传</button>
        </div>

        <button type="button" class="btn btn-primary" id="btnClearFile" onclick="clearImageFile()">清空文件</button>
        <div class="col-sm-12" style="padding: 0;margin:4px 0;overflow-y: scroll;height:200px;">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th width="60%" style="text-align: center;">文件名</th>
                    <th style="text-align: center;">操作</th>
                </tr>
                </thead>
                <tbody id="tabBody">
                </tbody>
            </table>
        </div>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<audio id="audioCapture">
    <source src="../../audio/Capture.wav" type="audio/wav">
</audio>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">拍照文件查看</h4>
            </div>
            <div class="modal-body">
                <img id="imgView" src="" style="max-width:860px">
<!--                <img id="imgView2" src="" style="max-width:860px">-->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../langs/zhCN.js"></script>
<script src="../../JS/MzWsConnect.js"></script>
<script src="../../JS/camera/HttpFtpUpload.js"></script>
</body>
</html>