<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- utf-8编码-->
    <meta charset="utf-8">
    <title>CamSDK自动连拍演示</title>
</head>
<body>
<p>这个例子演示了如何自动打开高拍仪、设置自动裁切、自动连拍。左边是实时的视频、右边是拍照出来的图片。</p>
<div>

    <div style="float:left;margin-right:20px;">
        <div id="video1">
            <img id="video" style="width:506px;height:380px">
        </div>
        <div style="margin-top: 20px; margin-left: 20px; ">
            <button onclick="continuCapture()">连拍</button>
            <button onclick="timeCapture()">定时连拍</button>
            <button onclick="StopAutoCapture()">停止连拍</button>
            <br/>
            <br/>
            <progress id="autoCaptureProgress" value="0" max="100"></progress>
        </div>
    </div>

    <div style="float:left;margin-right:20px;">
        <img id="imgCapture" style="width:506px;height:380px">
        <div style="margin-top: 20px; margin-left: 20px; ">
            <div id="captureInfo"></div>
        </div>
    </div>
</div>

<script src="js/autoCaptureDemo.js"></script>

</body>
</html>