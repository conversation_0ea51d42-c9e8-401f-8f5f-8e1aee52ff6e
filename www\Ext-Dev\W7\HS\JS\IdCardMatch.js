var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}


/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "OpenFaceCamera":
            if (jsonObj.result == 0 || jsonObj.result != 9) {
                // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
                document.getElementById("btnOpenClose").innerText = "关闭人像摄像头";
                // 打开多功能终端上的人像摄像头成功后，获取人像摄像头的视频
                SendJson({func:"GetFaceCameraBuff", to:1, enable:"true", reqId:new Date().getTime()});
            } else {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
        case "CloseFaceCamera":
            document.getElementById("btnOpenClose").innerText = "打开人像摄像头";
            break;
        case "GetFaceCameraBuff":
            // 获取到人像摄像头的视频,显示
            DisplayFaceVideo(jsonObj);
            break;
        case "FaceMatch":
            // 获取到人证比对结果
            DisplayFaceMatch(jsonObj);
            break;
        case "FaceLiveDetection":
            DisplayLiveDetection(jsonObj);
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

var _w = 0, _h = 0;
function DisplayFaceVideo(v) {
    if (_w != v.width || _h != v.height) {
        //设置视频显示的宽高（宽度控制为432px）
        $("#headVideo").css('width', '432px').css('height', 432 * v.height / v.width + 'px');
        _w = v.width, _h = v.height;
    }
    //显示视频
    document.getElementById("headVideo").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
}

function DisplayFaceMatch(v) {
    if (v.result == 0) {
        document.getElementById("idCardPic").src = "data:" +  v.mime + ";base64," + v.idCardImageBase64;
        document.getElementById("matchedPic").src = "data:" +  v.mime + ";base64," + v.faceImageBase64;
        displayOutputInfo(2, '比对通过');
    } else {
        document.getElementById("idCardPic").src = "";
        document.getElementById("matchedPic").src = "";
        displayOutputInfo(3, '比对失败: ' + v.errorMsg);
    }
}

function StartMatch() {
    SendJson({func:"FaceMatch", to:1, reqId:new Date().getTime()});
    displayOutputInfo(1, '人证比对中...');
}

function DisplayLiveDetection(v) {
    if (v.result == 0) {
        displayOutputInfo(2, '活体检测结果：' + (v.live ? "是活体" : "不是活体"));
    } else {
        displayOutputInfo(3, '活体检测失败: ' + v.errorMsg);
    }
}

function LiveDetect() {
    SendJson({func:"FaceLiveDetection",outTime:10, liveScore:50, to:1, reqId:new Date().getTime()});
    displayOutputInfo(1, '活体检测中...');
}

// 打开(1)或关闭(0)人像摄像头,无参数时根据按钮上的文字确定
function OpenCloseFaceCamera(val) {
    var open = false;
    if (typeof(val) == "undefined") {
        open = document.getElementById("btnOpenClose").innerText != "关闭人像摄像头";
    } else {
        open = val != 0;
    }
    var now = new Date().getTime();
    if (open) {
        SendJson({func:"OpenFaceCamera", to:1, faceCameraNum:1, reqId: now});
    } else {
        // 关闭前先停掉视频
        SendJson({func:"GetFaceCameraBuff", to:1, enable:"false", reqId: now++});
        SendJson({func:"CloseFaceCamera", to:1, reqId: now});
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

$(function () {
    // 打开多功能终端上的人像摄像头
    OpenCloseFaceCamera(1);
});

$(window).unload(function(){
    // 关闭页面之前,关闭人像摄像头
    SendJson({func:"CloseFaceCamera", to:1, reqId: new Date().getTime()});
});

