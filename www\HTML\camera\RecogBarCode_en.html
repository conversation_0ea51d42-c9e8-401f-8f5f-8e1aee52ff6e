<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>CamSDK barcode recognition demo</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/en.js"></script>
    <script src="../../JS/camera/RecogBarCode.js"></script>
</head>
<body>
<p>This example demonstrates how to do bar code recognition.<br>
It supports Code128, Code39, EAN13, EAN8, UPC-A, UPC-E, Codabar, Interleaved 2 of 5, Code 93, UCC_128, QR code, Data Matrix, Aztec, PDF 417, MaxiCode, RSS-14 and RSS-Expanded.<br>
There are three ways to specify the source image for barcode recognition:<br>
1. specify a picture file path;<br>
2. Encode image file content in Base64 mode;<br>
3. specify the camera, take photos from the camera and then identify.</p>
<p>
    <input type="radio" name="srcType" id="typePath" onchange="onChangeType()" value="1" checked/>
    <label for="typePath" style="margin-right:40px;">Specify image path</label>
    <input type="text" id="imgPath" style="width:600px;">
</p>
<p>
    <input type="radio" name="srcType" id="typeBase64" onchange="onChangeType()" value="2"/>
    <label for="typeBase64" style="margin-right:80px;">Image Base64</label>
    <input type="file" id="imgFile" accept=".jpg,.png,.bmp" onchange="onChangeFile()" style="width: 200px;" disabled>
</p>
<p>
    <input type="radio" name="srcType" id="typeCamera" onchange="onChangeType()"value="3"/>
    <label for="typeCamera" style="margin-right:53px;">Specified Camera</label>
    <select id="cameraInfo" onchange="onChangeCamera()" style="width:200px;" disabled></select>
</p>
<p>
    <button id="RecogBarCode" onclick="recogBarCode()" style="width:160px; margin-left:24px;">Barcode Recognition</button>
</p>
<div style="float:left;margin-right:20px; width:640px;">
    <label class="left-two-lable">Source image or video:</label><br>
    <img id="video" style="width:640px;height:480px">
</div>

<div style="float:left;margin-right:20px;">
    <label class="left-two-lable">Recognition results:</label><br>
    <textarea id="resultArea" style="width: 400px; height: 475px;"></textarea>
</div>
<audio id="audioCapture">
    <source src="../../audio/Capture.wav" type="audio/wav">
</audio>
</body>
</html>