<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="分类" prop="institutionclassId">
        <el-select v-model="queryParams.institutionclassId" placeholder="分类" clearable>
          <el-option v-for="dict in classlist" :key="dict.institutionclassId" :label="dict.className" :value="dict.institutionclassId" />
        </el-select>
      </el-form-item>
      <el-form-item label="文档名称" prop="insName">
        <el-input v-model="queryParams.insName" placeholder="请输入文档名称" clearable />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="institutionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文档编号" align="center" prop="insCode" :show-overflow-tooltip="true" />
      <el-table-column label="文档名称" align="center" prop="insName" :show-overflow-tooltip="true" />
      <el-table-column label="分类" align="center" prop="institutionclass.className" width="100"> </el-table-column>
      <el-table-column label="文档级别" align="center" prop="docLevel" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.docLevel == 1">企业</span>
          <span v-if="scope.row.docLevel == 2">地方</span>
          <span v-if="scope.row.docLevel == 3">行业</span>
          <span v-if="scope.row.docLevel == 4">国家</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" prop="state" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state == 1">发布</span>
          <span v-if="scope.row.state == 2">草稿</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="creationtime" width="100">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除</el-button>
          <el-button size="mini" type="text" icon="el-icon-download" @click="handleDownload(scope.row)">文档查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-s-open" @click="handleRole(scope.row)">权限控制</el-button>
          <el-button size="mini" type="text" icon="el-icon-share" @click="handleShare(scope.row)">分享</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="文档编号" prop="insCode">
          <el-input v-model="form.insCode" placeholder="请输入文档编号" />
        </el-form-item>
        <el-form-item label="文档名称" prop="insName">
          <el-input v-model="form.insName" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="分类" prop="institutionclassId">
          <el-select v-model="form.institutionclassId" placeholder="请选择分类" clearable>
            <el-option v-for="dict in classlist" :key="dict.institutionclassId" :label="dict.className" :value="dict.institutionclassId" />
          </el-select>
        </el-form-item>

        <el-form-item label="文档级别" prop="docLevel">
          <el-select v-model="form.docLevel" placeholder="请选择级别" clearable>
            <el-option v-for="dict in leveloptions" :key="dict.value" :label="dict.label" :value="dict.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="关键词" prop="excerpt">
          <el-input v-model="form.excerpt" placeholder="请输入关键词" />
        </el-form-item>
        <el-form-item label="状态" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio v-for="dict in options" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-row>
          <el-form-item label="文档" prop="filepaths">
            <!-- <file-upload v-model="filepaths" :limit="5" /> -->
            <p v-if="form.fileUrl" style="margin-bottom: 10px">{{ form.fileUrl }}</p>
            <el-button type="primary" @click="toOpenScanner">高拍仪</el-button>
          </el-form-item>
        </el-row>
        <el-form-item label="是否权限控制" prop="isPerm">
          <el-switch v-model="form.isPerm" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="title" :visible.sync="fileopen" width="780px" append-to-body>
      <div v-for="o in filedowns" :key="o.filepath">
        <a :href="o.filepath" target="_blank"> {{ o.fileName }}</a>
      </div>
    </el-dialog>

    <!--  高拍仪组件 -->
    <high-scanner :show-scanner.sync="showScanner" @success="handleScannerSuccess" @cancel="handleScannerCancel" />
  </div>
</template>

<script>
import { listInstitution, listClass, addInstitution, updateInstitution, getInstitution, delInstitution } from "@/api/institution/institution";
import { Base64 } from "js-base64";
import HighScanner from "@/components/HighScanner/index.vue";

export default {
  name: "Notice",
  components: { HighScanner },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 制度化表格数据
      institutionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示下载弹出层
      fileopen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      //制度化分类
      classlist: [],
      //文档路径
      filepaths: [],
      //文档下载路径
      filedowns: [],
      //级别
      leveloptions: [
        {
          value: 1,
          label: "企业",
        },
        {
          value: 2,
          label: "地方",
        },
        {
          value: 3,
          label: "行业",
        },
        {
          value: 4,
          label: "国家",
        },
      ],
      //状态
      options: [
        {
          value: 1,
          label: "发布",
        },
        {
          value: 2,
          label: "草稿",
        },
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        insName: [{ required: true, message: "名称不能为空", trigger: "blur" }],
        institutionclassId: [{ required: true, message: "分类不能为空", trigger: "change" }],
        state: [{ required: true, message: "状态不能为空", trigger: "change" }],
      },
      showScanner: false,
    };
  },
  created() {
    this.getclasslist();
    this.getList();
  },
  methods: {
    // 高拍仪组件显示
    toOpenScanner() {
      this.showScanner = true;
    },
    // 高拍仪成功
    handleScannerSuccess(url) {
      this.form.fileUrl = url;
      this.showScanner = false;
    },
    // 高拍仪取消
    handleScannerCancel() {
      this.showScanner = false;
    },
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      this.queryParams.orderBy = "creationtime desc";
      this.queryParams.noAuth = true;
      listInstitution(this.queryParams).then((response) => {
        this.institutionList = response.data;
        this.total = response.totalCount;
        this.loading = false;
      });
    },
    getclasslist() {
      const queryclass = {};
      queryclass.state = 1;
      listClass(queryclass).then((response) => {
        this.classlist = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        cmsId: undefined,
        cmsclassId: undefined,
        title: undefined,
        corp: undefined,
        subtitle: undefined,
        excerpt: undefined,
        titleImg: undefined,
        type: undefined,
        state: 1,
        content: undefined,
        docLevel: 1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if (this.queryParams.institutionclassId == "") {
        this.queryParams.institutionclassId = undefined;
      }
      if (this.queryParams.insName == "") {
        this.queryParams.insName = undefined;
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.institutionId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.filepaths = [];
      this.open = true;
      this.title = "添加制度化";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.filepaths = [];
      const institutionId = row.institutionId || this.ids;
      getInstitution(institutionId).then((response) => {
        this.form = response.data;
        if (typeof this.form.filepaths != "undefined") {
          for (let i = 0; i < this.form.filepaths.length; i++) {
            let filepath = {};
            filepath.name = this.form.filepaths[i].fileName;
            filepath.url = this.form.filepaths[i].filePath;
            this.filepaths.push(filepath);
          }
        }
        this.open = true;
        this.title = "修改制度化";
      });
    },
    /** 提交按钮 */
    submitForm: function () {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (typeof this.filepaths !== "undefined" && this.filepaths.length > 0) {
            this.form.filepaths = [];
            for (let i = 0; i < this.filepaths.length; i++) {
              let filepath = {};
              filepath.filePath = this.filepaths[i].url;
              filepath.fileName = this.filepaths[i].name;
              this.form.filepaths.push(filepath);
            }
          }
          if (this.form.institutionId != undefined) {
            updateInstitution(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInstitution(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const institutionId = row.institutionId || this.ids;
      this.$modal
        .confirm("是否确认删除文档？")
        .then(function () {
          return delInstitution(institutionId);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /**权限控制 */
    handleRole(row) {
      console.log(row.institutionId);

      let data = { id: "pj1pHjhRL1ss11TT", url: "./xdemui/xj/institution/role/" + row.institutionId, name: "制度化权限", closable: true, openwin: false, backgroundColor: "white" };
      parent.postMessage(data, "*");

      //  this.$router.push("/xj/institution/role/" + row.institutionId);
    },
    handleShare(row) {
      let data = { id: "pj1pHjhRL1ss22TT", url: "./xdemui/xj/institution/share/" + row.institutionId, name: "分享", closable: true, openwin: false, backgroundColor: "white" };
      parent.postMessage(data, "*");

      // this.$router.push("/xj/institution/share/" + row.institutionId);
    },
    /**文档下载弹出操作 */
    handleDownload(row) {
      const institutionId = row.institutionId || this.ids;
      getInstitution(institutionId).then((response) => {
        this.form = response.data;
        if (typeof this.form.filepaths != "undefined") {
          let files = [];
          for (let i = 0; i < this.form.filepaths.length; i++) {
            let filedown = {};
            // filedown.filepath = process.env.VUE_APP_BASE_API+this.form.filepaths[i].filePath;
            console.log(this.form.filepaths[i].filePath);
            let fileurl = encodeURIComponent(Base64.encode(this.form.filepaths[i].filePath));
            console.log(fileurl);
            filedown.filepath = "http://em.xdbg.cn/xdempreview/onlinePreview?url=" + fileurl;
            filedown.fileName = this.form.filepaths[i].fileName;
            files.push(filedown);
          }
          this.filedowns = files;
        }
        this.fileopen = true;
        this.title = "文档查看";
      });
    },
  },
};
</script>
