<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="所属部门" prop="routeOrgId">
          <treeselect v-model="queryParams.routeOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择责任单位"  style="width:200px"/>
      </el-form-item> -->
      <el-form-item label="状态" prop="state">
       <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item label="路线名" prop="routeName">
        <el-input  v-model="queryParams.routeName" placeholder="请输入路线名" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" highlight-current-row  @row-click="handleClick" :height="400">
      <el-table-column label="所属部门" align="center"  prop="organization.organName"  :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="路线名" align="center" prop="routeName"  />
      <el-table-column label="路线编码" align="center" prop="routeCode" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">启用</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>
      
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建人" align="center" prop="creator" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDel(scope.row)"
          >删除</el-button>
          <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" >
            <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="handleUser" icon="el-icon-s-custom" >巡检人员</el-dropdown-item>
              <!--<el-dropdown-item command="handlePrint" icon="el-icon-connection" >打印二维码</el-dropdown-item>-->
              <el-dropdown-item command="handlePlan" icon="el-icon-date" >巡检计划</el-dropdown-item>
              <el-dropdown-item command="handleTemp" icon="el-icon-s-order" >临时任务</el-dropdown-item>
              <el-dropdown-item command="handleCopy" icon="el-icon-copy-document" >复制</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

<!-- 点击显示路线的区域-->
 <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-share"
          size="mini"
          @click="handleAddSite"
        >绑定区域</el-button>
      </el-col>
 </el-row>
    <el-table v-loading="loading" :data="siteList" >
      <el-table-column label="所属部门" align="center"  prop="organization.organName"  :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="区域名" align="center" prop="siteName" :show-overflow-tooltip="true" />
      <el-table-column label="区域编码" align="center" prop="siteCode" :show-overflow-tooltip="true" />
      <el-table-column label="二维码值" align="center" prop="siteCode" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">启用</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
         
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleunbind(scope.row)"
          >解绑</el-button>
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-notebook-2"
            @click="handleitem(scope.row)"
          >检测项</el-button>
         
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="totalitem>0"
      :total="totalitem"
      :page.sync="queryitemParams.pageNum"
      :limit.sync="queryitemParams.pageSize"
      @pagination="getsiteList"
    />

  <!-- 巡检人员列表-->
  <el-dialog :title="title" :visible.sync="openuser" width="700px" append-to-body>
    <el-form :model="userform" ref="userform" size="small" :inline="true"  label-width="68px">
      <el-form-item label="人员" prop="workerId">
       <el-select v-model="workerIds" placeholder="请选择人员" clearable filterable multiple>
        <el-option
          v-for="item in useroptions"
          :key="item.worker.workerId"
          :label="item.worker.workerName+'('+item.worker.workerCode+')'"
          :value="item.worker.workerId">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-plus" size="mini" @click="handleadduser">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="loading" :data="userList"  >
      <el-table-column label="姓名" align="center"  prop="worker.workerName"  :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="工号" align="center"  prop="worker.workerCode"  :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="是否责任人" align="center"  :show-overflow-tooltip="true" >
        <template slot-scope="scope">
          <el-switch v-model="scope.row.ismajor" :active-value=true :inactive-value=false @change="handleStatusChange(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
       <el-table-column label="创建人" align="center" prop="creator" :show-overflow-tooltip="true" />
    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
      <template slot-scope="scope">
         <el-button 
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDeluser(scope.row)"
          >删除</el-button>
      </template>
    </el-table-column>
    </el-table>
 </el-dialog>


 <!--绑定区域 -->
 <el-dialog :title="title" :visible.sync="opensite" width="700px" append-to-body>

    <el-form :model="sitequery"  size="small" :inline="true"  label-width="68px">
     
      <el-form-item label="区域名" prop="siteName">
        <el-input  v-model="sitequery.siteName" placeholder="请输入区域名" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleSiteQuery">搜索</el-button>
      </el-form-item>
    </el-form>


    <el-table v-loading="loading" :data="unsiteList"  @selection-change="handleSiteSelectionChange">
       <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属部门" align="center"  prop="organization.organName"  :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="区域名" align="center" prop="siteName" :show-overflow-tooltip="true" />
      <el-table-column label="区域编码" align="center" prop="siteCode" :show-overflow-tooltip="true" />
    </el-table>
    <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitSite">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
    </div>
 </el-dialog>


    <!-- 添加或修改路线配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        
        <el-form-item label="所属部门" prop="routeOrgId">
          <treeselect v-model="form.routeOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择所属部门"  />
        </el-form-item>
        <el-form-item label="路线名" prop="routeName">
          <el-input  v-model="form.routeName" placeholder="请输入区域名" maxlength="50"/>
        </el-form-item>
        <el-form-item label="路线编码" prop="routeCode">
          <el-input  v-model="form.routeCode" :disabled="true"  />
        </el-form-item>
        <el-form-item label="规定用时(分)" prop="limitTime">
          <el-input type="number"  v-model="form.limitTime" placeholder="请输入规定用时(分)" />
        </el-form-item>
       
        <el-form-item label="状态" prop="state">
              <el-radio-group v-model="form.state" >
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
        </el-form-item>
         <el-form-item label="备注说明" prop="description">
            <el-input  type="textarea" v-model="form.description" placeholder="请输入备注说明" />
         </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

     <!--添加临时任务-->
     <el-dialog :title="title" :visible.sync="tempopen" width="700px" append-to-body>
      <el-form ref="tempform" :model="tempform"  label-width="80px">
        <el-form-item label="开始时间" prop="begintime">
           <el-date-picker v-model="tempform.begintime" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endtime">
          <el-date-picker v-model="tempform.endtime" type="datetime" placeholder="选择日期" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTempForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>


    <!-- 添加或修改巡检计划配置对话框 -->
    <el-dialog :title="title" :visible.sync="planopen" width="700px" append-to-body>
      <el-form ref="planform" :model="planform"  label-width="80px">
        
        <el-form-item label="调度类型" prop="deployType">
          <el-radio-group v-model="planform.deployType" @input="deploychange">
            <el-radio
              v-for="dict in deploys"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="频率" prop="cycle" v-if="showcycle">
          <el-input  type="number" v-model="planform.cycle"   />
        </el-form-item>
        <el-form-item label="频率单位" prop="unit" v-if="showcycle">
           <el-select v-model="planform.unit" placeholder="请选择单位" clearable>
                  <el-option v-for="dict in timeunit" :key="dict.value" :label="dict.label" :value="dict.value"/>
           </el-select>
        </el-form-item>
        <el-form-item label="间隔时长" prop="interval" v-if="showinterval">
           <el-col :span="7">
              <el-input  type="number" v-model="planform.day"   />
           </el-col>
           <el-col :span="1">
            天
           </el-col> 
           <el-col :span="7">
              <el-input  type="number" v-model="planform.hour"   />
           </el-col>
           <el-col :span="1">
            时
           </el-col> 
           <el-col :span="7">
              <el-input  type="number" v-model="planform.minute"   />
           </el-col>
           <el-col :span="1">
            分
           </el-col> 
        </el-form-item>
         <el-form-item label="固定类型" prop="fixedType" v-if="showfixed">
            <el-radio-group v-model="planform.fixedType" @input="deployfixed">
            <el-radio
              v-for="dict in fiextype"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
         </el-form-item>
          <el-form-item label="周循环选择"  v-if="showfixedzhou">
            <ul class="fixedul">
				      <li class="fixedli" v-for="(item,index) in weeks" :key="index" :class="getzhou(item)" @click="addzhou(item)">
                星期<span v-if="item === 1">一</span><span v-if="item === 2">二</span>
                <span v-if="item === 3">三</span><span v-if="item === 4">四</span>
                <span v-if="item === 5">五</span><span v-if="item === 6">六</span>
                <span v-if="item === 7">日</span>
              </li>
			      </ul>
          </el-form-item>
          <el-form-item label="月循环选择"  v-if="showfixedday">
            <ul class="fixedul">
              <li class="fixedli" v-for="(item,index) in 31" :key="index" :class="loadday(item)" @click="addday(item)">
                {{item}}
              </li>
            </ul>
          </el-form-item>
        <el-form-item label="提前计划时间" prop="earlytime" >
          <el-input  type="number" v-model="planform.earlytime"   />
        </el-form-item>
        <el-form-item label="提前时间单位" prop="earlytimeUnit" >
          <el-select v-model="planform.earlytimeUnit" placeholder="请选择单位" clearable>
                  <el-option v-for="dict in timeunit" :key="dict.value" :label="dict.label" :value="dict.value"/>
           </el-select>
        </el-form-item>
        <el-form-item label="计划开始时间" prop="startTime" >
           <el-date-picker v-model="planform.startTime" type="datetime" placeholder="选择时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="计划结束时间" prop="finishTime" >
           <el-date-picker v-model="planform.finishTime" type="datetime" placeholder="选择时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
         <el-form-item label="系统下次计算时间" prop="nextTime" >
           <el-date-picker v-model="planform.nextTime" type="datetime" placeholder="选择时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="状态" prop="state">
              <el-radio-group v-model="planform.state" >
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitPlanForm">确 定</el-button>
      </div>
    </el-dialog>


  </div>
</template>

<script>

import { listline, getline,addline,updateline,delline,bindSite,listuser,adduser,deluser,getuser,updateuser,getplan,addplan,updateplan,addtemptask,copyById,addusers} from "@/api/inspect/line";
import { listsite,unbindSite} from "@/api/inspect/site";
import {  listDeptExcludeChild } from "@/api/system/dept";
import {  listWorkorg } from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Cookies from 'js-cookie'

export default {
  name: "Inspectline",
  components: { Treeselect },
  dicts: ['system_timeunit'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
       // 总条数
      totalitem: 0,
      routeId:undefined,
      orgId:undefined,
      //路线列表数据
      list:[],
      //区域列表数据
      siteList:[],
      //未绑定的区域列表
      unsiteList:[],
      //线路责任人
      userList:[],
      //人员
      useroptions:[],
      //部门
      deptOptions:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示选择区域弹出层
      opensite:false,
      //是否显示弹出显示层
      viewopen:false,
      //是否显示路线责任人
      openuser:false,
      //是否显示巡检计划
      planopen:false,
      //是否显示临时任务
      tempopen:false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
       // 查询参数
      queryitemParams: {
        pageNum: 1,
        pageSize: 10
      },
      sitequery:{},
      // 表单参数
      form: {},
      //绑定人员参数
      userform:{},
      //巡检计划参数
      planform:{
        deployType:1,
        state:1
      },
      //临时任务参数
      tempform:{

      },
      //显示周期输入框
      showcycle:true,
       //显示间隔输入框
      showinterval:false,
      //显示固定输入框
      showfixed:false,
      //显示循环周
      showfixedzhou:false,
      //显示循环日
      showfixedday:false,
      weeks:[1,2,3,4,5,6,7],
      fixedzhou:[],
      fexedday:[],
      currentuserid:undefined,
      workerIds:[],

      // 表单校验
      rules: {
        routeOrgId: [
          { required: true, message: "部门不能为空", trigger: "blur" }
        ],
        routeName: [
          { required: true, message: "路线名不能为空", trigger: "blur" }
        ],
        state: [
          { required: true, message: "状态不能为空", trigger: 'change' }
        ],
        
      },
         //状态
      options: [{
          value: 1,
          label: '启用'
        }, {
          value: 2,
          label: '停用'
      }],
      //调度类型
      deploys: [{
          value: 1,
          label: '周期'
        }, {
          value: 2,
          label: '间隔'
      }, {
          value: 3,
          label: '固定'
      }],
      //固定类型
      fiextype:[{
          value: 1,
          label: '周'
        }, {
          value: 2,
          label: '月'
      }
      ],
      //时间单位
      timeunit: [{
          value: 1,
          label: '分'
        }, {
          value: 2,
          label: '时'
      }, {
          value: 3,
          label: '天'
      }, {
          value: 4,
          label: '周'
      }, {
          value: 5,
          label: '月'
      }, {
          value: 6,
          label: '季度'
      }, {
          value: 7,
          label: '半年'
      }, {
          value: 8,
          label: '年'
      }],
    };
  },
  created() {
    this.currentuserid = this.$store.getters.userid
    this.getDeptTree();
    this.getList();
   
  },
  methods: {

    /** 查询部门下拉树结构 */
    getDeptTree() {
      let queryparams1 = {};
      listDeptExcludeChild(queryparams1).then(response => {
        let datas = response.data;
        for(let i=0; i<datas.length; i++){
          datas[i].children=[];
        }
        this.deptOptions = this.handleTree(datas, "organId");
      });
    },
       /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    //路线列表
    getList(){
      this.loading = true;
       this.queryParams.orderBy="creationtime desc"
       this.queryParams.userId=this.currentuserid
      listline(this.queryParams).then(response => {
          this.list = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },

    //巡检人员列表
    getUserList(routeId){
      this.loading = true;
      let query = {}
      query.routeId = routeId
      listuser(query).then(response => {
          this.userList = response.data;
          this.loading = false;
        }
      );
    },
    //查询组织内人员
    listworkerorg(organId){
      let query = {};
      query.organId=organId
      listWorkorg(query).then(response => {
        console.log(JSON.stringify(response.data))
          this.useroptions = response.data;
        }
      );
    },

    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleUser":
          this.title = ''
          this.openuser = true
          this.userform = {
             routeId: undefined,
             workerId: undefined,
          }
          this.routeId = row.routeId
          this.workerIds = []
          this.getUserList(row.routeId)
          this.listworkerorg(row.routeOrgId)
          break;
        case "handlePrint":
          
          break;
        case "handlePlan":
          this.title = '巡检计划'
          this.routeId = row.routeId
          this.getplan(row.routeId)
          this.planopen=true
          break;
        case "handleTemp":
          this.title = '临时任务'
          this.routeId = row.routeId
          this.tempopen=true
          break;
        case "handleCopy":
          this.title = '复制'
          this.handleCopy(row)
          break;
        default:
          break;
      }
    },

    //复制路线
    handleCopy(row){
       const routeId = row.routeId 
      this.$modal.confirm('是否确认复制路线信息？').then(function() {
        return copyById(routeId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("复制成功");
      }).catch(() => {});
    },

    //查询巡检计划
    getplan(routeid){
      this.resetplanform()
      getplan(routeid).then(response=>{
        console.log(response.data)
        if(typeof(response.data)!="undefined"){
          this.planform =  response.data;
          if(this.planform.deployType===1){
             this.showcycle=true
             this.showinterval=false
             this.showfixed=false
             this.showfixedzhou=false
             this.showfixedday=false
          }else if(this.planform.deployType===2){
            this.showcycle=false
            this.showinterval=true
            this.showfixed=false
            this.showfixedzhou=false
             this.showfixedday=false
          }else{
            this.showcycle=false
            this.showinterval=false
            this.showfixed=true
            if(this.planform.fixedType===1){
              this.showfixedzhou=true
              this.showfixedday=false
              this.fixedzhou=this.planform.fixedTime.split(',')
            }else{
              this.showfixedzhou=false
              this.showfixedday=true
              this.fexedday=this.planform.fixedTime.split(',')
            }
          }
        }else{
          //默认周期
          this.showcycle=true
             this.showinterval=false
             this.showfixed=false
             this.showfixedzhou=false
             this.showfixedday=false
        }
        this.planform.routeId=routeid
        
        
      })
    },

    //调度类型变化
    deploychange(){
      if(this.planform.deployType===1){
        this.showcycle=true
        this.showinterval=false
        this.showfixed=false
        this.showfixedzhou=false
        this.showfixedday=false
      }else if(this.planform.deployType===2){
        this.showcycle=false
        this.showinterval=true
        this.showfixed=false
        this.showfixedzhou=false
        this.showfixedday=false
      }else{
        this.showcycle=false
        this.showinterval=false
        this.showfixed=true;
      }
    },
    //固定类型变化
    deployfixed(){
      if(this.planform.fixedType===1){
        this.showfixedzhou=true
        this.showfixedday=false
      }else{
        this.showfixedzhou=false
        this.showfixedday=true
      }
    },
    getzhou(item){
				let index = this.fixedzhou.findIndex(v=>v==item)
				if(index>-1){
					return { active:true}
				}
		},
    addzhou(item){
      let index = this.fixedzhou.findIndex(v=>v==item)
      if(index==-1){
        this.fixedzhou.push(item)
      }else{
        this.fixedzhou.splice(index, 1);
      }
    },
    loadday(item){
				let index = this.fexedday.findIndex(v=>v==item)
				if(index>-1){
					return { active:true}
				}
		},
    addday(item){
      let index = this.fexedday.findIndex(v=>v==item)
      if(index==-1){
        this.fexedday.push(item)
      }else{
        this.fexedday.splice(index, 1);
      }
    },

     //区域列表
    getsiteList(){
      this.queryitemParams.routeId=this.routeId
      
      listsite(this.queryitemParams).then(response =>{
        this.siteList = response.data;
        this.totalitem = response.totalCount;
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.tempopen = false;
      this.opensite=false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        routeId: undefined,
        routeOrgId: undefined,
        routeCode: undefined,
        routeName: undefined,
        nameEn:undefined,
        nameTh:undefined,
        limitTime:undefined,
        description:undefined,
        state:1,
      };
      this.resetForm("form");
    },
    resetplanform(){
      this.planform={
        deployType:1,
        state:1,
        earlytime:0,
        earlytimeUnit:1
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if(this.queryParams.routeOrgId == ''){
        this.queryParams.routeOrgId=undefined
      }
      if(this.queryParams.state == ''){
        this.queryParams.state=undefined
      }
      if(this.queryParams.routeName == ''){
        this.queryParams.routeName=undefined
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增巡检路线";
    },
    //选择未绑定的区域
    handleAddSite(){
      if(this.orgId != undefined){
        this.sitequery = {};
        this.handleSiteQuery()
        this.opensite=true;
        this.title = "选择区域";
      }else{
         this.$modal.msgError("还未选择路线");
      }
      
    },
    //未绑定的区域查询
    handleSiteQuery(){
        var querysiteparam = {};
        querysiteparam.routeId=null
        querysiteparam.state=1
        querysiteparam.orgId=this.orgId
        if(this.sitequery.siteName != undefined && this.sitequery.siteName != ""){
          querysiteparam.siteName=this.sitequery.siteName
        }
        listsite(querysiteparam).then(response =>{
          this.unsiteList = response.data;
        })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.siteId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
      // 多选框选中数据
    handleSiteSelectionChange(selection) {
      this.ids = selection.map(item => item.siteId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    //表格点击
    handleClick(row){
      const routeId = row.routeId
      this.routeId = routeId
      this.getsiteList()
      this.routeId=routeId
      this.orgId=row.routeOrgId
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();

      const routeId = row.routeId || this.ids
      getline(routeId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改路线";
      });
    },
    /**删除巡检人员 */
    handleDeluser(row){
      const routeUserId = row.routeUserId
      this.$modal.confirm('是否确认巡检人员？').then(function() {
        return deluser(routeUserId);
      }).then(() => {
        this.getUserList(this.routeId);
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

        // 用户状态修改
    handleStatusChange(row) {
      getuser(row.routeUserId).then(response => {
        let user = response.data;
        if(user.ismajor){
          user.ismajor=false;
        }else{
            user.ismajor=true;
        }
        updateuser(user).then(response => {
          
        });
      });
   
    },


    /**删除按钮操作 */
    handleDel(row){
      const routeId = row.routeId || this.ids
      this.$modal.confirm('是否确认删除路线信息？').then(function() {
        return delline(routeId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**解绑路线 */
    handleunbind(row){
      const siteId = row.siteId
      this.$modal.confirm('是否确认解绑？').then(function() {
        return unbindSite(siteId);
      }).then(() => {
        this.routeId=row.routeId;
        this.getsiteList();
        this.$modal.msgSuccess("解绑成功");
      }).catch(() => {});
    },
    /**检测项 */
    handleitem(row){
      const siteId = row.siteId
      let data = {id: 'pj1pHjhR22CC11TT', url:'./xdemui/xj/inspect/item1/'+siteId, name: '检测项', closable: true, openwin: false,backgroundColor: 'white'};
      parent.postMessage(data, "*");


      // this.$router.push("/xj/inspect/item1/" + siteId);
    },

    //新增巡检人员
    handleadduser(){
      console.log(this.workerIds)
      let roleusers = []
      for(let i=0; i<this.workerIds.length; i++){
        let roleuser = {};
        roleuser.workerId=this.workerIds[i]
        roleuser.routeId=this.routeId;
        roleuser.ismajor = false
        roleusers.push(roleuser)
         
      }
      if(roleusers.length > 0){
        addusers(roleusers).then(response => {
          this.workerIds = []
          this.$modal.msgSuccess("新增成功");
          this.getUserList(this.routeId);
        });
      }
      
      
    
      // if(typeof(this.userform.workerId)!=="undefined" && this.userform.workerId != ''){
          
      // }
        
    },

    //确定选择区域
    submitSite(){
      console.log(JSON.stringify(this.ids))
      if(this.routeId==undefined){
         this.$modal.msgError("未选中巡检路线");
      }else{
        if(this.ids.length==0){
           this.$modal.msgError("未选中巡检区域");
        }else{
          let binddata = {};
          binddata.routeId=this.routeId;
          binddata.siteIds=this.ids;
           bindSite(binddata).then(response => {
             this.$modal.msgSuccess("绑定成功");
             this.routeId=this.routeId;
             this.getsiteList();
             this.opensite=false
          });
        }
      }
    },

    /**添加临时任务 */
    submitTempForm:function(){
        this.tempform.routeId=this.routeId
        addtemptask(this.tempform).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.tempopen = false;
          this.getList();
        });
    },

    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
         
          if (this.form.routeId != undefined) {
            updateline(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addline(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },


      /** 巡检计划提交按钮 */
    submitPlanForm: function() { 
      
      if(this.planform.deployType===3){
        //固定
        if(this.planform.fixedType != undefined){
            if(this.planform.fixedType === 1){
              if(this.fixedzhou.length === 0){
                 this.$modal.msgSuccess("请选择日期");
              }else{
                this.planform.fixedTime= this.fixedzhou.join(',');
              }
            }else{
              if(this.fexedday.length === 0){
                 this.$modal.msgSuccess("请选择日期");
              }else{
                this.planform.fixedTime=this.fexedday.join(',');
              }
            }
        }else{
           this.$modal.msgSuccess("请选择固定类型");
        }
      }else{
        this.planform.fixedType=undefined
      }
      if(this.planform.deployType===2){
        //间隔
        if(this.planform.minute == undefined ){
          this.$modal.msgError("间隔分钟未填");
          return
        }
      }
      if(this.planform.deployType===1){
        //周期
         if(this.planform.cycle == undefined || this.planform.unit == undefined){
          this.$modal.msgError("周期频率未填");
          return
        }
      }
      if(this.planform.earlytime == undefined || this.planform.earlytimeUnit == undefined){
          this.$modal.msgError("提前时间未填");
          return
      }
      if(this.planform.startTime == undefined ){
          this.$modal.msgError("开始时间未填");
          return
      }
      if(this.planform.finishTime == undefined ){
          this.$modal.msgError("结束时间未填");
          return
      }
      
      if (this.planform.planId != undefined) {
        updateplan(this.planform).then(response => {
          this.$modal.msgSuccess("修改成功");
          this.planopen=false
             this.getList();
        });
      } else {
        addplan(this.planform).then(response => {
          this.$modal.msgSuccess("新增成功");
          this.planopen=false
             this.getList();
        });
      }
       
      
    },



  }
};
</script>
<style scoped lang="scss">
    .fixedul{
			// padding-top: 50px;
      padding-left: 0px;
			display: flex;
      flex-wrap: wrap;
		}
		.fixedli{
			width: 70px;
			height: 40px;
      margin-right: 2px;
			list-style: none;
			border: 1px solid;
			text-align: center;
		}
		.active{
			background-color: #409EFF;
		}
    ::v-deep .el-radio input[aria-hidden="true"] {
      display: none !important;
    }
    
    ::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
      box-shadow: none !important;
    }
</style>