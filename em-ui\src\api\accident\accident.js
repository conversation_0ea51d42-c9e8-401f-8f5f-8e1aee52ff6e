import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询事故上报列表
export function listHand(query) {
    return request({
      url: '/accidenthand/doQuery',
      method: 'post',
      data: query
    })
}

// 查询发送人员列表
export function listPushuser(query) {
  return request({
    url: '/accidentuser/doQuery',
    method: 'post',
    data: query
  })
}

// 查询事故上报详细
export function getHand(accidenthandId) {
    return request({
      url: '/accidenthand/' + parseStrEmpty(accidenthandId),
      method: 'get'
    })
}

// 查询事故调查详细
export function loadExamine(accidenthandId) {
  return request({
    url: '/accidentexamine/loadExamine',
    method: 'get',
    params:{"accidenthandId":accidenthandId}
  })
}

// 新增事故上报
export function addHand(data) {
    return request({
      url: '/accidenthand/doAdd',
      method: 'post',
      data: data
    })
}

// 新增事故调查
export function addExamine(data) {
  return request({
    url: '/accidentexamine/doAdd',
    method: 'post',
    data: data
  })
}

// 新增发送人员
export function addPushuser(data) {
  return request({
    url: '/accidentuser/doAdd',
    method: 'post',
    data: data
  })
}


// 修改事故上报
export function updateHand(data) {
  return request({
    url: '/accidenthand/doEdit',
    method: 'post',
    data: data
  })
}

// 修改事故调查
export function updateExamine(data) {
  return request({
    url: '/accidentexamine/doEdit',
    method: 'post',
    data: data
  })
}

// 删除事故上报
export function delHand(accidenthandId) {
  return request({
    url: '/accidenthand/doDelete/' + parseStrEmpty(accidenthandId),
    method: 'post'
  })
}


// 删除发送人员
export function delPush(accidentuserId) {
  return request({
    url: '/accidentuser/doDelete/' + parseStrEmpty(accidentuserId),
    method: 'post'
  })
}
