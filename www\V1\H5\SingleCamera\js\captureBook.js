var ws = null;
var m_isConnectWS = false;
var m_splitTag = "$*$";
var m_closed = false; //是否被关闭了
var _cropBox = null;

function isIE() {
    if (!!window.ActiveXObject || "ActiveXObject" in window)
        return true;
    else
        return false;
}

Date.prototype.toLocaleString = function () {
    var y = this.getFullYear(), m = this.getMonth() + 1, d = this.getDate(), h = this.getHours(),
        mi = this.getMinutes(), s = this.getSeconds(), ms = this.getMilliseconds();
    return y + '-' + (m < 10 ? ('0' + m) : m) + '-' + (d < 10 ? ('0' + d) : d) + ' ' + (h < 10 ? ('0' + h) : h) + ':' + (mi < 10 ? ('0' + mi) : mi) + ":" + (s < 10 ? ('0' + s) : s) + '.' + ((ms + 1000) + "").substr(1, 3);
};

//加载页面完成
window.onload = function () {
    //document.getElementById("video").offsetLeft;
    drawHalfLine();
    if (ws == null) {
        StartWebSocket();
    }
    _cropBox = new CropBox("video");
}

//页面卸载
window.onunload = function () {
    if (m_closed) {
        return;
    }
    //反初始化
    sendWsMessage({FuncName: 'camUnInitCameraLib'});
}

function OnManualCropChange() {
    var obj = document.getElementById("manualCrop");
    var btn = document.getElementById("btnCapture");
    var cv = document.getElementById("cv");
    if (obj.checked) {
        _cropBox.enable();
        btn.innerText = "拍  照";
        cv.style.display = "none";
    } else {
        _cropBox.disable();
        btn.innerText = "拍  书";
        cv.style.display = "";
    }
}

function drawHalfLine() {
    var cv = document.getElementById("cv");
    var w = cv.clientWidth, h = cv.clientHeight;
    cv.width = w;
    cv.height = h;
    var ctx = cv.getContext("2d");
    ctx.strokeStyle = 'red';
    //ctx.fillRect(0,0,100,100);
    ctx.beginPath();
    ctx.moveTo(w/2,0);
    ctx.lineTo(w/2, h);

    // ctx.moveTo(0,w / 2);
    // ctx.lineTo((h-1),w / 2);
    ctx.stroke();
}

//开启webSocket
function StartWebSocket() {
    var url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    if ('WebSocket' in window) {
        ws = new WebSocket(url);
    } else if ('MozWebSocket' in window) {
        ws = new MozWebSocket(url);
    } else {
        alert("浏览器版本过低，请升级您的浏览器。\r\n浏览器要求：IE10+/Chrome14+/FireFox7+/Opera11+");
    }

    ws.onopen = function () {
        m_isConnectWS = true;
        sendWsMessage({FuncName: 'camRegCallBackDeviceChange'});
        sendWsMessage({FuncName: 'camInitCameraLib'});
        m_closed = false;
    };

    ws.onmessage = function (evt) {
        if (typeof (evt.data) == "string") {
            var str = evt.data;
            if (str.length <= 0) {
                return;
            }
            if (str.indexOf(m_splitTag) >= 0) {
                //视频的每一帧
                var strs = new Array();
                strs = str.split(m_splitTag);
                setImageWithBase64(strs[1]);
            } else {
                //处理其他请求
                console.log(str);
                handleJsonStrMessage(str);
            }
        }
    };

    ws.onclose = function () {
        m_isConnectWS = false;
        var myimg = document.getElementById("video");
        myimg.src = "images/load1.gif";
        StartWebSocket();
    };
}

function sendWsMessage(jsonObj) {
    var jsonStr = JSON.stringify(jsonObj);
    ws.send(jsonStr);
}

// 处理命令返回的结果
function handleJsonStrMessage(str) {
    var json = JSON.parse(str);
    var re = json.result;
    switch (json.FuncName) {
        case "camInitCameraLib":
            //打开摄像头
            sendWsMessage({FuncName: 'camOpenDev', argument: {devIndex: 0, subtype: 0, width: 0, height: 0}});
            //开始预览
            sendWsMessage({FuncName: 'camStartPreview'});
            break;

        case "camOpenDev":
            if (re != 0) {
                alert("打开摄像头失败, result = " + re);
            }
            break;

        case "camCaptureBookEx":
            //拍书结果显示
            DisplayBookPage(json);
            break;

        case "camCaptureImageFile":
            DisplayImage(json);
            break;
    }
}

//拍书 或 拍照
function doCapture() {
    var obj = document.getElementById("manualCrop");
    if (obj.checked) {
        //// 设置手动裁切区域，再拍照
        // 获取CropBox的数组:[视频框的宽, 视频框的高, 框1的left, 框1的top, 框1的right, 框1的bottom, 框2的left, 框2的top, ....]
        var cropBoxPosArray = _cropBox.getCropBoxArray();
        if (cropBoxPosArray.length >= 6) {
            // 至少有一个裁切框，使用裁切框
            sendWsMessage({FuncName: 'camSetImageManualCrop', argument: {posArray: cropBoxPosArray}});
        } else {
            // 无裁切框，不裁切
            sendWsMessage({FuncName: 'camSetImageAutoCrop', argument: {CropType: 0}});
        }
        // 拍照,返回base64与文件路径
        sendWsMessage({FuncName: 'camCaptureImageFile', argument: {mode: 'base64&path'}});
    } else {
        // 拍书
        sendWsMessage({FuncName: 'camCaptureBookEx'});
    }
}

//拍书结果显示
function DisplayBookPage(json) {
    if (json.result == 0) {
        if (json.pageLBase64Str) {
            document.getElementById("imgPageL").src = "data:image/jpg;base64," + json.pageLBase64Str;
        } else {
            document.getElementById("imgPageL").src = "";
        }
        if (json.pageRBase64Str) {
            document.getElementById("imgPageR").src = "data:image/jpg;base64," + json.pageRBase64Str;
        } else {
            document.getElementById("imgPageR").src = "";
        }
    } else {
        alert("拍书失败, result = " + json.result);
    }
}

function DisplayImage(v) {
    if (v.result == 0) {
        var size = v.imgBase64.length;
        var lp = document.getElementById("imgPageL"), rp = document.getElementById("imgPageR");
        lp.src = "", rp.src = "";

        // 删除之前的第3张及以后的图片
        var divOther = document.getElementById("otherImg");
        divOther.innerHTML = "";

        if (size >= 1) {
            lp.src = "data:image/jpg;base64," + v.imgBase64[0];
            if (size >= 2) {
                rp.src = "data:image/jpg;base64," + v.imgBase64[1];
                for (var i = 2; i < size; i++) {
                    var img = document.createElement("img");
                    img.src = "data:image/jpg;base64," + v.imgBase64[i];
                    img.className = 'pic';
                    divOther.appendChild(img);
                    img = null;
                }
            }
        }
    }
}

//显示每一帧
function setImageWithBase64(str) {
    var myImg = document.getElementById("video");
    myImg.src = "data:image/jpg;base64," + str;
}
