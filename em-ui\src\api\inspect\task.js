import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询任务列表
export function listtask(query) {
    return request({
      url: '/inspecttask/doQuery',
      method: 'post',
      data: query
    })
}

  // 查询任务详细
export function gettask(taskId) {
    return request({
      url: '/inspecttask/' + parseStrEmpty(taskId),
      method: 'get'
    })
}

// 查询巡检记录
export function loadrecord(query) {
    return request({
      url: '/inspectrecord/queryOne',
      method: 'post',
      data: query
    })
}