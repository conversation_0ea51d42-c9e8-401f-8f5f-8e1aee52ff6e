<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>相机视频设置</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/zhCN.js"></script>
    <script src="../../JS/camera/CameraVideoSetting.js"></script>
    <script src="../../JS/camera/Message.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/CameraVideoSetting.css"/>
    <link rel="stylesheet" href="../../CSS/camera/Message.css"/>
</head>
<body>
    <table class="fullTable">
        <tr>
            <td id="td1" class="left" valign="top" align="center" rowspan="2">
                <div class="left-one">
                    <div class="left-one-left">
                        <img id="cameraImage" src="../../image/camera.png"/>
                    </div>
                    <div class="left-one-right">
                        <span>相机视频设置</span>
                    </div>
                </div>
                <div class="left-two">
                    <div>
                        <label class="left-two-lable">设备名称:</label>
                        <select id="cameraInfo" class="left-two-select" onchange="switchCameraInfo_Fun()"></select>
                    </div>
                    <div id="divVideoCtrl">
                        <div style="text-align:left;">视频设置:<div class="divAuto">自动</div></div>
                        <table id="tab1" class="ctrlTable"></table>
                        <label id="lb6" for="chk6" style="margin-left:10px;">启用颜色</label>
                        <input type="checkbox" id="chk6" style="margin-right:55px;">
                        <label id="lbPowerFreq" for="powerFreq">电源频率</label>
                        <select id="powerFreq" onchange="onChangePowerFreq()">
                            <option value="50">50Hz</option>
                            <option value="60">60Hz</option>
                        </select><br>
                        <button id="btnDefaultVideo" onclick="restoreDefault(1)">默认值</button>
                    </div>
                    <div id="divCameraCtrl">
                        <div style="text-align:left;">相机控制:<div class="divAuto">自动</div></div>
                        <table id="tab2" class="ctrlTable"></table>
                        <label id="lb105" for="chk6" style="margin-left:15px;">低照度补偿</label>
                        <input type="checkbox" id="chk105" style="margin-right:205px;"><br>
                        <button id="btnDefaultCamera" onclick="restoreDefault(2)">默认值</button>
                    </div>
                </div>
            </td>
            <td id="td2" class="middle">
                <img id="pic"/>
            </td>
            <td class="right" valign="top" rowspan="2" style="line-height: 28px;">
                <label for="languages">OCR语言:</label>
                <select id="languages" style="width: 265px;"></select><br>
                <label for="extNames">OCR输出文件格式:</label>
                <select id="extNames" style="width: 99px;"></select>
                <button id="OCR" onclick="ocrFile()">OCR文字识别</button><br>
                <label class="left-two-lable">拍照文件列表:</label>
                <button id="btnMergePDF" onclick="mergePDF()" style="margin-left:82px;">合并PDF</button>
                <button id="btnClearFile" onclick="clearImageFile()">清空文件</button><br>
                <table id="tbFile"><tr><th>文件名</th><th>操作</th></tr></table>
            </td>
        </tr>
        <tr>
            <td id="td4" class="bottom" align="center" valign="middle" >
                <button id="CaptureBase64" onclick="cameraCapture_Fun()">拍照</button>
            </td>
        </tr>
    </table>
    <div id="divView" style="display:none;">
        <div>
            <button onclick="closeView()">关闭查看</button>
        </div>
        <img id="imgView">
    </div>
    <audio id="audioCapture"><source src="../../audio/Capture.wav" type="audio/wav"></audio>
</body>
</html>
