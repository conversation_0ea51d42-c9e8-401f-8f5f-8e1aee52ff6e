﻿


//开启webSocket
function StartWebSocket(){
	 var url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
	if ('WebSocket' in window) {
            ws = new WebSocket(url);
    } else if ('MozWebSocket' in window){
        ws = new MozWebSocket(url);
    } else {
		alert("浏览器版本过低，请升级您的浏览器。\r\n浏览器要求：IE10+/Chrome14+/FireFox7+/Opera11+");
	}
   
	ws.onopen = function()
   {
      m_isConnectWS = true;
	  unload();
	  sendInitMsg();//初始化
	  sendGetPath(); //获取电脑中的路径
	  m_closed = false;
   };
	
	
   ws.onmessage = function (evt) 
   { 
   	
   	
   	if(typeof(evt.data)=="string"){
   		
   		var str = evt.data;
   		
   		if(str.length <= 0){
   			
   			return;
   		}
		
		if(str.indexOf("FileEncodeBase64") >=0){
			
			var strs= new Array(); 
			strs=str.split(m_splitTag);
			var baseStr = strs[2];
			if(IDCardFaceRecogin.encodeBase64Tag == 1){

                IDCardFaceRecogin.ShowRealPic(baseStr,true);
                var jsonObj = {FuncName:'camDeleteFile',argument:{FilePath:IDCardFaceRecogin.realPicPath}};
                sendWsMessage(jsonObj);

			}else if(IDCardFaceRecogin.encodeBase64Tag == 0){

                IDCardFaceRecogin.ShowComparePic(baseStr,true);
                IDCardFaceRecogin.compareBase64 = baseStr;

			}	
			return;
		}
		
		if(str.indexOf(m_splitTag)>=0){
			//视频的每一帧
			var strs= new Array();
			strs=str.split(m_splitTag); 
			setImageWithBase64(strs[1]);
		}else{
			//处理其他请求
			console.log(str);
			handleJsonStrMessage(str);
		}
		
		
   		
   	}
  
 	};
	
   ws.onclose = function()
   { 
      m_isConnectWS = false;
	var myimg = document.getElementById("video");
	myimg.src = "images/load1.gif";
		StartWebSocket();
   };
	
}

function sendWsMessage(jsonObj){
	var jsonStr = JSON.stringify(jsonObj);
	ws.send(jsonStr);
}

function handleJsonStrMessage(str){
	
	
	var jsonOBJ = JSON.parse(str);
	var name = jsonOBJ.FuncName;
	var re = jsonOBJ.result;
	//初始化
	if( name == "camInitCameraLib"){

			//openDev();
		

		var jsonObj = {FuncName:'camInitFace',argument:{}};
		sendWsMessage(jsonObj);


		 jsonObj = {FuncName:'camGetDevCount',argument:{}};
		sendWsMessage(jsonObj);
		


			//获取设备名
		jsonObj = {FuncName:'camGetDevName'};
		sendWsMessage(jsonObj);

			
	}
	//打开设备
	else if(name == "camOpenDev"){
		
		if(re == 0){

			//获取分辨率
			var jsonObj = {FuncName:'camGetResolution'};
			sendWsMessage(jsonObj);

			 jsonObj = {FuncName:'camSetImageAutoCrop',argument:{CropType:5}};
			sendWsMessage(jsonObj);

			 jsonObj = {FuncName:'camSetLivingBodyState',argument:{bOpen:1}};
			sendWsMessage(jsonObj);

		}else{
			alert("打开失败" + re);
		}
		
	}
	//获取设备名
	else if(name == "camGetDevName"){

		configureDevInfo(re);
		
	}
	//设备个数
	else if(name == "camGetDevCount")
	{
		
		if(re <= 1)
		{
			IDCardFaceRecogin.devIndex = 0;
		}
		else{
			IDCardFaceRecogin.devIndex = 0;
		}
		openDev();
	}
	
	//获取分辨率
	else if(name == "camGetResolution"){
		
		configureRestionInfo(re);
	}
	//设置分辨率
	else if(name == "camSetResolution"){
		
		if(re !=0){
			
			alert("设置分辨率失败");
		}
	}
	//拍照
	else if(name == "camCaptureImageFile"){
		
		if(re != 0){
			
			alert("拍照失败");
		}
		else
		{
			retCapture = re;
               
		}
		
		
	}
	//自动裁切
	else if(name == "camSetImageAutoCrop"){
		if(re != 0){
			
			alert("自动裁切失败");
		}
	}


	//初始化人脸识别模块
	else if(name == "camInitFace"){
		
		if(re == "0"){
			//alert("初始化人脸识别成功");
		}else {
			alert("初始化人脸识别失败");
		}
		
	}
	//反初始化人脸识别模块
	else if(name == "camUnInitFace"){
		
		alert(re);
	}
//比对视频
	else if(name == "camMatchFaceByFileVideo"){
		alert(re);
		
	}
	//异步base64比对
	else if(name == "camStartAsyMatchFaceBase64Video")
	{
		alert(re);
	}
	
	//匹配图片
	else if(name == "camMatchFaceByFile") {
		
			var score = parseInt(re);
			if(score>=0 && score<=100){
				
				alert("分数：" + re);			
			}else{
				
				alert("error：" + re);
			}				
		
	}


	else if (name == "idcardrfidReadIDCardEx")
	{
		//alert(re);
		var strs= new Array();
		strs=re.split("|");
		IDCardFaceRecogin.compareBase64 = strs[9];
		IDCardFaceRecogin.ShowComparePic(IDCardFaceRecogin.compareBase64,true);

	}
	else if(name == "camMatchFaceByBase64Video")
	{
	    IDCardFaceRecogin.EncodeBase64(IDCardFaceRecogin.realPicPath,1);

		if (re > 100 || re < 0) {

			IDCardFaceRecogin.ShowCompareReslut(re == 10014 ? "非活体":"比对失败ErrorCode：" + re,true);

		}else {

			IDCardFaceRecogin.ShowCompareReslut("比对分数：" + re,false);
		}

	}

}

	
	

