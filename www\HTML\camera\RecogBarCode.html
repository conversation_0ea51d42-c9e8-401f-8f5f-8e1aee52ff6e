<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>CamSDK条码识别演示</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/zhCN.js"></script>
    <script src="../../JS/camera/RecogBarCode.js"></script>
</head>
<body>
<p>这个页面演示了如何进行条码识别。<br>
支持的条码格式有：Code128, Code39, EAN13, EAN8, UPC-A, UPC-E, Codabar, Interleaved 2 of 5, Code 93, UCC_128, QR code, Data Matrix, Aztec, PDF 417, MaxiCode, RSS-14 and RSS-Expanded.<br>
条码识别时有三种指定源图片的方式：1、指定一个图片文件路径；2、以base64方式编码图片文件内容；3、指定摄像头，从摄像头拍照然后再进行识别。</p>
<p>
    <input type="radio" name="srcType" id="typePath" onchange="onChangeType()" value="1" checked/>
    <label for="typePath" style="margin-right:50px;">指定图片路径</label>
    <input type="text" id="imgPath" style="width:600px;">
</p>
<p>
    <input type="radio" name="srcType" id="typeBase64" onchange="onChangeType()" value="2"/>
    <label for="typeBase64" style="margin-right:60px;">图片Base64</label>
    <input type="file" id="imgFile" accept=".jpg,.png,.bmp" onchange="onChangeFile()" style="width: 200px;" disabled>
</p>
<p>
    <input type="radio" name="srcType" id="typeCamera" onchange="onChangeType()"value="3"/>
    <label for="typeCamera" style="margin-right:67px;">指定摄像头</label>
    <select id="cameraInfo" onchange="onChangeCamera()" style="width:200px;" disabled></select>
</p>
<p>
    <button id="RecogBarCode" onclick="recogBarCode()" style="width:80px; margin-left:24px;">条码识别</button>
</p>
<div style="float:left;margin-right:20px; width:640px;">
    <label class="left-two-lable">源图片或视频:</label><br>
    <img id="video" style="width:640px;height:480px">
</div>

<div style="float:left;margin-right:20px;">
    <label class="left-two-lable">识别结果:</label><br>
    <textarea id="resultArea" style="width: 400px; height: 475px;"></textarea>
</div>
<audio id="audioCapture">
    <source src="../../audio/Capture.wav" type="audio/wav">
</audio>
</body>
</html>