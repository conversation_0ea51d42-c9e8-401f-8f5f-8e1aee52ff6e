import request from '@/utils/request'

// 查询字典数据列表
export function listData(type) {
  return request({
    url: '/dict/type/'+type,
    method: 'get'
  })
}

// 查询字典数据详细
export function getData(dictId) {
  return request({
    url: '/dict/' + dictId,
    method: 'get'
  })
}

// 根据字典类型查询字典数据信息
export function getDicts(dictType) {
  return request({
    url: '/dict/type/' + dictType,
    method: 'get'
  })
}

// 新增字典数据
export function addData(data) {
  return request({
    url: '/dict/doAdd',
    method: 'post',
    data: data
  })
}

// 修改字典数据
export function updateData(data) {
  return request({
    url: '/dict/doEdit',
    method: 'post',
    data: data
  })
}

// 删除字典数据
export function delData(dictId) {
  return request({
    url: '/dict/doDelete/' + dictId,
    method: 'post'
  })
}
