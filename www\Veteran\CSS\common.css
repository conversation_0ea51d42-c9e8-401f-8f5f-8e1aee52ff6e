body {
    padding-top: 5px;
    padding-bottom: 30px;
}

.theme-dropdown .dropdown-menu {
    position: static;
    display: block;
    margin-bottom: 20px;
}

.theme-showcase > p > .btn {
    margin: 5px 0;
}

.theme-showcase .navbar .container {
    width: auto;
}

.jumbotron {
    padding-top: 10px;
    padding-bottom: 10px;
}

.jumbotron h1 {
    font-size: 32px;
    margin-top: 10px;
}

.jumbotron p {
    margin-bottom: 10px;
    font-size: 16px;
}

#tabBody a {
    background-color: #337ab7;
    border-color: #2e6da4;
    color: #fff;
    font-size: 12px;
    border-radius: 4px;
    padding: 3px 6px;
    text-decoration: none;
    cursor: pointer;
}

#appName {
    width: 320px;
}

#product {
    width: 240px;
}

.rotateImgDiv {
    text-align: center;
    height: 500px;
    padding-top: 54px;
}

.rotateImgDiv img {
    height: 324px;
    width: 432px;
    margin-bottom: 54px;
    -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
}

#idCardPic {
    width: 112px;
    height: 138px;
    margin-top: 140px;
}

.cameraPU {
    width: 150px !important;
    display: inline-block !important;
    margin-top: 4px;
}

/* Switch开关样式 */
input[type='checkbox'].switch {
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    position: relative;
    width: 38px;
    height: 18px;
    background: #ccc;
    border-radius: 10px;
    transition: border-color .3s, background-color .3s;
}

input[type='checkbox'].switch::after {
    content: '';
    display: inline-block;
    width: 14px;
    height: 14px;
    border-radius: 14px;
    background: #fff;
    box-shadow: 0 0 2px #999;
    transition: .4s;
    position: absolute;
    top: 2px;
    left: 2px;
}

input[type='checkbox'].switch:checked {
    background: rgb(19, 206, 102);
}

/* 当input[type=checkbox]被选中时：伪元素显示下面样式 位置发生变化 */
input[type='checkbox'].switch:checked::after {
    content: '';
    position: absolute;
    left: 56%;
    top: 2px;
}

.demo-canvas-centerlize {
    background-color: #9d9d9d;
    position: absolute;
    left: -180px;
    top: -180px;
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
}

#tabBody2 td {
    word-break: break-all;
}

#vertical-line {
    position: absolute;
    height: 500px;
    left: 400px;
    top: 4px;
    border-right: 3px solid green;
    z-index: 999;
}
