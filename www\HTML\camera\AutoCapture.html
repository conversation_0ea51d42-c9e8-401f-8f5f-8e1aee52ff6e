<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>CamSDK自动连拍演示</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/zhCN.js"></script>
    <script src="../../JS/camera/AutoCapture.js"></script>
</head>
<body>
<p>这个例子演示了如何自动打开高拍仪、设置自动裁切、自动连拍。左边是实时的视频、右边是拍照出来的图片。</p>
<div>
    <div style="float:left;margin-right:20px;">
        <div id="video1">
            <img id="video" style="width:506px;height:380px">
        </div>
        <div style="margin-top: 20px; margin-left: 20px; ">
            <button onclick="autoCaptureControl(0)">自动连拍</button>
            <button onclick="autoCaptureControl(1)">定时连拍</button>
            <button onclick="autoCaptureControl(-1)">停止连拍</button>
            <label style="padding-left:20px;">连拍速度: <span id="speedNum">6</span>
                <input id="speed" type="range" min="4" max="12" value="6" onchange="onSpeedChange()"/>
            </label>
            <br/>
            <br/>
            <div id="divTimer" style="display:none;">
                定时连拍倒计时：
                <progress id="progress0" value="0" max="100"></progress>
                <br/>
                <br/>
            </div>
            <span id="status"></span>
            <audio id="audioCapture">
                <source src="../../audio/Capture.wav" type="audio/wav">
            </audio>
        </div>
    </div>

    <div style="float:left;margin-right:20px;">
        <img id="imgCapture" style="width:506px;height:380px">
        <div style="margin-top: 20px; margin-left: 20px; ">
            <div id="captureInfo"></div>
        </div>
    </div>
</div>

</body>
</html>