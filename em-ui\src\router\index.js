import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login'),
    hidden: true
  },
  {
    path: '/register',
    component: () => import('@/views/register'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error/404'),
    hidden: true
  },
  {
    path: '/401',
    component: () => import('@/views/error/401'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: 'index',
    children: [
      {
        path: 'index',
        component: () => import('@/views/index'),
        name: 'Index',
        meta: { title: '首页', icon: 'dashboard', affix: true }
      }
    ]
  },

  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:dicttypeId(\\d+)',
        component: () => import('@/views/system/dict/data'),
        name: 'Data',
        meta: { title: '字典数据', activeMenu: '/system/dict' }
      }
    ]
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'org/:userId(\\d+)',
        component: () => import('@/views/system/user/authOrg'),
        name: 'UserOrg',
        meta: { title: '所属组织', activeMenu: '/system/user' }
      }
    ]
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'credent/:userId(\\d+)',
        component: () => import('@/views/system/credent/usercredent'),
        name: 'Usercredent',
        meta: { title: '资质', activeMenu: '/system/user' }
      }
    ]
  },
  // {
  //   path: '/system/inspect/item/:siteId(\\d+)',
  //   component: () => import('@/views/inspect/item'),
  //   hidden: true
  // },
  {
    path: '/system/inspect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'item/:siteId(\\d+)',
        component: () => import('@/views/inspect/item'),
        name: 'Inspectitem',
        meta: { title: '检测项', activeMenu: '/views/inspect' }
      }
    ]
  },
  {
    path: '/xj/user',
    component: () => import('@/views/system/user/index'),
    hidden: true
  },
  {
    path: '/xj/user-auth/credent/:userId(\\d+)',
    component: () => import('@/views/system/credent/usercredent'),
    hidden: true
  },
  {
    path: '/xj/dept',
    component: () => import('@/views/system/dept/index'),
    hidden: true
  },
  {
    path: '/xj/dict',
    component: () => import('@/views/system/dict/index'),
    hidden: true
  },
  {
    path: '/xj/cmsclass',
    component: () => import('@/views/system/cmsclass/index'),
    hidden: true
  },
  {
    path: '/xj/cms',
    component: () => import('@/views/system/cms/index'),
    hidden: true
  },
  {
    path: '/xj/problemclass',
    component: () => import('@/views/patrol/problemclass/index'),
    hidden: true
  },
  {
    path: '/xj/problem',
    component: () => import('@/views/patrol/problem/index'),
    hidden: true
  },
  {
    path: '/xj/blackspot',
    component: () => import('@/views/patrol/blackspot/index'),
    hidden: true
  },
  {
    path: '/xj/amend',
    component: () => import('@/views/patrol/amend/index'),
    hidden: true
  },
  {
    path: '/xj/verify',
    component: () => import('@/views/patrol/verify/index'),
    hidden: true
  },
  {
    path: '/xj/patrolreport',
    component: () => import('@/views/patrol/report/index'),
    hidden: true
  },
  {
    path: '/xj/line',
    component: () => import('@/views/inspect/line/index'),
    hidden: true
  },
  {
    path: '/xj/region',
    component: () => import('@/views/inspect/region/index'),
    hidden: true
  },
  {
    path: '/xj/inspecttask',
    component: () => import('@/views/inspect/report/index'),
    hidden: true
  },
  {
    path: '/xj/device',
    component: () => import('@/views/device/index'),
    hidden: true
  },
  {
    path: '/xj/institutionclass',
    component: () => import('@/views/system/institutionclass/index'),
    hidden: true
  },
  {
    path: '/xj/institution',
    component: () => import('@/views/system/institution/index'),
    hidden: true
  },
  {
    path: '/xj/credentclass',
    component: () => import('@/views/system/credentclass/index'),
    hidden: true
  },
  {
    path: '/xj/accidenthand',
    component: () => import('@/views/accident/hand/index'),
    hidden: true
  },
  {
    path: '/xj/accidentexamine',
    component: () => import('@/views/accident/examine/index'),
    hidden: true
  },
  {
    path: '/xj/hazardcheck',
    component: () => import('@/views/hazardcheck/index'),
    hidden: true
  },
  {
    path: '/xj/inspect/item/:siteId(\\d+)',
    component: () => import('@/views/inspect/item'),
    hidden: true
  },
  {
    path: '/xj/inspect/item1/:siteId(\\d+)',
    component: () => import('@/views/inspect/item/index1'),
    hidden: true
  },
  {
    path: '/xj/dict-data/index/:dicttypeId(\\d+)',
    component: () => import('@/views/system/dict/data'),
    hidden: true
  },
  {
    path: '/xj/user-auth/org/:userId(\\d+)',
    component: () => import('@/views/system/user/authOrg'),
    hidden: true
  },
  {
    path: '/xj/credent',
    component: () => import('@/views/system/credent/index'),
    hidden: true
  },
  {
    path: '/xj/institution/role/:institutionId(\\d+)',
    component: () => import('@/views/system/institution/role'),
    hidden: true
  },
  {
    path: '/xj/institution/share/:institutionId(\\d+)',
    component: () => import('@/views/system/institution/share'),
    hidden: true
  },
  {
    path: '/xj/share/file/:shareId',
    component: () => import('@/views/system/institution/sharefile'),
    hidden: true
  },
]

// 静态路由 - 手动自定义路由
export const staticRouter = [
  
    {
      "name": "System",
      "path": "/system",
      "hidden": false,
      "redirect": "noRedirect",
      "component": "Layout",
      "alwaysShow": true,
      "meta": {
        "title": "巡查管理",
        "icon": "system",
        "noCache": false,
        "link": null
      },
      "children": [
        {
          "name": "User",
          "path": "user",
          "hidden": false,
          "component": "system/user/index",
          "meta": {
            "title": "用户管理",
            "icon": "user",
            "noCache": false,
            "link": null
          }
        },
    
        
        {
          "name": "Dept",
          "path": "dept",
          "hidden": false,
          "component": "system/dept/index",
          "meta": {
            "title": "部门管理",
            "icon": "tree",
            "noCache": false,
            "link": null
          }
        },
       
        {
          "name": "Dict",
          "path": "dict",
          "hidden": false,
          "component": "system/dict/index",
          "meta": {
            "title": "字典管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Cmsclass",
          "path": "cmsclass",
          "hidden": false,
          "component": "system/cmsclass/index",
          "meta": {
            "title": "文化分类管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Cms",
          "path": "cms",
          "hidden": false,
          "component": "system/cms/index",
          "meta": {
            "title": "文化管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Problemclass",
          "path": "problemclass",
          "hidden": false,
          "component": "patrol/problemclass/index",
          "meta": {
            "title": "问题分类管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Problem",
          "path": "problem",
          "hidden": false,
          "component": "patrol/problem/index",
          "meta": {
            "title": "问题描述",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Blackspot",
          "path": "blackspot",
          "hidden": false,
          "component": "patrol/blackspot/index",
          "meta": {
            "title": "隐患上报",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Amend",
          "path": "amend",
          "hidden": false,
          "component": "patrol/amend/index",
          "meta": {
            "title": "隐患整改",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Verify",
          "path": "verify",
          "hidden": false,
          "component": "patrol/verify/index",
          "meta": {
            "title": "隐患验收",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Patrolreport",
          "path": "patrolreport",
          "hidden": false,
          "component": "patrol/report/index",
          "meta": {
            "title": "隐患台账",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Line",
          "path": "line",
          "hidden": false,
          "component": "inspect/line/index",
          "meta": {
            "title": "巡检路线",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Region",
          "path": "region",
          "hidden": false,
          "component": "inspect/region/index",
          "meta": {
            "title": "巡检区域",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Inspecttask",
          "path": "inspecttask",
          "hidden": false,
          "component": "inspect/report/index",
          "meta": {
            "title": "巡检任务",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Device",
          "path": "device",
          "hidden": false,
          "component": "device/index",
          "meta": {
            "title": "设备台账",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Institutionclass",
          "path": "institutionclass",
          "hidden": false,
          "component": "system/institutionclass/index",
          "meta": {
            "title": " 制度化分类管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Institution",
          "path": "institution",
          "hidden": false,
          "component": "system/institution/index",
          "meta": {
            "title": " 制度化管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Credentclass",
          "path": "credentclass",
          "hidden": false,
          "component": "system/credentclass/index",
          "meta": {
            "title": "资质类型管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Credent",
          "path": "credent",
          "hidden": false,
          "component": "system/credent/index",
          "meta": {
            "title": "资质管理",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Accidenthand",
          "path": "accidenthand",
          "hidden": false,
          "component": "accident/hand/index",
          "meta": {
            "title": "事故上报",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Accidentexamine",
          "path": "accidentexamine",
          "hidden": false,
          "component": "accident/examine/index",
          "meta": {
            "title": "事故调查",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
        {
          "name": "Hazardcheck",
          "path": "hazardcheck",
          "hidden": false,
          "component": "hazardcheck/index",
          "meta": {
            "title": "职业危害检测",
            "icon": "dict",
            "noCache": false,
            "link": null
          }
        },
      ]
    }
  
]

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  // {
  //   path: '/system/user-auth',
  //   component: Layout,
  //   hidden: true,
  //   permissions: ['system:user:edit'],
  //   children: [
  //     {
  //       path: 'role/:userId(\\d+)',
  //       component: () => import('@/views/system/user/authRole'),
  //       name: 'AuthRole',
  //       meta: { title: '分配角色', activeMenu: '/system/user' }
  //     }
  //   ]
  // },
  // {
  //   path: '/system/role-auth',
  //   component: Layout,
  //   hidden: true,
  //   permissions: ['system:role:edit'],
  //   children: [
  //     {
  //       path: 'user/:roleId(\\d+)',
  //       component: () => import('@/views/system/role/authUser'),
  //       name: 'AuthUser',
  //       meta: { title: '分配用户', activeMenu: '/system/role' }
  //     }
  //   ]
  // },
  // {
  //   path: '/system/dict-data',
  //   component: Layout,
  //   hidden: true,
  //   children: [
  //     {
  //       path: 'index/:dicttypeId(\\d+)',
  //       component: () => import('@/views/system/dict/data'),
  //       name: 'Data',
  //       meta: { title: '字典数据', activeMenu: '/system/dict' }
  //     }
  //   ]
  // },
  // {
  //   path: '/monitor/job-log',
  //   component: Layout,
  //   hidden: true,
  //   permissions: ['monitor:job:list'],
  //   children: [
  //     {
  //       path: 'index/:jobId(\\d+)',
  //       component: () => import('@/views/monitor/job/log'),
  //       name: 'JobLog',
  //       meta: { title: '调度日志', activeMenu: '/monitor/job' }
  //     }
  //   ]
  // },
  // {
  //   path: '/tool/gen-edit',
  //   component: Layout,
  //   hidden: true,
  //   permissions: ['tool:gen:edit'],
  //   children: [
  //     {
  //       path: 'index/:tableId(\\d+)',
  //       component: () => import('@/views/tool/gen/editTable'),
  //       name: 'GenEdit',
  //       meta: { title: '修改生成配置', activeMenu: '/tool/gen' }
  //     }
  //   ]
  // }
]

// // 防止连续点击多次路由报错
// let routerPush = Router.prototype.push;
// let routerReplace = Router.prototype.replace;
// // push
// Router.prototype.push = function push (location) {
//   return routerPush.call(this, location).catch(err => err)
// }
// // replace
// Router.prototype.replace = function push (location) {
//   return routerReplace.call(this, location).catch(err => err)
// }

export default new Router({
  mode: 'history', // 去掉url中的#
  base: process.env.BASE_URL,
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})
