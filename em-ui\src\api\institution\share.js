import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询分享列表
export function listShare(query) {
    return request({
      url: '/share/doQuery',
      method: 'post',
      data: query
    })
}

// 新增分享
export function addShare(data) {
    return request({
      url: '/share/doAdd',
      method: 'post',
      data: data
    })
}

// 删除分享
export function delShare(shareId) {
    return request({
      url: '/share/doDelete/' + shareId,
      method: 'post'
    })
}

// 查询分享的文件
export function filelist(params) {
  return request({
    url: '/share/filelist',
    method: 'get',
    params:params
  })
}