<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <!-- <el-form-item label="所属部门" prop="orgId">
          <treeselect v-model="queryParams.orgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择责任单位"  style="width:200px"/>
      </el-form-item> -->
      <el-form-item label="状态" prop="state">
       <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item label="区域名" prop="siteName">
        <el-input  v-model="queryParams.siteName" placeholder="请输入区域名" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="siteList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属部门" align="center"  prop="organization.organName"  :show-overflow-tooltip="true" >
      
      </el-table-column>
      <el-table-column label="路线" align="center" prop="inspectroute.routeName" :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="区域名" align="center" prop="siteName" :show-overflow-tooltip="true" />
      <el-table-column label="区域编码" align="center" prop="siteCode" :show-overflow-tooltip="true" />
      <el-table-column label="二维码" align="center" prop="siteCode" :show-overflow-tooltip="true" />
      <el-table-column label="设备" align="center" prop="inspectdevice.deviceName" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">启用</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDel(scope.row)"
          >删除</el-button>
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-notebook-2"
            @click="handleitem(scope.row)"
          >检测项</el-button>
           <el-button 
            size="mini"
            type="text"
            icon="el-icon-copy-document"
            @click="handleCopy(scope.row)"
          >复制</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        
        <el-form-item label="所属部门" prop="orgId">
          <treeselect v-model="form.orgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择所属部门"   @input="orgchange"/>
        </el-form-item>
        <el-form-item label="区域名" prop="siteName">
          <el-input  v-model="form.siteName" placeholder="请输入区域名" maxlength="50"/>
        </el-form-item>
        <el-form-item label="区域编码" prop="siteCode">
          <el-input  v-model="form.siteCode" :disabled="true"  />
        </el-form-item>
        <el-form-item label="二维码" prop="siteCode">
          <el-input  v-model="form.siteCode" :disabled="true" />
        </el-form-item>
        <el-form-item label="设备" prop="inspectDeviceId">
          <el-select v-model="inspectDeviceId" placeholder="请选择设备" filterable clearable @change="seldevice">
            <el-option
              v-for="item in devices"
              :key="item.deviceId"
              :label="item.deviceName"
              :value="item.deviceId">
            </el-option>
          </el-select>
        </el-form-item> 
        <el-form-item label="半径" prop="deviation">
          <el-input type="number" v-model="form.deviation" placeholder="请输入半径" />
        </el-form-item>
         <el-form-item label="是否联网" prop="isonline">
          <el-switch v-model="form.isonline" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
        </el-form-item>
        <el-form-item label="是否定位" prop="isposition">
          <el-switch v-model="form.isposition" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
        </el-form-item>
        <el-form-item label="是否拍照" prop="isphoto">
          <el-switch v-model="form.isphoto" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
        </el-form-item>
        <el-form-item label="状态" prop="state">
              <el-radio-group v-model="form.state" >
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import { listsite, getsite,addsite,updatesite,delsite} from "@/api/inspect/site";
import {  listDeptExcludeChild } from "@/api/system/dept";
import { listDevice} from "@/api/device/device";
import { getOrgByWork} from "@/api/system/user";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import Cookies from 'js-cookie'

export default {
  name: "Site",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //问题列表数据
      siteList:[],
      //部门
      deptOptions:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示弹出显示层
      viewopen:false,
      //设备列表
      devices:[],
      inspectDeviceId:undefined,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      userorg:'',
      // 表单参数
      form: {},
      currentuserid:undefined,
      // 表单校验
      rules: {
        siteName: [
          { required: true, message: "区域名不能为空", trigger: "blur" }
        ],
        state: [
          { required: true, message: "状态不能为空", trigger: 'change' }
        ],
      },
         //状态
      options: [{
          value: 1,
          label: '启用'
        }, {
          value: 2,
          label: '停用'
      }],
    };
  },
  created() {
    this.currentuserid = Cookies.get("em_user")
    this.getDeptTree();
    this.getList();
    this.getuserorglist();
  },
  methods: {

    /** 查询部门下拉树结构 */
    getDeptTree() {
      let queryparams1 = {};
      listDeptExcludeChild(queryparams1).then(response => {
        let datas = response.data;
        for(let i=0; i<datas.length; i++){
          datas[i].children=[];
        }
        this.deptOptions = this.handleTree(datas, "organId");
      });
    },
     getuserorglist(){
        getOrgByWork().then(response => {
          this.userorg=response.data[0].organId
          console.log( this.userorg)
        })
    },
       /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    //区域列表
    getList(){
      this.loading = true;
       this.queryParams.orderBy="creationtime desc"
       this.queryParams.userId=this.currentuserid
      listsite(this.queryParams).then(response => {
          this.siteList = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },
    //选择问题分类
    handleClassChange(data){
      let problemparams = {}
      problemparams.problemclassId=data
      problemparams.state=1
      listProblem(problemparams).then(response => {
          this.problems = response.data;
        }
      );
    },
    handleProblemChange(data){
      this.problems.forEach(b =>{
        if(b.problemId===data){
          this.form.blackspotDesc=b.riskDesc
          this.form.blackspotLevel=b.problemLevel
        }
      })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        siteId: undefined,
        routeId: undefined,
        orgId: undefined,
        siteCode: undefined,
        siteName:undefined,
        vbarcode:undefined,
        nameEn:undefined,
        nameTh:undefined,
        state:1,
        isposition:undefined,
        isphoto:undefined,
        isonline:undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if(this.queryParams.orgId == ''){
        this.queryParams.orgId=undefined
      }
      if(this.queryParams.state == ''){
        this.queryParams.state=undefined
      }
      if(this.queryParams.siteName == ''){
        this.queryParams.siteName=undefined
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.inspectDeviceId=undefined
      this.open = true;
      this.title = "新增巡检区域";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.siteId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {

      this.reset();
      this.inspectDeviceId=undefined;
      const siteId = row.siteId || this.ids
      
      getsite(siteId).then(response => {
        this.form = response.data;
        if(this.form.inspectDeviceId != undefined){
          this.inspectDeviceId=this.form.inspectDeviceId+''
          console.log(this.inspectDeviceId)
        }
        console.log(this.form.inspectDeviceId)
        this.open = true;
        this.title = "修改区域";

        this.devices=[]
        let paramorg = {"deviceOrgId":this.form.orgId}
        console.log('111')
        listDevice(paramorg).then(response=>{
          if(response.code==200){
            this.devices=response.data;
          }
        })
       
        
        

      });
    },
    seldevice(value){
      this.form.inspectDeviceId=value
    },
    /**删除按钮操作 */
    handleDel(row){
      const siteId = row.siteId || this.ids
      this.$modal.confirm('是否确认删除区域信息？').then(function() {
        return delsite(siteId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**复制按钮操作 */
    handleCopy(row){
       
       let orgid = this.userorg
      this.$modal.confirm('是否确认复制区域信息？').then(function() {
        row.timeStamp=undefined;
        row.creationtime=undefined;
        row.creatorId=undefined;
        row.organization=undefined;
        row.orgId=orgid;
        row.siteCode=undefined;
        row.siteName=row.siteName+"-复制";
        row.siteId=undefined;
        row.inspectroute=undefined;
        row.routeId=undefined;
        row.inspectdevice=undefined;
        row.lon=undefined;
        row.lat=undefined;
       
        let items = [];
        for(let i=0; i<row.inspectitems.length; i++){
            let item = row.inspectitems[i];
            item.timeStamp=undefined;
            item.creationtime=undefined;
            item.creatorId=undefined;
            item.inspectitemId=undefined;
            item.siteId=undefined;
            item.creator=undefined;
            item.modifiedtime=undefined;
            item.modifierId=undefined;
            item.modifier=undefined;
            items.push(item);
        }
        row.inspectitems=items;

        return addsite(row);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("复制成功");
      }).catch(() => {});
    },
    /**检测项操作 */
    handleitem(row){
      const siteId = row.siteId 

      let data = {id: 'pj1pHjhRL1CC11TT', url:'./xdemui/xj/inspect/item/'+siteId, name: '检测项', closable: true, openwin: false,backgroundColor: 'white'};
      parent.postMessage(data, "*");


      // this.$router.push("/xj/inspect/item/" + siteId);
      // window.location.href="/system/inspect/item/" + siteId
    },
 
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          console.log(this.inspectDeviceId)
          console.log(this.form.inspectDeviceId)
          if(this.inspectDeviceId == ''){
            this.form.inspectDeviceId = undefined
          }else{
             this.form.inspectDeviceId = this.inspectDeviceId
          }
          
          if (this.form.siteId != undefined) {
            updatesite(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addsite(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },

    //选择责任单位
    orgchange(value){
        this.devices=[]
        this.form.inspectDeviceId=undefined
        if(typeof(value) != "undefined"){
          let paramorg = {"deviceOrgId":value}
          
          listDevice(paramorg).then(response=>{
            if(response.code==200){
              this.devices=response.data;
            }
          })
        }
       
    }


  }
};
</script>