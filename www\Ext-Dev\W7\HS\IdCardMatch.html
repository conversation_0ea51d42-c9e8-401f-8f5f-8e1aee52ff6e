<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>人证比对演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="FileDisplay.html">文件播放</a></li>
                <li><a href="IdCard.html">身份证读取</a></li>
                <li><a href="ReadFinger.html">指纹读取</a></li>
                <li class="active"><a href="#">人证比对</a></li>
                <li><a href="DocOCR.html">OCR识别与水印</a></li>
                <li><a href="ReadSign.html">手写签名</a></li>
                <li><a href="ReadKeyboard.html">评价按键读取</a></li>
                <li><a href="Barcode.html">条码扫描与识别</a></li>
                <li><a href="LayoutRecognition.html">版面识别</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/W7-0911.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>人证比对/活体检测 演示</h1>
        <p>这个页面会自动打开安卓多功能终端上的人像摄像头，并在页面上显示人像摄像头的视频；点击【开始人证比对】按钮，会启动多功能终端上的人证比对功能，并在页面上显示人证比对结果。点击【活体检测】按钮，会用多功能终端进行活体检测。</p>
    </div>

    <div class="page-header">
    </div>

    <div class="col-md-5 rotateImgDiv">
        <img id="headVideo" src="">
        <p>人像摄像头视频</p>
        <p><button id="btnOpenClose" type="button" class="btn btn-primary" onclick="OpenCloseFaceCamera()">关闭人像摄像头</button></p>
    </div>

    <div class="col-md-7" style="text-align: center">
        <div class="col-md-4">
            <img id="idCardPic" src="">
            <p>身份证照片</p>
        </div>
        <div class="col-md-8 rotateImgDiv">
            <img id="matchedPic" src="">
            <p style="padding-left: 50px;">现场照片</p>
        </div>
        <div class="col-md-12" style="margin-top: -38px;">
            <button type="button" class="btn btn-primary" onclick="StartMatch()">开始人证比对</button>
            <button type="button" class="btn btn-primary" onclick="LiveDetect()" style="margin-left: 20px;">活体检测</button>
        </div>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/IdCardMatch.js"></script>
</body>
</html>