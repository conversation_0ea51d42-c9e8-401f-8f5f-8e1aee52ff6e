<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="所属部门" prop="deviceOrgId">
          <treeselect v-model="queryParams.deviceOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择所属部门"  style="width:200px"/>
      </el-form-item>
      <el-form-item label="设备名称" prop="deviceName">
          <el-input  v-model="queryParams.deviceName" placeholder="请输入路线名" clearable/>
      </el-form-item>
      <el-form-item label="状态" prop="state">
       <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属单位" align="center" prop="organization.organName" :show-overflow-tooltip="true" />
      <el-table-column label="设备名称" align="center" prop="deviceName" :show-overflow-tooltip="true" />
      <el-table-column label="设备编码" align="center" prop="deviceCode" :show-overflow-tooltip="true" />
      <el-table-column label="车间" align="center" prop="deviceWorkshop" :show-overflow-tooltip="true" />
      <el-table-column label="区域" align="center" prop="deviceRegion" :show-overflow-tooltip="true" />
      <el-table-column label="类型" align="center" prop="deviceType" :show-overflow-tooltip="true" />
      <el-table-column label="使用地点" align="center" prop="useAddress" :show-overflow-tooltip="true" />
      <el-table-column label="作业地点" align="center" prop="workAddress" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">使用中</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.state==1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button v-if="scope.row.state==1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDel(scope.row)"
          >删除</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">


        <el-form-item label="所属单位" prop="deviceOrgId">
          <treeselect v-model="form.deviceOrgId" :options="deptOptions" :normalizer="normalizer" placeholder="选择所属单位"  />
        </el-form-item>

        <el-form-item label="设备名称" prop="deviceName">
          <el-input v-model="form.deviceName" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备编码" prop="deviceCode">
          <el-input v-model="form.deviceCode" placeholder="请输入设备编码" />
        </el-form-item>
        <el-form-item label="车间" prop="deviceWorkshop">
          <el-input v-model="form.deviceWorkshop" placeholder="请输入车间" />
        </el-form-item>
        <el-form-item label="区域" prop="deviceRegion">
          <el-input v-model="form.deviceRegion" placeholder="请输入区域" />
        </el-form-item>
        <el-form-item label="类型" prop="deviceType">
          <el-input v-model="form.deviceType" placeholder="请输入类型" />
        </el-form-item>
        <el-form-item label="使用地点" prop="useAddress">
          <el-input v-model="form.useAddress" placeholder="请输入使用地点" />
        </el-form-item>
        <el-form-item label="作业地点" prop="workAddress">
          <el-input v-model="form.workAddress" placeholder="请输入作业地点" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.state">
            <el-radio
              v-for="dict in options"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
         </el-form-item>
        
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listDevice, getDevice,addDevice,updateDevice,delDevice} from "@/api/device/device";
import {  listDeptExcludeChild } from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Blackspot",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,

      //设备列表数据
      deviceList:[],
      //部门
      deptOptions:[],
      //问题列表
      problems:[],
      //图片地址
      imgPath:undefined,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示弹出显示层
      viewopen:false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      //责任人
      liableers:[],
      // 表单参数
      form: {},

      // 表单校验
      rules: {
        deviceOrgId: [
          { required: true, message: "单位不能为空", trigger: "change" }
        ],
        deviceName: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        state: [
          { required: true, message: "状态不能为空", trigger: 'change' }
        ],
        deviceCode:[
           { required: true, message: "设备编码不能为空", trigger: 'blur' }
        ]
      },
         //状态
      options: [{
          value: 1,
          label: '使用中'
        }, {
          value: 2,
          label: '停用'
      }],
    };
  },
  created() {
    this.getDeptTree();
    this.getList();
  },
  methods: {

    /** 查询部门下拉树结构 */
    getDeptTree() {
      let queryparams1 = {};
      listDeptExcludeChild(queryparams1).then(response => {
        let datas = response.data;
        for(let i=0; i<datas.length; i++){
          datas[i].children=[];
        }
        this.deptOptions = this.handleTree(datas, "organId");
      });
    },
       /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    //设备列表
    getList(){
      this.loading = true;
      this.queryParams.orderBy="creationtime desc"
      listDevice(this.queryParams).then(response => {
          this.deviceList = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        deviceId: undefined,
        deviceOrgId: undefined,
        deviceName: undefined,
        deviceCode: undefined,
        deviceWorkshop:undefined,
        deviceRegion:undefined,
        deviceType:undefined,
        useAddress:undefined,
        workAddress:undefined,
        state:1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
       if(this.queryParams.deviceOrgId == ''){
        this.queryParams.deviceOrgId=undefined
      }
      if(this.queryParams.deviceName == ''){
        this.queryParams.deviceName=undefined
      }
      if(this.queryParams.state == ''){
        this.queryParams.state=undefined
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("form");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "新增设备";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.deviceId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const deviceId = row.deviceId || this.ids
      getDevice(deviceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改设备";
      });
    },
    /**删除按钮操作 */
    handleDel(row){
      const deviceId = row.deviceId || this.ids
      this.$modal.confirm('是否确认设备信息？').then(function() {
        return delDevice(deviceId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.deviceId != undefined) {
              updateDevice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },




  }
};
</script>