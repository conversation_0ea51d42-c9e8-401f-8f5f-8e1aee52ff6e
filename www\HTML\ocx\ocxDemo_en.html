<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset=utf-8 http-equiv="Content-Language" content="en"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>CamSDK Client OCX Show</title>
    <script src="../../langs/en.js"></script>
    <style>
        body {
            line-height: 26px;
            padding: 2px 5px 2px 5px;
            background-color: #e3e8ea;
            text-align: left;
            font-size: 16px;
        }
        label, button, select, option {
            font-size: 16px;
        }
    </style>
</head>
<body>
<OBJECT ID="OCXobject" name="OCXobject" width="0" height="0" CLASSID="CLSID:DA096059-7D27-42E9-BDED-5BF4317CAC77">
</OBJECT>
<h1>Compare face and ID card For OCX - Comparison between photos and video</h1>
<p>Comparison between photos and video photos here is a demonstration of each frame of the video transmitted to the page, which is compared with the selected portrait photos to display the similarity. Compared with direct comparison with video, because each frame is transmitted to the page and then sent back to the server for comparison, the efficiency is much lower.</p>
<p><p>
<hr/>
<button onclick="myInit()">1. Start</button>&nbsp;&nbsp;
<span id="connState" style="padding-right:40px">Disconnect</span>
Camera：<select id="selDev" style="width:220px;"></select>
<button onclick="openCamera()">2. Open Camera</button>&nbsp;&nbsp;
<button onclick="openHeadImage()">3. Open face image</button>&nbsp;&nbsp;
<input id="imgFile" type="file" onchange="chooseImgFile()" accept="image/jpg, image/jpeg" style="display: none">
<!--<button onclick="readIdCard()">1.读取身份证</button>-->
<button onclick="MatchFace(true)">4. Start Compare</button>&nbsp;&nbsp;
<button onclick="MatchFace(false)">5. Stop Compare</button>&nbsp;&nbsp;
<button onclick="myUninit()">6. Finish</button>
<br>
<hr/>
<img id="imgVideo" style="width:640px;height:480px;"/>
<img id="imgHead" style="width:640px;height:480px;"/>
<br>
<hr/>
<span id="matchResult" style="padding: 10px 30px 5px 500px; font-size: 32px;color: #b52b27">Comparison similarity</span>
<br>
<hr/>
<script type="text/javascript" src="js/common/json2.js"></script>
<script type="text/javascript">
var _bOpened = false;
var _headBase64 = null;
var _matchFace = false;
var _devInfos = null;
</script>
<script language="JavaScript" for="OCXobject" event="OnRecvMsg(eventType, msg)" type="text/javascript">
    console.log(eventType, msg);
    switch (eventType) {
        case 3:
            // EVENT_TYPE_ON_OPEN
            _bOpened = true;
            document.getElementById('connState').innerText = "Connected";
            // 获取设备信息
            var cmd = '{"func":"GetCameraInfo","reqId":"' + new Date().getTime() + '"}';
            OCXobject.SendTxt(cmd);
            break;
        case 4:
            // EVENT_TYPE_ON_MESSAGE
            var jsonObj = JSON.parse(msg);
            if (jsonObj.errorMsg) {
                console.log(jsonObj.func, jsonObj.errorMsg);
                //alert(jsonObj.func + "\r\n" + jsonObj.errorMsg);
            } else {
                switch (jsonObj.func) {
                    case "GetCameraVideoBuff":
                        DisplayVideo(jsonObj);
                        break;
                    case "GetCameraInfo":
                        DisplayDevInfo(jsonObj.devInfo);
                        break;
                    case "FaceMatch":
                        DisplayMatchResult(jsonObj);
                        break;
                }
            }
            break;
        case 5:
            // EVENT_TYPE_ON_CLOSE
            _bOpened = false;
            document.getElementById('connState').innerText = "Disconnect";
            break;
    }
</script>

<script type="text/javascript">
    function isIE() {
        if (!!window.ActiveXObject || "ActiveXObject" in window)
            return true;
        else
            return false;
    }

    function myInit() {
        var ret = OCXobject.Init();
        console.log("OCXobject.Init() = ", ret);
    }

    function myUninit() {
        OCXobject.Uninit();
    }

    function DisplayDevInfo(devInfos) {
        _devInfos = devInfos;
        var selDev = document.getElementById("selDev");
        selDev.options.length = 0;
        for (var i = 0; i < _devInfos.length; i++) {
            var dev = _devInfos[i];
            selDev.options.add(new Option(dev.devName, dev.id));
        }
    }

    var _camId = -1;
    function openCamera() {
        var obj = document.getElementById("selDev");
        var index = obj.selectedIndex;
        var selCamId = obj.options[index].value;
        var resNum = 0;

        // 找到640x480的分辨率，人像比结一般使用640x480即可
        for (var i = 0; i < _devInfos.length; i++) {
            if (_devInfos[i].id == selCamId) {
                var mediaTypes = _devInfos[i].mediaTypes;
                var resolutions = mediaTypes[0].resolutions;
                for (var j = 0; j < resolutions.length; j++) {
                    if (resolutions[j] == '640x480') {
                        resNum = j;
                        break;
                    }
                }
                break;
            }
        }

        var reqId = new Date().getTime();
        if (_camId >= 0) {
            // 关闭之前打开的摄像头
            var cmd = '{"func":"CloseCamera","reqId":"' + reqId + '","devNum":' + _camId + '}'
            OCXobject.SendTxt(cmd);
            reqId++;
        }

        _camId = selCamId;
        var cmd = '{"func":"OpenCamera","reqId":"' + reqId + '","devNum":' + _camId +
                        ',"mediaNum":0,"resolutionNum":' + resNum + ',"fps":5}'
        OCXobject.SendTxt(cmd);
        reqId++;

        cmd = '{"func":"GetCameraVideoBuff","reqId":"' + reqId + '","devNum":' + _camId +',"enable":"true"}';
        OCXobject.SendTxt(cmd);
    }

    var _reqIdaceMatch = 0, _dispCount = 0;
    function DisplayVideo(v) {
        if (v.result == 0) {
            document.getElementById("imgVideo").src = "data:image/jpg;base64," + v.imgBase64Str;
            _dispCount++;

            if (_matchFace && _headBase64 && _dispCount % 5 == 1) {
                _reqIdaceMatch++;
                cmd = '{"func":"FaceMatch","reqId":"' + _reqIdaceMatch + '","image1Base64":"' + _headBase64 +'","image2Base64":"' + v.imgBase64Str + '"}';
                OCXobject.SendTxt(cmd);
            }
        }
    }

    function openHeadImage() {
        imgFile.click();
    }

    function chooseImgFile() {
        var file = imgFile.files[0];
        //readFile(file);
        var reader = new FileReader();
        reader.readAsDataURL(file);//转化成base64数据类型
        reader.onload = function (e) {
            document.getElementById("imgHead").src = this.result;
            _headBase64 = this.result.substring(23);
        }
    }

    function MatchFace(bEnable) {
        if (_headBase64 != null) {
            _matchFace = bEnable;
        }
    }

    function DisplayMatchResult(v) {
        document.getElementById("matchResult").innerText = "Similarity：" + v.similarity + "%"
    }

    //加载页面完成，等500毫秒后 初始化OCXobject
    window.onload = function () {
        if (!isIE()) {
            alert("Web used OCX, Please open it in IE browser.");
        }
    };

    //页面卸载,反初始化OCXobject
    window.onunload = function () {
        if (isIE()) {
            myUninit();
        }
    };
</script>
</body>
</html>
