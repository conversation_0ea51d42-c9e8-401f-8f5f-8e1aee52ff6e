<template>
  <div class="app-container" style="background-color:white;">
    <h4 class="form-header h4">制度化信息</h4>
    <el-form ref="form" :model="form" label-width="80px">
      <el-row>
        <el-col :span="8" :offset="2">
          <el-form-item label="文档编号" prop="insCode">
            <el-input v-model="form.insCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="2">
          <el-form-item label="文档名称" prop="insName">
            <el-input  v-model="form.insName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <h4 class="form-header h4">权限信息</h4>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" >新增</el-button>
      </el-col>

      
      <right-toolbar  @queryTable="getList" ></right-toolbar>
    </el-row>

    <el-table v-loading="loading"  ref="table" @selection-change="handleSelectionChange" :data="permissList">

      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center"  key="organName" prop="organization.organName" />
      <el-table-column label="人员" align="center"  key="workerName" prop="worker.workerName" />
     
      <el-table-column label="创建时间" align="center"  key="creationtime"  prop="creationtime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
           <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"  @pagination="getList"/>

     <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="workorgform" :model="workorgform" :rules="rules" label-width="80px">
       
            <el-form-item label="部门" prop="organId">
                <el-select v-model="initValue"   :clearable="true"  placeholder="请选择部门"
                   :popper-append-to-body="false"  @change="changeOption" style="width: 200px">
                    <el-option :value="optionValue" style="height: auto">
                        <el-tree  ref="tree" node-key="id" empty-text="暂无数据" :data="deptOptions"   @node-click="handleCheckChange"/>
                        </el-option>
              </el-select>

            </el-form-item>
        
            <el-form-item label="人员" prop="worktypeId">
               <el-select v-model="workorgform.workerId" placeholder="请选择人员" clearable>
                    <el-option
                    v-for="item in useroptions"
                    :key="item.worker.workerId"
                    :label="item.worker.workerName"
                    :value="item.worker.workerId">
                    </el-option>
                </el-select>


            </el-form-item>
          
       
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">添加部门权限</el-button>
        <el-button type="primary" @click="submitForm1">添加人员权限</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listWorkorg,deptTreeSelect} from "@/api/system/user";
import { getInstitution } from "@/api/institution/institution";
import {listPermission,addPermission,delPermission} from "@/api/institution/permission";

export default {
  name: "AuthRole",
  dicts: ['system_worktype','system_job'],
  data() {
    return {
       // 遮罩层
      loading: true,
      // 分页信息
      total: 0,
      pageNum: 1,
      pageSize: 10,
      //制度化管理
      institutionId:undefined,
      // 选中角色编号
      roleIds:[],
      // 角色信息
      roles: [],
      // 制度化信息
      form: {},
      //权限列表
      permissList:[],
       // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      //用户部门信息
      workorgform:{},
      // 是否显示弹出层
      open:false,
      //人员
      useroptions:[],
      //弹出标题
      title:undefined,
       // 部门树选项
      deptOptions: undefined,
       optionValue: undefined,
       initValue:undefined,
      // 表单校验
      rules: {

      }
    };
  },
  created() {
    const institutionId = this.$route.params && this.$route.params.institutionId;

    if (institutionId) {
      this.institutionId=institutionId
      this.getInstitution();
      this.getList();
      this.getDeptTree();
    }
  },
  methods: {

    getList(){
      this.loading = true;
      this.queryParams.sourceId=this.institutionId
      listPermission(this.queryParams).then((response)=>{
          this.permissList = response.data;
          this.total = response.totalCount;
          this.loading = false;
      })
    },
    getInstitution(){
      getInstitution(this.institutionId).then((response) => {
        this.form = response.data;
        
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },

     //查询组织内人员
    listworkerorg(organId){
      let query = {};
      query.organId=organId
      listWorkorg(query).then(response => {
        console.log(JSON.stringify(response.data))
          this.useroptions = response.data;
        }
      );
    },

    // 表单重置
    reset() {
      this.workorgform = {
        permissionId: undefined,
        permissionLevel: undefined,
        sourceType: undefined,
        sourceId: undefined,
        organId: undefined,
        workerId:undefined
      };
      this.resetForm("workorgform");
    },
    //新增用户部门
    handleAdd(){
        this.reset();
        this.open = true;
        this.title = "新增权限";
    },

     /** 提交按钮 */
    submitForm(){

      if(this.workorgform.organId != undefined && this.workorgform.organId != '' ){
            this.workorgform.permissionLevel=2;
            this.workorgform.sourceType=1;
            this.workorgform.sourceId=this.institutionId
            this.workorgform.workerId=undefined
            addPermission(this.workorgform).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
      }else{
           this.$modal.msgError("部门没有选择");
      }

    },
    submitForm1(){

      if(this.workorgform.workerId != undefined && this.workorgform.workerId != '' ){
            this.workorgform.permissionLevel=1;
            this.workorgform.sourceType=1;
            this.workorgform.sourceId=this.institutionId
            this.workorgform.organId=undefined
            addPermission(this.workorgform).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
      }else{
           this.$modal.msgError("人员没有选择");
      }

    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const permissionId = row.permissionId || this.ids;
      this.$modal.confirm('是否确认权限?').then(function() {
        return delPermission(permissionId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    changeOption(item) {
            console.log('select======', this.initValue,this.optionValue);
    },
    /**单击部门树 */
     handleCheckChange(data) {
      this.workorgform.organId=data.id
      this.optionValue = data.id
      this.initValue = data.label
      this.listworkerorg(this.workorgform.organId)
     },
    /** 单击选中行数据 */
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.roleIds = selection.map((item) => item.roleId);
    },
    // 保存选中的数据编号
    getRowKey(row) {
      return row.roleId;
    },


  },
};
</script>