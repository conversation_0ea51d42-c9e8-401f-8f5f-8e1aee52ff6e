<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>轮播图片设置 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="DeviceConfig.html">设备配置信息</a></li>
                <li class="active"><a href="#">轮播图片设置</a></li>
                <li><a href="SignAuthorization.html">签署诚信授权</a></li>
                <li><a href="CertifiedSalvageQualification.html">救助资格认证</a></li>
<!--                <li><a href="TakePhoto.html">文档拍照</a></li>-->
            </ul>
        </div>
    </div>
</nav>

<div style="float: left;">
    <img id="product" src="image/W7_2.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>轮播图片设置</h1>
        <p>选择1到3个jpg文件，然后按【上传】按钮，可以设置安卓多功能终端上的首页轮播图片。轮播图片最多限3张。</p>
    </div>

    <div class="page-header"></div>

    <div class="col-sm-12" style="margin-bottom: 10px;">
        <div class="row" style="margin-bottom: 10px;">
            <div class="input-group" style="width:70%;float:left;">
                <input type="text" class="form-control" placeholder="请选择首页轮播图片" />
                <span class="input-group-btn">
                    <label for="fileJpg" class="form-control btn btn-primary" style="border-radius:4px;">请选择首页轮播图片</label>
                    <input id="fileJpg" type="file" multiple="true" style="display:none" accept=".jpg,.png" onchange="onChgFileJpg()"/>
                </span>
            </div>
            <button id="uploadBtn" type="button" class="btn btn-primary" onclick="startUpload()" style="float:left; margin-left:35px">上  传</button>
        </div>

    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/Slideshow.js"></script>
</body>
</html>