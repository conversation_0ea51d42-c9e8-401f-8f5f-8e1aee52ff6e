body {
    padding-top: 70px;
    padding-bottom: 30px;
}

.theme-dropdown .dropdown-menu {
    position: static;
    display: block;
    margin-bottom: 20px;
}

.theme-showcase > p > .btn {
    margin: 5px 0;
}

.theme-showcase .navbar .container {
    width: auto;
}

.jumbotron {
    padding-top: 10px;
    padding-bottom: 10px;
}

.jumbotron h1 {
    font-size: 32px;
    margin-top: 10px;
}

.jumbotron p {
    margin-bottom: 10px;
    font-size: 16px;
}

#tabBody a {
    background-color: #337ab7;
    border-color: #2e6da4;
    color: #fff;
    font-size: 12px;
    border-radius: 4px;
    padding: 3px 6px;
    text-decoration: none;
    cursor: pointer;
}

#verInfo {
    font-size: 14px;
    padding-left: 20px;
}
#product {
    width: 240px;
}
#open1, #open2 {
    margin-left: 30px;
}
#videoMain, #videoVice {
    width: 320px;
    min-width: 320px;
    min-height: 180px;
    max-height: 240px;
    border: #9d9d9d 1px solid;
    margin-top: 5px;
}

#imgCapture1, #imgCapture2 {
    width: 360px;
    min-width: 360px;
    min-height: 202.5px;
    max-height: 270px;
    border: #9d9d9d 1px solid;
    margin-bottom: 5px;
}

.fieldItem {
    padding-right: 20px;
}

.fieldItem2 {
    padding: 0 12px;
}

#outInfo {
    margin-top: 5px;
}
.lbn {
    margin-left: 5px;
}
#fps1, #fps2 {
    width: 40px;
}
#resolution1, #resolution2 {
    width: 94px;
}
#rotate1, #rotate2 {
    width: 50px;
}