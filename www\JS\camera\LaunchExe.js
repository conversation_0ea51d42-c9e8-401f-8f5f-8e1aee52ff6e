var _myWs = new MzWsConnect(location.port, onTxtMessage, onError, ClearOutputInfo);

/**
* 处理服务器文本消息
*/
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.func, jsonObj.errorMsg);
        displayOutputInfo(3, jsonObj.func + "    " + jsonObj.errorMsg)
        if (jsonObj.func == "FaceMatch") {
            var btn = document.getElementById("btnStartStop");
            btn.innerText = Lang.IDC.startCompare;
        }
    } else {
        switch (jsonObj.func) {
            case "LaunchExe":
                DisplayOutputInfo(2, "启动Exe成功.");;
                break;
            case "ExecutionResult":
                DisplayImage(jsonObj);
                break;
        }
    }
}

// 处理连接错误
function onError(e, msg) {
    DisplayOutputInfo(3, msg);
}

function ClearOutputInfo() {
    DisplayOutputInfo(0);
}

function DisplayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["#b5daf1", "#9de09d", "#f3a960"];
        $('#outInfo').css("display", "").css("background-color", ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function DisplayImage(v) {
    for (var i = 0; i < v.imgBase64.length; i++) {
        var h = '<img src="data:' + v.mime + ';base64,' + v.imgBase64[i] + '">';
        $("#divImages").prepend(h);
    }
    DisplayOutputInfo(2, "收到Exe程序的拍照结果，共 " + v.imageCount + " 个文件.");;
}

function launchExe(exeName) {
    _myWs.SendJson({func:"LaunchExe", exeName:exeName, caption:"请扫描文件然后按【保存】", mode:"base64", reqId: new Date().getTime()});
}

$(window).unload(function () {
    // 关闭页面之前,关闭摄像头,关闭连接
    if (_myWs && _myWs.connected) {
        _myWs.Close();
        _myWs = null;
    }
});


