body {
    padding-top: 70px;
    padding-bottom: 30px;
}

.theme-dropdown .dropdown-menu {
    position: static;
    display: block;
    margin-bottom: 20px;
}

.theme-showcase > p > .btn {
    margin: 5px 0;
}

.theme-showcase .navbar .container {
    width: auto;
}

.jumbotron {
    padding-top: 10px;
    padding-bottom: 10px;
}

.jumbotron h1 {
    font-size: 32px;
    margin-top: 10px;
}

.jumbotron p {
    margin-bottom: 10px;
    font-size: 16px;
}

#tabBody a {
    background-color: #337ab7;
    border-color: #2e6da4;
    color: #fff;
    font-size: 12px;
    border-radius: 4px;
    padding: 3px 6px;
    text-decoration: none;
    cursor: pointer;
}

#appName {
    width: 320px;
}

#product {
    width: 240px;
}

.rotateImgDiv {
    text-align: center;
    height: 500px;
    padding-top: 54px;
}

.rotateImgDiv img {
    height: 324px;
    width: 432px;
    margin-bottom: 54px;
    -moz-transform: rotate(-90deg);
    -webkit-transform: rotate(-90deg);
    filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=3);
}

#idCardPic {
    width: 112px;
    height: 138px;
    margin-top: 140px;
}

.cameraPU {
    width: 150px !important;
    display: inline-block !important;
    margin-top: 4px;
}