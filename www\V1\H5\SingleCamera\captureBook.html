<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- utf-8编码-->
    <meta charset="utf-8">
    <title>CamSDK拍书演示</title>
    <link rel="stylesheet" href="css/cropBox.css"/>
    <style>
        .videoBox {
            width:506px;
            height:380px;
            position:absolute;
            left:12px;
            top:80px;
        }
        .page {
            width:506px;
            height:380px
        }
        .pic {
            width:506px;
            height:380px;
            margin: 5px 5px 0 0;
        }
    </style>
</head>
<body>
<p>这个例子演示了如何拍摄打开的书本。拍摄时，书本的中缝对齐视频预览中的红线，然后点击“拍书”按钮。不需要拍摄左页，也可以把左页卷起。<br>
也可以使用“手动裁切”功能。通过鼠标在视频区上绘出绿色裁切框(支持多个)，然后再“拍照”时就会自动按绿色裁切框来裁切，而不再使用默认的书本裁切。
</p>
<table border="0">
    <tr height="405">
        <td colspan="3" valign="top">
            <span>视频预览</span>
            <img id="video" class="videoBox">
            <canvas id="cv" class="videoBox">你的浏览器太low了 不支持canvas 赶快换一个吧</canvas>
        </td>
        <td>
            <input id="manualCrop" type="checkbox" value="0" onchange="OnManualCropChange()"/><label for="manualCrop">手动裁切</label>
            <br><br>
            <button id="btnCapture" onclick="doCapture()" style="width:100px;height:32px;">拍  书</button>
        </td>
    </tr>
    <tr>
        <td colspan="2">
            <span>左页</span><br/>
            <img id="imgPageL" class="page">
        </td>
        <td colspan="2">
            <span>右页</span><br/>
            <img id="imgPageR" class="page">
        </td>
    </tr>
</table>
<div id="otherImg"></div>

<script src="js/cropBox.js"></script>
<script src="js/captureBook.js"></script>
</body>
</html>