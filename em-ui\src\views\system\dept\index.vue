<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch">
      <el-form-item label="部门名称" prop="deptName">
        <el-input
          v-model="queryParams.deptName"
          placeholder="请输入部门名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="部门状态" clearable>
          <el-option
            v-for="dict in options"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-sort"
          size="mini"
          @click="toggleExpandAll"
        >展开/折叠</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table
      v-if="refreshTable"
      v-loading="loading"
      :data="deptList"
      row-key="organId"
      :default-expand-all="isExpandAll"
      :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
    >
      <el-table-column prop="organName" label="部门名称" width="260"></el-table-column>
      <el-table-column prop="code" label="部门编码" width="200"></el-table-column>
      <el-table-column prop="shortname" label="部门简称" width="200"></el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">正常</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="creationtime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-plus"
            @click="handleAdd(scope.row)"
          >新增</el-button>
          <el-button
            v-if="scope.row.parentId != 0"
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-s-marketing"
            @click="handleliability(scope.row)"
          >责任书</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改部门对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="24" v-if="form.fatherId !== 0">
            <el-form-item label="上级部门" prop="fatherId">
              <treeselect v-model="form.fatherId" :options="deptOptions" :normalizer="normalizer" placeholder="选择上级部门" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门名称" prop="organName">
              <el-input v-model="form.organName" placeholder="请输入部门名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门简称" prop="shortname">
              <el-input v-model="form.shortname" placeholder="请输入部门简称"  />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门编码" prop="code">
              <el-input v-model="form.code" placeholder="请输入部门编码" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分管领导" prop="leader">
              <el-input v-model="form.leader" placeholder="请输入分管领导" maxlength="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="NC部门" prop="ncdeptname">
              <el-input v-model="form.ncdeptname" placeholder="请输入NC部门" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="部门状态">
              <el-radio-group v-model="form.state">
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
           <el-col :span="12">
           <el-form-item label="所属公司" prop="corp">
                <el-select v-model="form.corp" placeholder="请选择公司">
                <el-option
                  v-for="item in corps"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
           </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 目标责任书对话框 -->
    <el-dialog :title="title" :visible.sync="liabilityopen" width="600px" append-to-body>
      <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAddDuty"
        >新增</el-button>
      </el-col>
    </el-row>
      <el-table v-loading="loading" :data="dutydata" >
      <el-table-column label="年度" align="center" prop="year" />
      <!-- <el-table-column label="责任人" align="center" prop="dicttypeName" :show-overflow-tooltip="true" />
      <el-table-column label="附件" align="center" prop="fileName" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.fileName) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDown(scope.row)"
          >下载</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDeleteDuty(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
      </el-table>
        <pagination
      v-show="total>0"
      :total="total"
      :page.sync="dutyParams.pageNum"
      :limit.sync="dutyParams.pageSize"
      @pagination="getDutys"
    />
    </el-dialog>
      <!-- 新增目标责任书对话框 -->
     <el-dialog :title="title" :visible.sync="adddutyopen" width="400px" append-to-body>
        <el-form ref="dutyform" :model="dutyform" :rules="dutyrules" label-width="80px">
          <el-row>
            <el-form-item label="年度" prop="year">
               <el-date-picker  v-model="dutyform.year" type="year" placeholder="选择年" format="yyyy"
      value-format="yyyy"> </el-date-picker>
            </el-form-item>
           <el-form-item label="文件" prop="imgPath">
            <file-upload v-model="dutyform.filePath" :limit="1"  />
           </el-form-item>
          </el-row>
        </el-form>
         <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="dutysubmitForm">确 定</el-button>
          <el-button @click="dutycancel">取 消</el-button>
        </div>
     </el-dialog>
  </div>
</template>

<script>
import { listDept, getDept, delDept, addDept, updateDept, listDeptExcludeChild ,listLiability,addDuty,delDuty} from "@/api/system/dept";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Dept",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 表格树数据
      deptList: [],
      // 部门树选项
      deptOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //目标责任书显示弹出层
      liabilityopen:false,
      //新增目标责任书显示弹出层
      adddutyopen:false,
      // 是否展开，默认全部展开
      isExpandAll: true,
      // 重新渲染表格状态
      refreshTable: true,
      // 查询参数
      queryParams: {
        deptName: undefined,
        status: undefined
      },
      //目标责任书查询参数
      dutyParams:{

      },
        // 总条数
      total: 0,
      //目标责任书数据
      dutydata:[],
      //状态
      options: [{
          value: 1,
          label: '正常'
        }, {
          value: 2,
          label: '停用'
      }],
      //公司
      corps:[{
          value: 'js',
          label: '江苏'
        },{
          value: 'dy',
          label: '东营'
      }, {
          value: 'jn',
          label: '济宁'
      }, {
          value: 'tg',
          label: '泰国'
      }],
      // 表单参数
      form: {},
      //目标责任书参数
      dutyform:{},
      //目标责任书表单验证
      dutyrules:{
         year: [
          { required: true, message: "年度不能为空", trigger: "blur" }
        ],
      },
      // 表单校验
      rules: {
        organName: [
          { required: true, message: "部门名称不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "显示编码不能为空", trigger: "blur" }
        ],
        corp: [
          { required: true, message: "公司不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询部门列表 */
    getList() {
      this.loading = true;
      listDept(this.queryParams).then(response => {
        let datas = response.data;
        for(let i=0; i<datas.length; i++){
          datas[i].children=[];
        }
        this.deptList = this.handleTree(datas, "organId");
        this.loading = false;
      });
    },
    /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    //目标责任书取消按钮
    dutycancel(){
      this.adddutyopen=false;
      this.dutyform={};
    },
    // 表单重置
    reset() {
      this.form = {
        organId: undefined,
        corp: undefined,
        processId: undefined,
        organName: undefined,
        shortname: undefined,
        code: undefined,
        fatherId: undefined,
        state: 1,
        nameEn:undefined,
        nameTh:undefined,
        leaderId:undefined,
        ncdeptname:undefined,
        ncdeptcode:undefined

      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },
    /**目标责任书操作 */
    handleliability(row){
      this.liabilityopen = true;
      this.title = "目标责任书";
      this.dutyParams.organId=row.organId;
      this.dutyParams.orderBy="year desc";
      this.getDutys();
    },
    /**查询目标责任书 */
    getDutys(){
      this.loading = true;
      listLiability(this.dutyParams).then(response => {
          this.dutydata = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },
    /**新增目标责任书按钮操作 */
    handleAddDuty(){
        this.dutyform={}
        this.adddutyopen=true
    },
    /** 新增按钮操作 */
    handleAdd(row) {
      this.reset();
      this.open = true;
      this.title = "添加部门";
      let queryparams1 = {};
      // if (typeof(row.organId) != undefined) {
      //   queryparams1.organId=row.organId
      // }
       listDeptExcludeChild(queryparams1).then(response => {
          let datas = response.data;
          for(let i=0; i<datas.length; i++){
            datas[i].children=[];
          }
          this.deptOptions = this.handleTree(datas, "organId");
          console.log(JSON.stringify(this.deptOptions))
        });
     
    },
    /** 展开/折叠操作 */
    toggleExpandAll() {
      this.refreshTable = false;
      this.isExpandAll = !this.isExpandAll;
      this.$nextTick(() => {
        this.refreshTable = true;
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      getDept(row.organId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改部门";
        let queryparams1 = {};
        // if (typeof(row.organId) != undefined) {
        //   queryparams1.organId=row.organId
        // }
        listDeptExcludeChild(queryparams1).then(response => {
          this.deptOptions = this.handleTree(response.data, "organId");
          if (this.deptOptions.length == 0) {
            console.log(JSON.stringify(this.form))
            const noResultsOptions = { organId: this.form.organId, organName: this.form.organName, children: [] };
            this.deptOptions.push(noResultsOptions);
          }
        });
      });
    },
    /**目标责任书提交按钮 */
    dutysubmitForm:function(){
       this.$refs["dutyform"].validate(valid => {
        if (valid) {
            console.log(JSON.stringify(this.dutyform))
            this.dutyform.organId=this.dutyParams.organId
             addDuty(this.dutyform).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.adddutyopen = false;
              this.getDutys();
            });
        }
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.organId != undefined) {
            updateDept(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDept(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$modal.confirm('是否确认删除名称为"' + row.organName + '"的数据项？').then(function() {
        return delDept(row.organId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 删除目标责任书按钮操作 */
    handleDeleteDuty(row) {
      this.$modal.confirm('是否确认删除该目标责任书？').then(function() {
        return delDuty(row.responsibilityId);
      }).then(() => {
        this.getDutys();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**下载目标责任书 */
    
    handleDown(row){
      const downurl = process.env.VUE_APP_BASE_API+row.filePath;
      console.log(downurl)
      window.open(downurl)
    }
  }
};
</script>
