<!DOCTYPE html>
<html>
<head>
	<title>CameraTestDemo</title>
</head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<body onload ="InitCamera();" onunload = "UnInitCamera();">
<p>
		<div id="videoPreviewDiv1">
			<img id="videoPreview" >
			<img id="IDCardPreview" width="300px" height = "400px">
			<p>
			<input style="width:10%" type = "text" name="path" id="path"  value = "C:\tmp">
			<input type = "button" value = "读卡" onClick="ReadCard()" style = "width:60; text-align:center">
			<p>
			<input type = "button" value = "证照比对" onClick="FaceComPare()" style = "width:60; text-align:center">
			人脸比对分数：
			<input style="width:10%" type = "text" name="FaceScore" id="FaceScore"  value = "">
		</div>
		
</body>
</html>
<script type="text/javascript">
var CamOCX;
var m_splitTag = "$*$";
var isie = false;
var CardFilePath;
var ImageFilePath;
var FileBase64;
var IDCardBase64;
//初始化，页面加载时执行
function InitCamera() 
{ 
	//如果浏览器为支持Acticex（IE），动态创建OCX控件。且执行StartVideoByOcx()。
 if (!!window.ActiveXObject || "ActiveXObject" in window)
  {
		isie = true;
	    var ocxObject = document.createElement('object');//ocx控件对象

        var child=document.getElementById("videoPreview");
        ocxObject.style.width = "800px";
        ocxObject.style.height = "600px";
 
        ocxObject.id = "CamOCX";
        ocxObject.name = "CamOCX";
        ocxObject.classid = "clsid:556DBC8A-FE4A-4DA7-A82E-3926C8D4AC41";
		var parent=document.getElementById("videoPreviewDiv1");
		var parent=document.getElementById("videoPreviewDiv1");
		parent.replaceChild(ocxObject,child);
		CamOCX = document.getElementById("CamOCX");
		setTimeout("StartVideoByOcx()",200);
  }
  //如果不支持Activex，创建显示区域满足分辨率4：3，创建wensocket，并且SendMessage。
  else
  {
	var VideoImage  = document.getElementById("videoPreview");
	VideoImage.style.width = "800px";
    VideoImage.style.height = "600px";
	StartWebSocket();
	//InitCameraByServer();
	
  }
 }
 
 //ocx-初始化，开启视频
 function StartVideoByOcx()
 {
		var init = CamOCX.InitCameraLib();
		if(init !=0)
		{
			alert("检测设备授权失败！"+init);
			return;
		}
		var InitFace = CamOCX.camInitFace();
		if(InitFace !=0)
		{
			alert("人脸初始化失败！"+InitFace);
			return;
		}
		var open = CamOCX.openDev(1,0,640,480);
		CamOCX.SetAutoCrop(5);
 }
 
  //H5-开启webSocket
function StartWebSocket()
{
	 var url = "ws://localhost:9000/";

	if('WebSocket' in window){
            ws = new WebSocket(url);
    }
    else if('MozWebSocket' in window){
        ws = new MozWebSocket(url);
    }else{
		alert("浏览器版本过低，请升级您的浏览器。\r\n浏览器要求：IE10+/Chrome14+/FireFox7+/Opera11+");
	}
   
	ws.onopen = function()
   {
	  InitCameraByServer();//初始化
   };
	
	//OnMessage
   ws.onmessage = function (evt) 
   { 
   	
   	
   	if(typeof(evt.data)=="string"){
   		
   		var str = evt.data;
   		
   		if(str.length <= 0){
   			
   			return;
   		}
		
		if(str.indexOf("FileEncodeBase64") >=0){
			
			var strs= new Array(); 
			strs=str.split(m_splitTag);
			FileBase64 = strs[2];
		}
		
		if(str.indexOf(m_splitTag)>=0){
			//视频的每一帧
			var strs= new Array();
			strs=str.split(m_splitTag); 
			setImageWithBase64(strs[1]);
		}else{
			//处理其他请求
			console.log(str);
			handleJsonStrMessage(str);
		}
   	} 
 	};
	
}

//H5-HandsonStrMessage，接收server端的Message，并且解析。
function handleJsonStrMessage(str)
{
	var jsonOBJ = JSON.parse(str);
	var name = jsonOBJ.FuncName;
	var re = jsonOBJ.result;
	if(name =="camInitCameraLib")
	{
		if( re!=0)
		{
			alert("camInitCameraLib失败："+re);
			return;
		}
		
		StartVideoByServer();
	}
	else if(name =="camOpenDev")
	{
		if( re!=0)
		{
			alert("camOpenDev失败:"+re);
		}
		else
		{
			var jsonObj = {FuncName:'camSetImageAutoCrop',argument:{CropType:5}};
			sendWsMessage(jsonObj);
		}
	}
	else if(name =="camCaptureImageFile")
	{
			if( re!=0)
		{
			alert("camCaptureImageFile失败："+re);
		}
	}
	else if(name =="camInitFace")
	{
			if( re!=0)
		{
			alert("camInitFace失败："+re);
		}
	}
	else if(name =="camMatchFaceByFileVideo")
	{
		document.getElementById("FaceScore").value = re;
	}
	else if(name =="camMatchFaceByBase64Video")
	{
		document.getElementById("FaceScore").value = re;
	}
	else if(name =="idcardrfidReadIDCard")
	{
		alert(re);
	}
	else if (name == "idcardrfidReadIDCardEx")
	{
		//alert(re);
		var strs= new Array();
		strs=re.split("|");
		IDCardBase64 = strs[9];
		document.getElementById("IDCardPreview").src = "data:image/png;base64," + IDCardBase64;
	}
}

 //H5-初始化
  function InitCameraByServer()
 {
	var jsonObj = {FuncName:'camInitCameraLib'};
	sendWsMessage(jsonObj);
	var jsonObj = {FuncName:'camInitFace',argument:{}};
	sendWsMessage(jsonObj);
 }
 
 //H5-开启视频，预览
 function StartVideoByServer()
 {
	//打开摄像头
	var jsonObj = {FuncName:'camOpenDev',argument:{devIndex:1, subtype:0,width:640,height:480}};
	sendWsMessage(jsonObj);
	//开始预览
	jsonObj = {FuncName:'camStartPreview'};
	sendWsMessage(jsonObj);

 }
 
//H5-接收图像Base64数据显示。
function setImageWithBase64(str)
{
	var myimg = document.getElementById("videoPreview");
	myimg.src = "data:image/png;base64,"+str;
}

//H5-发送消息给Server
function sendWsMessage(jsonObj){
	var jsonStr = JSON.stringify(jsonObj);
	ws.send(jsonStr);
}

//比对
function FaceComPare()
{
	var strFolder = document.getElementById("path").value;
	var ImageDate = new Date();
	var ImageName = "Image_"+ImageDate.getFullYear()+(ImageDate.getMonth()+1)+ImageDate.getDate()+"_"+ImageDate.getHours()+ImageDate.getMinutes()+ImageDate.getSeconds()+ImageDate.getMilliseconds()+".jpg";
	ImageFilePath = strFolder + "\\" +ImageName;
	
	//OCX——比对
	if(isie)
	{
		
		var Score = CamOCX.camMatchFaceByBase64Video(1,IDCardBase64,ImageFilePath,100);
		//var Score = CamOCX.camMatchFaceByFileVideo(1,CardFilePath,ImageFilePath,100);
		document.getElementById("FaceScore").value = Score;
	}
	//H5-比对
	else
	{
		
		 var jsonObj = {FuncName:'camMatchFaceByBase64Video',argument:{personFace:IDCardBase64,videoFilePath:ImageFilePath,ldelayTime:0}};
         sendWsMessage(jsonObj);
		//var jsonObj = {FuncName:'camMatchFaceByFileVideo',argument:{filePath:CardFilePath,videoFilePath:ImageFilePath,ldelayTime:0}};
		//sendWsMessage(jsonObj); 
	}
}

//读卡
 function ReadCard()
 {
	var strFolder = document.getElementById("path").value;
	var ImageDate = new Date();
	var ImageName = "Image_"+ImageDate.getFullYear()+(ImageDate.getMonth()+1)+ImageDate.getDate()+"_"+ImageDate.getHours()+ImageDate.getMinutes()+ImageDate.getSeconds()+ImageDate.getMilliseconds()+".jpg";
	CardFilePath = strFolder + "\\" +ImageName;
	//OCX读卡
	if(isie)
	{
		CamOCX.UnInitIDCardRFID();
		var ret = CamOCX.InitIDCardRFID();
		if(ret != 0)
		{
			alert("连接读卡器失败，错误码：" + ret);
			return;
		}
		var IDCardMessage = CamOCX.GetIDCardInfoRFID(CardFilePath);
		alert(IDCardMessage);
		IDCardBase64 = CamOCX.EncodeBase64(CardFilePath);
		if(IDCardBase64 =="")
		{
			alert("人像base64为空，请确认文件夹下是否有图片！")
		}
		document.getElementById("IDCardPreview").src = "data:image/png;base64," + IDCardBase64;
		CamOCX.UnInitIDCardRFID();
	}
	//H5-读卡
	else
	{
		var jsonObj = {FuncName:'idcardrfidReadIDCardEx',argument:{}};
		sendWsMessage(jsonObj);
	}
 }

//页面关闭IE或者H5反初始化。
function UnInitCamera()
{
	if(isie)
	{
		CamOCX.CloseDev();
		CamOCX.camUnInitFace();
		CamOCX.UnInitCameraLib();
	}
	else
	{
		//反初始化
		var jsonObj = {FuncName:'camUnInitFace'};
		sendWsMessage(jsonObj);
		var jsonObj = {FuncName:'camUnInitCameraLib'};
		sendWsMessage(jsonObj);
	}
}

</script>