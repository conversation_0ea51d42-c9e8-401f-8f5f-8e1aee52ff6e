var _devInfos = null;
var _devNum = -1;
var _myWs = new MzWsConnect(location.port, onTxtMessage, onError, ClearOutputInfo);

// 处理连接错误
function onError(e, msg) {
    displayOutputInfo(3, msg);
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

function ClearOutputInfo() {
    displayOutputInfo(0);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.func, jsonObj.errorMsg);
        displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
    } else {
        switch (jsonObj.func) {
            case "GetCameraInfo":
                DisplayDevInfo(jsonObj);
                break;
            case "OpenCamera":
                // 打开文档摄像头成功后,设置'去除手指'，获取文档摄像头的视频
                var reqId = new Date().getTime();
                _myWs.SendJson({func: 'SetCameraImageInfo', devNum:_devNum, removalForeign:1, reqId:reqId++});
                _myWs.SendJson({func: "GetCameraVideoBuff", devNum: _devNum, enable: true, reqId: reqId++});
                break;
            case "CloseCamera":
                displayOutputInfo(2, "摄像头关闭成功.");
                break;
            case "GetCameraVideoBuff":
                // 获取到摄像头的视频,显示
                DisplayVideo(jsonObj);
                break;
            case "Notify":
                // 收到事件通知
                if (jsonObj.event == "OnCameraAutoCapture") {
                    // 是CameraAutoCapture触发事件,调用RecogBarCode来识别条码或二维码
                    _myWs.SendJson({func: "RecogBarCode", devNum: _devNum, enable: true, reqId: new Date().getTime()});
                }
                break;
            case "RecogBarCode":
                DisplayBarCodeResult(jsonObj);
                break;
            case "CameraCaptureBook":
                DisplayCaptureBook(jsonObj);
                break;
            case "CameraAutoCapture":
                if (jsonObj.enable) {
                    document.getElementById("btnAuto").innerText = "停止";
                    document.getElementById("btnAuto").disabled = false;
                    displayOutputInfo(2, "开启[自动检测图像变化]成功, 请在高拍仪下放入条码或翻开书本。");
                } else {
                    document.getElementById("btnAuto").innerText = "开始";
                    document.getElementById("btnAuto").disabled = false;
                    displayOutputInfo(2, "已停止[自动检测图像]。");
                }
                break;
            case "FileToBase64":
                DisplayFileView(jsonObj);
                break;
            case "MergeFile":
                DisplayMergeFile(jsonObj);
                break;
            case "FileOCR":
                DisplayOcrFile(jsonObj);
                break;
            case "GetOcrSupportInfo":
                displayOcrSupportInfo(jsonObj);
                break;
            default:
                console.log(msg);
                if (jsonObj.result != 0) {
                    displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
                }
                break;
        }
    }
}

//显示条码识别结果
function DisplayBarCodeResult(v) {
    if (v.codeInfos) {
        var a = v.codeInfos;
        displayOutputInfo(2, "识别到" + a.length + "个条码.", 0);
        for (var i = 0; i < a.length; i++) {
            $("#tabBody2").append('<tr><td>' + a[i].codeNum + '</td><td>' + a[i].codeType + '</td></tr>');
        }
    } else {
        // 没有识别到条码, 拍书
        _myWs.SendJson({func:"CameraCaptureBook", devNum:_devNum, "mode":"path", reqId:new Date().getTime()});
        displayOutputInfo(2, Lang.cam.noRrecognized, 0);
    }
}

function displayOcrSupportInfo(v) {
    if (v.result == 0) {
        var obj = document.getElementById("language");
        obj.options.length = 0;
        for (var i = 0; i < v.languages.length; i++) {
            // 语言ID是从1开始
            obj.options.add(new Option(v.languages[i], i + 1));
        }
        // 默认选中'Simplified chinese+English'
        obj.selectedIndex = 98;

        obj = document.getElementById("fileType");
        obj.options.length = 0;
        for (var i = 0; i < v.fileTypes.length; i++) {
            obj.options.add(new Option(v.fileTypes[i], i));
        }
    }
}

function DisplayDevInfo(v) {
    if (v.result == 0) {
        _devInfos = v.devInfo;
        if (_devInfos.length == 0) {
            displayOutputInfo(3, "获取设备信息时，返回的devInfos为空.");
            return;
        }

        displayCamera();
        displayMediaType();
        displayResolution();
        switchCamera();
    } else {
        displayOutputInfo(3, v.func + "<br>" + v.errorMsg);
    }
}

// 刷新显示 设备名称 下拉列表
function displayCamera() {
    var obj = document.getElementById("cameraInfo");
    var selectedIndex = -1;
    obj.options.length = 0;
    for (var i = 0; i < _devInfos.length; i++) {
        obj.options.add(new Option(_devInfos[i].devName, _devInfos[i].id));
        if (selectedIndex < 0 && _devInfos[i].camMode == 0) {
            // 打到第一个主头
            selectedIndex = i;
        }
    }
    obj.options.selectedIndex = selectedIndex;
}

// 刷新显示 视频格式 下拉列表
function displayMediaType() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var mediaTypes = _devInfos[index1].mediaTypes;
    var obj = document.getElementById("mediaType");
    obj.options.length = 0;
    for (var i = 0; i < mediaTypes.length; ++i) {
        obj.options.add(new Option(mediaTypes[i].mediaType, i));
    }
    obj.options.selectedIndex = 0;
}

// 刷新显示 分辨率下拉列表
function displayResolution() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var index2 = document.getElementById("mediaType").options.selectedIndex;
    var obj = document.getElementById("resolution");
    var resolutions = _devInfos[index1].mediaTypes[index2].resolutions;
    var selectedIndex = -1;
    //清空resolution:select数据
    obj.options.length = 0;
    for (var i = 0; i < resolutions.length; i++) {
        obj.options.add(new Option(resolutions[i], i));
        if (selectedIndex < 0 && Between(resolutions[i].split('x')[0], 2000, 3000)) {
            // 打到第一个在2000到3000之间的分辨率
            selectedIndex = i;
        }
    }
    obj.options.selectedIndex = 0;
}

function switchCamera() {
    var reqId = new Date().getTime();
    if (_devNum >= 0) {
        //关闭之前的摄像头
        _myWs.SendJson({func:"CloseCamera", reqId: reqId++, devNum: _devNum});
    }

    //开启摄像头
    _devNum = $("#cameraInfo").val() == null ? 0 : parseInt($("#cameraInfo").val());
    var mediaType = parseInt($("#mediaType").val());
    var resolutionNum = parseInt($("#resolution").val());
    _myWs.SendJson({func:"OpenCamera", devNum:_devNum, mediaNum:mediaType, resolutionNum:resolutionNum, reqId:reqId});
}


// 打开(1)或关闭(0)人像摄像头,无参数时根据按钮上的文字确定
function OpenCloseCamera(val) {
    var open = val != 0;
    var now = new Date().getTime();
    if (_devNum >= 0) {
        // 打开之前先关闭,关闭前先停掉视频
        _myWs.SendJson({func:"GetCameraVideoBuff", devNum:_devNum, enable:false, reqId: now++});
        _myWs.SendJson({func:"CameraAutoCapture", devNum:_devNum, enable:false, reqId: now++});
        _myWs.SendJson({func:"CloseCamera", devNum:_devNum, reqId: now++});
        _devNum = -1;
    }
    if (open) {
        _devNum = $("#cameraInfo").val() == null ? 0 : parseInt($("#cameraInfo").val());
        var mediaType = parseInt($("#mediaType").val());
        var resolutionNum = parseInt($("#resolution").val());
        _myWs.SendJson({func:"OpenCamera", to:1, devNum:_devNum, mediaNum:mediaType, resolutionNum:resolutionNum, reqId: now++});
    }
}

function ReOpenCamera() {
    // 重新打开多功能终端上的文档摄像头
    OpenCloseCamera(1);
}

function Between(v, maxV, minV) {
    return v <= maxV && v >= minV;
}

function SwitchAuto() {
    var cmd = {func: "CameraAutoCapture", devNum: _devNum, reqId: new Date().getTime()};
    if (document.getElementById("btnAuto").innerText == "开始") {
        // 打开自动连拍, 自动检测图像变化（detectionType:0）、只发送通知不拍照（notify:1）
        cmd.enable = true;
        cmd.detectionType = 0;
        cmd.param = parseInt($("#param").val());
        cmd.notify = 1; // 0:不发送通知，这是缺省值。1:在触发时只发送通知，不执行拍照动作。这样应用程序可在收到通知后进行其他的动作，比如识别条码等等。在notify为1时，因为不需要拍照，所以isBook、mode、imagePath这三个参数会被忽略。2:在触发时即发送通知，也执行拍照动作。
        document.getElementById("btnAuto").disabled = true;
    } else {
        cmd.enable = false;
    }
    _myWs.SendJson(cmd);
}

var _lastVideoLeft = 0, _lastVideoTop = 0, _lastVideoW = 0, _lastVideoH = 0;
var _w = 0, _h = 0, _wp = 0, _hp = 0;
function DisplayVideo(v) {
    if (v.imgBase64Str) {
        if (_w != v.width || _h != v.height) {
            //设置视频显示的宽高（宽度控制为640px）
            _wp = 640, _hp = _wp * v.height / v.width, _w = v.width, _h = v.height;
            $("#video").css('width', _wp + 'px').css('height', _hp + 'px');
        }
        //显示视频
        document.getElementById("video").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    }
    // 显示视频中分线,用于对齐书本中缝
    var p = $("#video").offset();
    if (p.left != _lastVideoLeft || p.top != _lastVideoTop || _lastVideoW != _wp || _lastVideoH != _hp) {
        $("#vertical-line").css('top', p.top + 'px').css('left', (p.left + _wp/2 - 2) + 'px').css('height', _hp + 'px').css('display','');
        _lastVideoLeft = p.left, _lastVideoTop = p.top, _lastVideoW = _wp, _lastVideoH = _hp;
    }
}

function DisplayCaptureBook(v) {
    if (v.pageL || v.pageR) {
        audioCapture.play();
        displayOutputInfo(2, '拍书成功.', 1);
        if (v.pageL) {
            displayFile(v.pageL);
        }
        if (v.pageR) {
            displayFile(v.pageR);
        }
    } else {
        displayOutputInfo(2, '拍书没成功,不是书本.', 1);
    }
}

var _path = null;
var _th = '<tr><th>文件名</th><th>操作</th></tr>';
var _tdOpration = '<td><a onclick="vf()">查看</a> <a onclick="df()">删除</a></td>';
function displayFile(file) {
    var pos = file.lastIndexOf('\\');
    if (_path == null) {
        _path = file.substring(0, pos);
    }
    var html = '<tr><td>' + file.substring(pos + 1) + '</td>' + _tdOpration + '</tr>';
    $("#tabBody").append(html);
}

function DisplayImg(mime, imgBase64Str) {
    document.getElementById("imgView").src = "data:" + mime + ";base64," + imgBase64Str;
}

function vf() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    var file = _path + '\\' + $(target).parent().prev().text();

    // 把文件转为base64,用于显示查看
    _myWs.SendJson({func:'FileToBase64', filePath:file, reqId:new Date().getTime()});
}

function DisplayFileView(v) {
    document.getElementById("imgView").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    $('#myModal').modal();
}

// 删除拍照文件
function df() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    $(target).parent().parent().remove();
}

// 删除全部拍照文件
function clearImageFile() {
    $("#tabBody").empty();
}

function clearBarcode() {
    $("#tabBody2").empty();
}

//显示合并成功的PDF文件
function DisplayMergeFile(v) {
    if (v.result == 0) {
        if (v.filePath) {
            displayOutputInfo(2, (v.fileType == 0 ? "PDF" : "JPG") + "文件合并成功。" + v.filePath, 1);
            OpenFileByPath(v.filePath);
        }
    } else {
        displayOutputInfo(3, (v.fileType == 0 ? "PDF" : "JPG") + "文件合并失败。" + v.func + "<br>" + v.errorMsg, 1);
    }
}

function DisplayOcrFile(v) {
    if (v.result == 0) {
        if (v.filePath) {
            displayOutputInfo(2, "OCR识别成功。" + v.filePath);
            OpenFileByPath(v.filePath);
        }
    } else {
        displayOutputInfo(3, "文件OCR失败。" + v.func + "<br>" + v.errorMsg);
    }
}

function OpenFileByPath(filePath) {
    if (location.protocol.substr(0,4) == 'http') {
        var pos = filePath.lastIndexOf('\\');
        pos = filePath.lastIndexOf('\\', pos - 1);
        var url = location.origin + '/tmp/' + filePath.substr(pos + 1).replace('\\','/');
        window.open(url);
    }
}

function displayOutputInfo(disp, s, add) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        var h = "<b>" + s + "</b>";
        if (add) {
            var h0 = $('#outInfo').html();
            if (h0.length > 0) {
                h = h0 + "<br>" + h;
            }
        }
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html(h);
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function getImgFileArray() {
    var fileArray = new Array();
    var trs = $('#tabBody tr');
    for (i = 0; i < trs.length; i++) {
        fileArray.push(_path + '\\' + trs[i].children[0].innerText);
    }
    return fileArray;
}

function mergePDF() {
    // 合并PDF,合并的文件类型 fileType: 0 PDF; 1-TIFF; 2-JPG
    _myWs.SendJson({func: 'MergeFile', fileType:0, imagePath:getImgFileArray(), reqId: new Date().getTime()});
    displayOutputInfo(1, 'PDF文件合并中...');
}

function ocrFile() {
    // OCR识别
    var strLang = $("#language").find("option:selected").text(), strExtName = $("#fileType").find("option:selected").text();
    var chkTxtOri = $("#detectTextOrie")[0].checked;
    _myWs.SendJson({func: 'FileOCR', language:strLang, extName:strExtName, detectTextOrientation:chkTxtOri,
            imagePath:getImgFileArray(), mode: 'base64&path', reqId: new Date().getTime()});
    displayOutputInfo(1, 'OCR识别中...');
}

$(function () {
    var now = new Date().getTime();
    // 获取OCR支持信息
    _myWs.SendJson({func:"GetOcrSupportInfo", reqId:now++});
    // 获取摄像头的设备信息
    _myWs.SendJson({func:"GetCameraInfo", reqId:now});
});

$(window).unload(function(){
    // 关闭页面之前,关闭摄像头
    if (_devNum >= 0) {
        OpenCloseCamera(0);
    }
});
