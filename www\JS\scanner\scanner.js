/**
*--------------------------扫描仪--------------------------
**/
//获取扫描仪下标
var devNum = 0;

//初始化信息
$(function () {
    //初始化隐藏扫描设置属性
    initScanAttributehHide_Fun();
    //获取扫描仪信息
    GetScannerName();
    //初始化图片列表的高度
    initImageDataHeight();
})

//初始化图片列表的高度
function initImageDataHeight(){
    var nclientHeight = document.body.clientHeight-20;//容器区域尺寸
    $("#right_div").css('height',nclientHeight+'px');
}

//根据设备切换扫描仪扫描设置信息
function switchScanInfo_Fun(nType)
{   
    if(nType == 0){
        //关闭扫描仪
        // CloseScanner("",parseInt(devNum)); 
		//更新扫描仪设备的值
		getDevNum_Fun();
        //初始化隐藏扫描设置属性
        initScanAttributehHide_Fun();
    } 
    //开启扫描仪
    // openScanner_Fun();
    //获取扫描仪参数
    GetScannerInfo("",parseInt(devNum));
}

function scan_Fun(){
    //设置扫描仪参数
    // setScannerInfo_Fun();
    //设置扫描仪图像算法参数
    setScannerImageInfo_Fun();
    //扫描接口    
    scan_data_Fun();
}

/** 扫描接口
    reqId 时序号
    devNum 扫描仪序号
    ScanSource 扫描模式
    ScanMode 色彩模式
    ScanResolution 分辨率
    ScanPaperSize 扫描尺寸
**/
function scan_data_Fun(){
     //初始化时数据可能为空，默认为０
     var scanSource = $("#scan_source").val() == null ? 0 : $("#scan_source").val();
     var scanMode = $("#scan_mode").val() == null ? 0 : $("#scan_mode").val();
     var scanResolution = $("#scan_resolution").val() == null ? 0 : $("#scan_resolution").val();
     var scanPaperSize = $("#scan_paper").val() == null ? 0 : $("#scan_paper").val();
     Scan("",parseInt(devNum),parseInt(scanSource),parseInt(scanMode),parseInt(scanResolution),parseInt(scanPaperSize));
}

function getDevNum_Fun(){
	//获取设备的值,打开特定的设备 
    devNum = $("#scanInfo").val();
}

// function openScanner_Fun(){
//     OpenScanner("",parseInt(devNum));
// }

/** 设置扫描仪参数
    ScanSource 扫描模式
    ScanMode 色彩模式
    ScanResolution 分辨率
    ScanPaperSize 扫描尺寸
**/
// function setScannerInfo_Fun(){
//     //初始化时数据可能为空，默认为０
//     var scanSource = $("#scan_source").val() == null ? 0 : $("#scan_source").val();
//     var scanMode = $("#scan_mode").val() == null ? 0 : $("#scan_mode").val();
//     var scanResolution = $("#scan_resolution").val() == null ? 0 : $("#scan_resolution").val();
//     var scanPaperSize = $("#scan_paper").val() == null ? 0 : $("#scan_paper").val();
//     SetScannerInfo("",parseInt(scanSource),parseInt(scanMode),parseInt(scanResolution),parseInt(scanPaperSize));
// }
/**　设置扫描仪图像设置参数
    CropType 裁切：0-不裁切  1-单图裁切　２－多图裁切　３－曲面矫正
    ImageType 图像效果：0-彩色原色 1-灰度图片 2-黑白文档　３－　彩色文档　４－红印文档　５－蓝印文档　６－彩色照片　７－票据增强
    FillBorderType 补边：0-不填充 1-自动填充白色　２－自动填充周边色
**/
function setScannerImageInfo_Fun(){
    var cropType = $("#crop_type").val();
    var imageType = $("#color_type").val();
    var fillBorderType = $("#fill_border").val();
    var rotationAngle = $("#rotation_angle option:selected").text();
    SetScannerImageInfo("",parseInt(cropType),parseInt(imageType),parseInt(fillBorderType),0,parseInt(rotationAngle));
} 

//切换设置详细属性
function switchSetDetail_Fun(setType)
{   
    if(setType == 1){
        $("#scanSet").show();
        $("#imageProcess").hide();
        $("#scanSetButton").css("background","#f7f8fb");
        $("#imageProcessButton").css("background","");
    }else if(setType == 2){
        $("#scanSet").hide();
        $("#imageProcess").show();
        $("#scanSetButton").css("background","");
        $("#imageProcessButton").css("background","#f7f8fb");
    }
}

//初始化隐藏扫描设置属性
function initScanAttributehHide_Fun(){
     $("#scan_source").hide();
     $("#scan_source_lable").hide();
     $("#scan_mode").hide(); 
     $("#scan_mode_lable").hide();
     $("#scan_paper").hide(); 
     $("#scan_paper_lable").hide();  
     $("#scan_resolution").hide(); 
     $("#scan_resolution_lable").hide();  
}

//初始化设置扫描仪设置信息
function initScanSetInfo_Fun(scanSourceList,scanModeList,scanResolutionList,scanPaperList){
    //扫描模式如果为空，返回的值为－１，隐藏掉扫描模式
    if(scanSourceList != -1){
        $("#scan_source").show();
        $("#scan_source_lable").show();
        //初始化扫描仪设置信息为空
        document.getElementById("scan_source").options.length = 0;
        for(var i = 0;i < scanSourceList.length; i++){
            var scanSource = scanSourceList[i];
            document.getElementById("scan_source").options.add(new Option(scanSource, i));
        }
    }
    
    //色彩模式如果为空，返回的值为－１，隐藏掉色彩模式
    if(scanModeList != -1){
        $("#scan_mode").show(); 
        $("#scan_mode_lable").show();
        //初始化扫描仪设置信息为空 
        document.getElementById("scan_mode").options.length = 0;
        for(var i = 0;i < scanModeList.length; i++){
            var scanMode = scanModeList[i];
            document.getElementById("scan_mode").options.add(new Option(scanMode, i));
        }
    }
    
    //分辨率如果为空，返回的值为－１，隐藏掉色彩模式
    if(scanResolutionList != -1){
        $("#scan_resolution").show(); 
        $("#scan_resolution_lable").show(); 
        //初始化扫描仪设置信息为空
        document.getElementById("scan_resolution").options.length = 0;
        for(var i = 0;i < scanResolutionList.length; i++){           
            var scanResolution = scanResolutionList[i];
            document.getElementById("scan_resolution").options.add(new Option(scanResolution, i));
        }
    }

    //扫描尺寸如果为空，返回的值为－１，隐藏掉扫描尺寸
    if(scanPaperList != -1){
        $("#scan_paper").show(); 
        $("#scan_paper_lable").show(); 
        //初始化扫描仪设置信息为空
        document.getElementById("scan_paper").options.length = 0;
        for(var i = 0;i < scanPaperList.length; i++){
            var scanPaper = scanPaperList[i];
            document.getElementById("scan_paper").options.add(new Option(scanPaper, i));
        }
    }

    
}

//添加图片项并展示列表
function addImageItem_Fun(imgBase64Str){
    var imgBase64Str = "data:image/png;base64," + imgBase64Str;
    var image = '<img width="100" height="75" style="padding: 10px 10px 5px 5px;" onclick="clickImage_Fun('+imgBase64Str+')" src="'+imgBase64Str+'">';
    $("#image_data").append(image);
}

function clickImage_Fun(imgBase64Str){
    debugger;
    var myimg = document.getElementById("pic");
    myimg.src = imgBase64Str;
}

//隐藏遮罩层等待ｇｉｆ
function maskLayerHide(){
    $("#mask_layer").css("visibility","hidden");
}

//接收并处理后台发送的信息
function produceMessage(jsonObj) {
    if(jsonObj.errorMsg != null){
        //
        maskLayerHide();
        alert(jsonObj.errorMsg);
        return;
    }

    var funcName = jsonObj.func;
    console.log(Lang.MSG.funcName + funcName);
    
    if(funcName == "GetScannerName"){
        //隐藏遮罩层等待ｇｉｆ
        maskLayerHide();
        //解除扫描禁用控制
        $("#scan").attr("disabled",false);       
        var scannerNameList = jsonObj.scannerName;
        for(var i = 0;i < scannerNameList.length; i++){                
            var scannerName = scannerNameList[i];
            console.log(scannerName);
            document.getElementById("scanInfo").options.add(new Option(scannerName, i)); 
        }
        switchScanInfo_Fun(1);
    }else if(funcName == "GetScannerInfo"){
        //初始化设置扫描仪设置信息
        initScanSetInfo_Fun(jsonObj.scanSource,jsonObj.scanMode,jsonObj.scanResolution,jsonObj.scanPaper);
    }else if(funcName == "Scan"){
        debugger;
        var videoStr = jsonObj.imgBase64Str;
        if (videoStr == null){
            alert(Lang.scan.base64Null);
            return;
        }
        //console.log(videoStr);
        $("#pic").css('width',640+'px');//设置视频控件的宽高
        $("#pic").css('height',480+'px'); 

        var myimg = document.getElementById("pic");
        myimg.src = "data:image/png;base64," + videoStr;
        
        //添加图片项并展示列表
        addImageItem_Fun(videoStr);
    }else if(funcName == "GetScanEndStatue"){
        var scanEndStatue = jsonObj.scanEndStatue;
        if(scanEndStatue == 0){
            alert(Lang.scan.finish);
            return;
        }   
    }
}

