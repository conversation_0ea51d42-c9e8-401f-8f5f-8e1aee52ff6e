<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>评价按键读取演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="FileDisplay.html">文件播放</a></li>
                <li><a href="IdCard.html">身份证读取</a></li>
                <li><a href="ReadFinger.html">指纹读取</a></li>
                <li><a href="IdCardMatch.html">人证比对</a></li>
                <li><a href="DocOCR.html">OCR识别与水印</a></li>
                <li><a href="ReadSign.html">手写签名</a></li>
                <li class="active"><a href="#">评价按键读取</a></li>
                <li><a href="Barcode.html">条码扫描与识别</a></li>
                <li><a href="LayoutRecognition.html">版面识别</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/W7-0911.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>评价按键读取 演示</h1>
        <p>些页面演示了如何读取安卓多功能终端上的按键，一般用于客户满意度的评价。<br>
            按键的取值为：非常满意(0)、满意(1)、一般(2)、不满意(3)、非常不满意(4)。</p>
    </div>

    <div class="page-header">
    </div>

    <div class="col-md-12" style="text-align: center">
        <br><br>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2423" width="64" height="64"><path d="M987.429 369.714q0 12.572-14.858 27.429L765.143 599.429l49.143 285.714q0.571 4 0.571 11.428 0 12-6 20.286t-17.428 8.286q-10.858 0-22.858-6.857L512 783.429 255.429 918.286q-12.572 6.857-22.858 6.857-12 0-18-8.286t-6-20.286q0-3.428 1.143-11.428l49.143-285.714-208-202.286q-14.286-15.429-14.286-27.429 0-21.143 32-26.285l286.858-41.715L484 41.714q10.857-23.428 28-23.428t28 23.428l128.571 260L955.43 343.43q32 5.142 32 26.285z" p-id="2424" fill="#e6e6e6"></path></svg>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2423" width="64" height="64"><path d="M987.429 369.714q0 12.572-14.858 27.429L765.143 599.429l49.143 285.714q0.571 4 0.571 11.428 0 12-6 20.286t-17.428 8.286q-10.858 0-22.858-6.857L512 783.429 255.429 918.286q-12.572 6.857-22.858 6.857-12 0-18-8.286t-6-20.286q0-3.428 1.143-11.428l49.143-285.714-208-202.286q-14.286-15.429-14.286-27.429 0-21.143 32-26.285l286.858-41.715L484 41.714q10.857-23.428 28-23.428t28 23.428l128.571 260L955.43 343.43q32 5.142 32 26.285z" p-id="2424" fill="#e6e6e6"></path></svg>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2423" width="64" height="64"><path d="M987.429 369.714q0 12.572-14.858 27.429L765.143 599.429l49.143 285.714q0.571 4 0.571 11.428 0 12-6 20.286t-17.428 8.286q-10.858 0-22.858-6.857L512 783.429 255.429 918.286q-12.572 6.857-22.858 6.857-12 0-18-8.286t-6-20.286q0-3.428 1.143-11.428l49.143-285.714-208-202.286q-14.286-15.429-14.286-27.429 0-21.143 32-26.285l286.858-41.715L484 41.714q10.857-23.428 28-23.428t28 23.428l128.571 260L955.43 343.43q32 5.142 32 26.285z" p-id="2424" fill="#e6e6e6"></path></svg>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2423" width="64" height="64"><path d="M987.429 369.714q0 12.572-14.858 27.429L765.143 599.429l49.143 285.714q0.571 4 0.571 11.428 0 12-6 20.286t-17.428 8.286q-10.858 0-22.858-6.857L512 783.429 255.429 918.286q-12.572 6.857-22.858 6.857-12 0-18-8.286t-6-20.286q0-3.428 1.143-11.428l49.143-285.714-208-202.286q-14.286-15.429-14.286-27.429 0-21.143 32-26.285l286.858-41.715L484 41.714q10.857-23.428 28-23.428t28 23.428l128.571 260L955.43 343.43q32 5.142 32 26.285z" p-id="2424" fill="#e6e6e6"></path></svg>
        <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2423" width="64" height="64"><path d="M987.429 369.714q0 12.572-14.858 27.429L765.143 599.429l49.143 285.714q0.571 4 0.571 11.428 0 12-6 20.286t-17.428 8.286q-10.858 0-22.858-6.857L512 783.429 255.429 918.286q-12.572 6.857-22.858 6.857-12 0-18-8.286t-6-20.286q0-3.428 1.143-11.428l49.143-285.714-208-202.286q-14.286-15.429-14.286-27.429 0-21.143 32-26.285l286.858-41.715L484 41.714q10.857-23.428 28-23.428t28 23.428l128.571 260L955.43 343.43q32 5.142 32 26.285z" p-id="2424" fill="#e6e6e6"></path></svg>
        <br><br>
        <p id="evalGrade">客户评价星级</p>
        <br><br>
        <p><button type="button" class="btn btn-primary" onclick="ReadEvaluateKey()">评价按键读取</button></p>
        <br><br>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/ReadKeyboard.js"></script>
</body>
</html>