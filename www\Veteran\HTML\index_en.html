<!DOCTYPE html>
<html lang="en">
<head>
    <title>Doc Camera</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/en.js"></script>
    <script src="../JS/index.js"></script>
    <link rel="stylesheet" href="../CSS/index.css"/>
</head>
<body>
<div class="ai-platform">
    <div id="apiModuleBanner" class="ai-module-banner">
        <div class="ai-module-banner-content">
            <div class="ai-module-banner-title">Demonstration The Doc Camera SDK</div>
            <div class="ai-module-banner-info">Support CameraSDK 、ScannerSDK 、IDCard reader</div>
            <div id="verInfo">Version information</div>
        </div>
    </div>
    <div class="ai-module-demo" id="aiModuleContent">
       
        <div class="ai-module-demo-container" id="aiModuleContainer">
            <div class="ai-module-demo-container-line">
                <div class="cmeraSDK">
                    <div id='icon1' class="icon1">
                        <img id="cmeraSDKIcon" src="../image/CameraSDK.png" >
                    </div>
                    <span>CameraSDK</span>
                    <p><a href="camera/VisualCamera_EN.html" title="Demonstrate the functions of the camera, cutting, image processing, etc">Doc Camera</a></p>
                    <p><a href="camera/VisualCamera_EN.html" title="Demonstrate bar code recognition with a high-resolution camera">Bar Code Identification</a></p>
                    <p><a href="camera/CaptureBook_en.html" title="The demonstration that capture book with Document Camera">Book capture</a></p>
                    <p><a href="camera/AutoCapture_en.html" title="The demonstration that auto Capture with Document Camera">Auto continuous capture</a></p>
                </div>
                <div class="scannerSDK" onclick="pageJump('scanner/VisualScanner.html')">
                    <div id='icon2' class="icon2">
                        <img id="scannerSDKIcon" src="../image/ScannerSDK.png">
                    </div>
                    <span>ScannerSDK</span>
                </div>
                <div class="idCardSDK">
                    <div id='icon3' class="icon3">
                        <img id="idCardSDKIcon" src="../image/idCard.png">
                    </div>
                    <span>IdCardSSDK</span>
                    <p><a href="camera/IdFace_EN.html" title="Demonstrate the comparison of people's ID cards through high-speed camera, ID card reader and binocular camera">Face Comparison</a></p>
                    <p><a href="ocx/ocxDemo_EN.html" title="Demonstrate how to operate the DocCamera through ocx control in IE browser (IE11)">DocCamera Basic Function For OCX</a></p>
                    <p><a href="ocx/ocxIdFace_EN.html" title="Demonstrate how to Face Comparison in IE browser (IE11) through ocx control using high timer, ID card reader and binocular camera">Face Comparison For OCX</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>

