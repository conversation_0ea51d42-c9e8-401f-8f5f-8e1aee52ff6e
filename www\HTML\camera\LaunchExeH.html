<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>页成中启动Exe进行拍照 - 演示</title>
    <!--[if (gte IE 9)|!(IE)]><!-->
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <!--<![endif]-->
    <!--[if lte IE 8]>
    <script src="../../JS/common/jquery-1.9.1.min.js"></script>
    <script src="../../JS/common/json2.js"></script>
    <![endif]-->
    <script src="../../JS/camera/LaunchExeH.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/LaunchExe.css"/>
</head>
<body>
<h2>启动Exe进行拍照</h2>
<p>此Demo演示了如何在页面中启动一个exe程序进行拍照。这适用于业务不需要指定拍照的各种参数，完全由操作人员灵活处理的场景。目前仅支持SmartCapture这一种exe程序。</p>
<button id="btnStart" onclick="launchExe('SmartCapture')">启动SmartCapture拍照</button>
<br>
<div id="outInfo" style="display:none"></div>
<p>返回的图片：</p>
<div id="divImages">
</div>
</body>
</html>
