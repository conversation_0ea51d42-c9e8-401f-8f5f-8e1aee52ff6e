

//加载页面完成
window.onload = function () {

	 load();
}
//页面卸载
window.onunload = function () {
	unload();
}


//<!--设置保存路径-->
function SetSaveFolder()
{
	saveFolder.href = path.value;
}


//<!--读卡-->
function ReadIDCard()
{
	
	var myDate = new Date();
	var myName = "IDcardHeaderPic"+myDate.getFullYear()+(myDate.getMonth()+1)+myDate.getDate()+"_"+myDate.getHours()+myDate.getMinutes()+myDate.getSeconds()+myDate.getMilliseconds();	
	m_IDcardHeaderPic = path.value + "\\" + myName + ".bmp" ;
	
	ReadIDCardPath(m_IDcardHeaderPic);
	
}

function ReadBank()
{
	ReadBankCard();
}

function ReadSse()
{
	ReadSBKCard();
}

function CombineIDCardImage()
{
	
	var m_width ,m_height;
	var myDate = new Date();
	var myNameF = "Front"+myDate.getFullYear()+(myDate.getMonth()+1)+myDate.getDate()+"_"+myDate.getHours()+myDate.getMinutes()+myDate.getSeconds()+myDate.getMilliseconds();	
	var myNameB = "Back"+myDate.getFullYear()+(myDate.getMonth()+1)+myDate.getDate()+"_"+myDate.getHours()+myDate.getMinutes()+myDate.getSeconds()+myDate.getMilliseconds();		
	var nType = document.getElementById("CombineType").value;	
	m_strFileFront = path.value + "\\" + myNameF + ".jpg";
	m_strFileBack = path.value + "\\" + myNameB +".jpg";
	
	
	GetIDCardImage(nType,m_strFileFront,m_strFileBack);
		
	
	
}	
//<!--读指纹（身份证）-->
function ReadIDCardFingerprint()
{
	m_IDcardFPBase64 = "";
	var myDate = new Date();
	var myName = "IDcardFPFile_"+myDate.getFullYear()+(myDate.getMonth()+1)+myDate.getDate()+"_"+myDate.getHours()+myDate.getMinutes()+myDate.getSeconds()+myDate.getMilliseconds();
	m_IDcardFPFile = path.value + "\\" + myName + ".bin" ;
	GetIDCardOneFingerData(m_IDcardFPFile);
	
}

function UnInitIDCardRFID()
{
	CamSDKOCX.UnInitIDCardRFID();
}
//<!--采集指纹-->
function GetFingerprint(index)
{

	var myDate = new Date();
	var myName = "_"+myDate.getFullYear()+(myDate.getMonth()+1)+myDate.getDate()+"_"+myDate.getHours()+myDate.getMinutes()+myDate.getSeconds()+myDate.getMilliseconds();
	m_fingerIndex = index;
	if(index == 1)
	{
		m_finger1Base64 = "";
		m_finger1Bmp = path.value + "\\finger1" + myName + ".bmp" ;
		m_finger1File = path.value + "\\finger1" + myName + ".bin" ;
		
		GetFingerPicFromeMsg(m_finger1Bmp,m_finger1File);
	
	}
	else if(index == 2)
	{
		m_finger2Base64 = "";
		m_finger2Bmp = path.value + "\\finger2" + myName + ".bmp" ;
		m_finger2File = path.value + "\\finger2" + myName + ".bin" ;
		GetFingerPicFromeMsg(m_finger2Bmp,m_finger2File);
	
	}
}
//<!--比对指纹特征值-->
function Contrast(n)
{	
	var ret = -1;

	if(1 == n)
	{
		
		ComperaFingerPic(m_finger1File,m_finger2File);
	}
	else if(2 == n)
	{
		
		ContrastFingerprintBase64(m_finger1Base64,m_finger2Base64);
	}
	else if(3 == n)
	{
		//alert(IDcardFPFile+"\n"+finger1File);
		 ComperaFingerPic(m_IDcardFPFile,m_finger1File);
	}
	else if(4 == n)
	{
		//alert(IDcardFPBase64+"\n"+finger1Base64);
		 ContrastFingerprintBase64(m_IDcardFPBase64,m_finger1Base64);
	}
	else
	{
		alert("错误，Contrast("+n+")没有可执行代码");
		return;
	}

}


function UnInitFingerprint()
{
	if(IsInitFingerprint == 1)
	{
		CamSDKOCX.UnInitFingerprint();
		IsInitFingerprint = 0;
	}
}