import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/worker/doQuery',
    method: 'post',
    data: query
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/worker/' + parseStrEmpty(userId),
    method: 'get'
  })
}
//查询用户部门
export function getWorkorg(workerOrgId){
  return request({
    url: '/workerorg/' + parseStrEmpty(workerOrgId),
    method: 'get'
  })
}

//根据用户查询部门
export function getOrgByWork(){
  return request({
    url: '/workerorg/queryAllByWork',
    method: 'get'
  })
}

// 查询用户部门列表
export function listWorkorg(query) {
  return request({
    url: '/workerorg/doQuery',
    method: 'post',
    data: query
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/worker/doAdd',
    method: 'post',
    data: data
  })
}

//新增用户部门
export function addWorkorg(data){
  return request({
    url: '/workerorg/doAdd',
    method: 'post',
    data: data
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/worker/doEdit',
    method: 'post',
    data: data
  })
}

//修改用户部门
export function updateWorkorg(data) {
  return request({
    url: '/workerorg/doEdit',
    method: 'post',
    data: data
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/worker/doDelete/' + userId,
    method: 'post'
  })
}

//删除用户部门
export function delWorkorg(workerOrgId) {
  return request({
    url: '/workerorg/doDelete/' + workerOrgId,
    method: 'post'
  })
}

//查询用户的部门
export function getWorkerOrg(query){
  return request({
    url: '/workerorg/doQuery',
    method: 'post',
    params: query
  })
}

// 查询用户职业体检列表
export function listCareer(query) {
  return request({
    url: '/carcheck/doQuery',
    method: 'post',
    data: query
  })
}

//新增职业体检
export function addCareer(data){
  return request({
    url: '/carcheck/doAdd',
    method: 'post',
    data: data
  })
}

//删除职业体检
export function delCareer(carcheckId) {
  return request({
    url: '/carcheck/doDelete/' + carcheckId,
    method: 'post'
  })
}


//根据组织查询人员
export function queryOrgWork(organId) {
  return request({
    url: '/workerorg/queryOrgWork/' + organId,
    method: 'get'
  })
}


// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data
  })
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data: data
  })
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  })
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  })
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/organization/getTree',
    method: 'get'
  })
}
