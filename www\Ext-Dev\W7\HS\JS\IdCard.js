var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "ReadIDCardInfo":
            DisplayIdCardInfo(jsonObj);
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function DisplayIdCardInfo(v) {
/*  {
        "func": "ReadIDCardInfo",
        "reqId": "223387",
        "result": 0,
        "cardInfo": {
            "id": "430xxxxxxxxxxxxxxx",
            "name": "xxx",
            "folk": "汉",
            "gender": "男",
            "address": "长沙市xxxxxx",
            "birth": "19851025",
            "startDate": "20091013",
            "endDate": "20291013",
            "fingerFeature": "",
            "imageBase64": "/9j/4AA......//Z",
            "imagePath": "F:\\CamSDK\\release_ws\\tmp\\02ca9ee8\\1628740497.jpg",
            "issue": "长沙市xxxxxx分局",
            "mime": "image/jpeg"
        }
    } */
    console.log(v);
    if (v.result == 0) {
        if (v.cardInfo) {
            displayOutputInfo(2, "身份证读取成功");
            var ci = v.cardInfo;
            $("#name").text(ci.name);
            $("#gender").text(ci.gender);
            $("#folk").text(ci.folk);
            $("#birth").text(ci.birth);
            $("#address").text(ci.address);
            $("#ID").text(ci.id);
            $("#issue").text(ci.issue);
            $("#startEndDate").text(ci.startDate + " - " + ci.endDate);
            document.getElementById("pic").src = "data:" + ci.mime + ";base64," + ci.imageBase64;
        }
        if (v.jsonFile) {
            // 发消息给终端设备,回显身份证信息
            var ary = v.jsonFile.split("\\"), n = ary.length;
            var url = "http://" + v.serverIP + ":" + location.port + "/Ext-Dev/echo/IdCard.html?client=" + ary[n-2] +"&file=" + ary[n-1];
            SendJson({func:"Navigation", to:1, url:url, reqId: new Date().getTime()});

            // for debug
            //window.open(url);
        }
    } else {
        displayOutputInfo(3, "身份证读取失败。" + jsonObj.func + "<br>" + jsonObj.errorMsg);
    }
}

function ReadIdCard() {
    // 提示信息显示
    displayOutputInfo(1, "身份证读取中...... 请在多功能终端上放置身份证");
    // 读取身份证, to属性为1，表示经"ws://127.0.0.1:9000"转发给"安卓多功能终端"上的服务来处理
    SendJson({func:"ReadIDCardInfo", to:1, idType:0, saveJson:1, reqId: new Date().getTime()});
}


