import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询隐患列表
export function listblackspothand(query) {
    return request({
      url: '/blackspothand/doQuery',
      method: 'post',
      data: query
    })
}


  // 查询隐患详细
export function getblackspothand(blackspothandId) {
    return request({
      url: '/blackspothand/' + parseStrEmpty(blackspothandId),
      method: 'get'
    })
}

  // 查询整改详细
export function getblackspotamend(blackspotamendId) {
    return request({
      url: '/blackspotamend/' + parseStrEmpty(blackspotamendId),
      method: 'get'
    })
}

  // 查询验收详细
export function getVerifyByHand(blackspothandId) {
    return request({
      url: '/blackspotverify/getVerifyByHand/' + parseStrEmpty(blackspothandId),
      method: 'get'
    })
}




  // 根据隐患id查询整改详细
export function getAmendByHand(blackspothandId) {
    return request({
      url: '/blackspotamend/getByHand/' + parseStrEmpty(blackspothandId),
      method: 'get'
    })
}


// 新增隐患
export function addblackspothand(data) {
    return request({
      url: '/blackspothand/doAdd',
      method: 'post',
      data: data
    })
}

// 新增整改
export function addblackspotamend(data) {
  return request({
    url: '/blackspotamend/doAdd',
    method: 'post',
    data: data
  })
}

// 新增验收
export function addblackspotverify(data) {
  return request({
    url: '/blackspotverify/doAdd',
    method: 'post',
    data: data
  })
}

// 修改隐患
export function updateblackspothand(data) {
    return request({
      url: '/blackspothand/doEdit',
      method: 'post',
      data: data
    })
}

// 修改整改
export function updateblackspotamend(data) {
  return request({
    url: '/blackspotamend/doEdit',
    method: 'post',
    data: data
  })
}

// 修改整改
export function updateblackspotverify(data) {
  return request({
    url: '/blackspotverify/doEdit',
    method: 'post',
    data: data
  })
}


// 删除隐患
export function delblackspothand(blackspothandId) {
  return request({
    url: '/blackspothand/doDelete/' + blackspothandId,
    method: 'post'
  })
}

