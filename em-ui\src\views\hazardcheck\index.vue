<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="报告名称" prop="className">
        <el-input
          v-model="queryParams.checkName"
          placeholder="请输入报告名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报告编号" prop="checkNo">
        <el-input
          v-model="queryParams.checkNo"
          placeholder="请输入报告编号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="checkList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="报告编号" align="center" prop="checkNo" :show-overflow-tooltip="true" />
      <el-table-column label="报告名称" align="center" :show-overflow-tooltip="true">
        <template slot-scope="scope">
            <span>{{ scope.row.checkName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="关键字" align="center" prop="keywords" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">已发布</span>
          <span v-if="scope.row.state==2">草稿</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"

          >删除</el-button>
           <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDownload(scope.row)"

          >下载</el-button>
        </template>
       
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="报告名称" prop="checkName">
          <el-input v-model="form.checkName" placeholder="请输入报告名称" />
        </el-form-item>
        <el-form-item label="报告编号" prop="checkNo">
          <el-input v-model="form.checkNo" placeholder="请输入报告编号" />
        </el-form-item>
        <el-form-item label="关键词" prop="keywords">
          <el-input v-model="form.keywords" placeholder="请输入关键词" />
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input  type="textarea" v-model="form.content" placeholder="请输入内容" maxlength="1000"/>
        </el-form-item>
        <el-form-item label="备注" prop="description">
          <el-input  type="textarea" v-model="form.description" placeholder="请输入备注" maxlength="500"/>
        </el-form-item>
        <el-form-item label="状态">
              <el-radio-group v-model="form.state">
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
        </el-form-item>
        <el-form-item label="文档" prop="filepaths">
            <file-upload v-model="filepaths" :limit="5"  />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="title" :visible.sync="fileopen" width="780px" append-to-body>
       <div v-for="o in filedowns" :key="o" >
         <a :href="o.filepath" target="_blank"> {{ o.fileName }}</a>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCheck, getCheck, addCheck,updateCheck, delCheck } from "@/api/hazardcheck/hazardcheck";

export default {
  name: "Dict",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 职业危害检测表格数据
      checkList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        checkName: undefined,
        checkNo:undefined
      },
       //文档路径
      filepaths:[],
      //文档下载路径
      filedowns:[],
       //是否显示下载弹出层
      fileopen:false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        checkName: [
          { required: true, message: "报告名称不能为空", trigger: "blur" }
        ],
        checkNo: [
          { required: true, message: "报告编号不能为空", trigger: "blur" }
        ]
      },
         //状态
      options: [{
          value: 1,
          label: '已发布'
        }, {
          value: 2,
          label: '草稿'
      }],
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询字典类型列表 */
    getList() {
      this.loading = true;
      listCheck(this.queryParams).then(response => {
          this.checkList = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        hazardcheckId: undefined,
        state: 1,
        checkNo: undefined,
        checkName: undefined,
        keywords: undefined,
        content: undefined,
        description: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.filepaths=undefined
      this.open = true;
      this.title = "添加职业危害因素检测";
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cmsclassId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.filepaths=[]
      const hazardcheckId = row.hazardcheckId || this.ids
      getCheck(hazardcheckId).then(response => {
        this.form = response.data;
        if(typeof(this.form.filepaths)!="undefined"){
          for(let i=0; i<this.form.filepaths.length; i++){
              let filepath = {}
              filepath.name=this.form.filepaths[i].fileName
              filepath.url=this.form.filepaths[i].filePath
              this.filepaths.push(filepath);
          }
        }
        this.open = true;
        this.title = "修改职业危害因素检测";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {

          if(typeof(this.filepaths) !=="undefined" && this.filepaths.length > 0){
             this.form.filepaths=[]
            for(let i=0; i<this.filepaths.length; i++){
              let filepath = {};
              filepath.filePath=this.filepaths[i].url;
              filepath.fileName=this.filepaths[i].name;
              this.form.filepaths.push(filepath);
            }
          }
          if (this.form.hazardcheckId != undefined) {
            updateCheck(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCheck(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const hazardcheckId = row.hazardcheckId || this.ids
      this.$modal.confirm('是否确认职业危害因素检测？').then(function() {
        return delCheck(hazardcheckId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
      /**文档下载弹出操作 */
    handleDownload(row){
      const hazardcheckId = row.hazardcheckId || this.ids
      getCheck(hazardcheckId).then(response => {
        this.form = response.data;
        if(typeof(this.form.filepaths)!="undefined"){
          let files = [];
          for(let i=0; i<this.form.filepaths.length; i++){
            let filedown = {}
            filedown.filepath = process.env.VUE_APP_BASE_API+this.form.filepaths[i].filePath;
            filedown.fileName= this.form.filepaths[i].fileName;
            files.push(filedown)
          }
          this.filedowns=files
        }
        this.fileopen = true;
        this.title = "文档下载";
      });
    }



  }
};
</script>