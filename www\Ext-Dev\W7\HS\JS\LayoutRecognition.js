var _url, _ws;
var _connected = false;
var _devInfos = null;
var _devNum = -1;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "GetCameraInfo":
            DisplayDevInfo(jsonObj);
            break;
        case "OpenCamera":
            if (jsonObj.result == 0 || jsonObj.result != 9 || jsonObj.result != -2) {
                // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常， -2:camera is open,也忽略这个异常
                var reqId = new Date().getTime();
                // 打开多功能终端上的文档摄像头成功后，获取文档摄像头的视频
                SendJson({func:"GetCameraVideoBuff", to:1, devNum:_devNum, enable:"true", reqId:reqId++});
                SetCameraInfo();
            } else {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
        case "GetCameraVideoBuff":
            // 获取到人像摄像头的视频,显示
            DisplayVideo(jsonObj);
            break;
        case "CameraCapture":
            DisplayCaptureImg(jsonObj);
            break;
        case "RecogLayout":
            DisplayLayoutResult(jsonObj);
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function DisplayDevInfo(v) {
    if (v.result == 0) {
        _devInfos = v.devInfo;
        if (_devInfos.length == 0) {
            displayOutputInfo(3, "获取设备信息时，返回的devInfos为空.");
            return;
        }

        displayCamera();
        displayResolution();
        OpenCloseCamera(1);
    } else {
        displayOutputInfo(3, v.func + "<br>" + v.errorMsg);
    }
}

// 刷新显示 设备名称 下拉列表
function displayCamera() {
    var obj = document.getElementById("cameraInfo");
    var lastSelectedIndex = obj.options.selectedIndex;
    obj.options.length = 0;
    for (var i = 0; i < _devInfos.length; i++) {
        obj.options.add(new Option(_devInfos[i].devName, _devInfos[i].id));
        if (lastSelectedIndex == -1 && _devInfos[i].camMode == 0) {
            // 默认打开文档摄像头
            lastSelectedIndex = i;
        }
    }
    if (lastSelectedIndex >= 0 && lastSelectedIndex < _devInfos.length) {
        obj.options.selectedIndex = lastSelectedIndex;
    }
}

// 刷新显示 分辨率下拉列表
function displayResolution() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var index2 = 0;
    var obj = document.getElementById("resolution");
    var resolutions = _devInfos[index1].mediaTypes[index2].resolutions;
    //清空resolution:select数据
    obj.options.length = 0;
    var minWidth = _devInfos[index1].camMode == 0 ? 2048 : 1280;
    var maxWidth = _devInfos[index1].camMode == 0 ? 3200 : 1920;
    var resIndex = -1;
    for (var i = 0; i < resolutions.length; i++) {
        obj.options.add(new Option(resolutions[i], i));
        if (resIndex < 0) {
            var ary = resolutions[i].split('x');
            if (ary[0] >= minWidth && ary[0] < maxWidth ) {
                // 默认使用2048以上的第一个分辨率
                resIndex = i;
            }
        }
    }
    if (resIndex < 0) {
        resIndex = 0;
    }
    obj.options.selectedIndex = resIndex;
}

var _w = 0, _h = 0;
function DisplayVideo(v) {
    if (_w != v.width || _h != v.height) {
        //设置视频显示的宽高（宽度控制为640px）
        $("#video").css('width', '640px').css('height', 640 * v.height / v.width + 'px');
        _w = v.width, _h = v.height;
    }
    //显示视频
    if (v.imgBase64Str || v.imageBase64Str) {
        document.getElementById("video").src = "data:" + v.mime + ";base64," + (v.imgBase64Str ? v.imgBase64Str : v.imageBase64Str);
    }
}

function DisplayCaptureImg(v) {
    if (v.result == 0) {
        document.getElementById("imgView").src = "data:" + v.mime + ";base64," + v.imgBase64[0];
        displayOutputInfo(1, '版面识别中...');
        document.getElementById("strResult").innerText = "版面识别中...";
        SendJson({func: 'RecogLayout', type: 0, mime: v.mime, imageBase64: v.imgBase64[0], reqId: new Date().getTime()});
    } else {
        displayOutputInfo(3, '拍照失败: ' + v.errorMsg);
    }
}

//显示版面识别结果
function DisplayLayoutResult(v) {
    var s = "";
    if (v.response) {
        s = formatJson(v.response, cbOnFormatError);
        displayOutputInfo(2, '版面识别成功');
    } else {
        s = formatJson(v, cbOnFormatError);
        displayOutputInfo(3, '版面识别失败');
    }
    var obj = document.getElementById("strResult");
    obj.innerText = s;
}

function cbOnFormatError(errInfo) {
    displayOutputInfo(3, "格式化Json时错误: " + errInfo);
}

function SetCameraInfo() {
    var cropType = 1; // 单图裁切
    var rotateAngle = parseInt($("#rotateType").val());
    displayOutputInfo(0);
    SendJson({func:"SetCameraImageInfo", to:1, devNum:_devNum, cropType:cropType, rotate:rotateAngle, reqId: new Date().getTime()});
}

function LayoutRecognize() {
    displayOutputInfo(0);
    var now = new Date().getTime();
    var cmd = {func:"CameraCapture", to:1, devNum:_devNum, mode:'base64', cropType:1, imageType:0, fillBorderType:0, removalForeign:0, reqId:now};
    SendJson(cmd);
}

// 打开(1)或关闭(0)人像摄像头,无参数时根据按钮上的文字确定
function OpenCloseCamera(val) {
    var open = false;
    if (typeof(val) == "undefined") {
        open = document.getElementById("btnOpenClose").innerText != "关闭文档摄像头";
    } else {
        open = val != 0;
    }
    var now = new Date().getTime();
    var oldDevNum = _devNum;
    if (_devNum >= 0) {
        // 打开过先关闭,关闭前先停掉视频
        SendJson({func:"GetCameraVideoBuff", to:1, devNum:_devNum, enable:"false", reqId: now++});
        SendJson({func:"CloseCamera", to:1, devNum:_devNum, reqId: now++});
        _devNum = -1;
    }
    if (open) {
        var v = $("#cameraInfo").val();
        _devNum = v == null ? 0 : parseInt(v);
        if (oldDevNum != _devNum) {
            //刷新显示 分辨率下拉列表
            displayResolution();
        }
        var resolutionNum = parseInt($("#resolution").val());
        SendJson({func:"OpenCamera", to:1, devNum:_devNum, mediaNum:0, resolutionNum:resolutionNum, reqId: now++});
    }
}

function ReOpenCamera() {
    // 重新打开多功能终端上的文档摄像头
    OpenCloseCamera(1);
}


function SetCameraImageInfo(){
    var imageType = parseInt($("#imageType").val());
    var now = new Date().getTime();
    SendJson({func:"SetCameraImageInfo", to:1, devNum:1, imageType:imageType, reqId:now});
}

function displayOutputInfo(disp, s, add) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        var h = "<b>" + s + "</b>";
        if (add) {
            var h0 = $('#outInfo').html();
            if (h0.length > 0) {
                h = h0 + "<br>" + h;
            }
        }
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html(h);
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

$(function () {
    SendJson({func:"GetCameraInfo", to:1, reqId:new Date().getTime()});
});

$(window).unload(function(){
    // 关闭页面之前,关闭摄像头
    if (_devNum >= 0) {
        OpenCloseCamera(0);
    }
});
