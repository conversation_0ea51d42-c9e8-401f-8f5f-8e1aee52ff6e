var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "GetPhysicsKeys":
            DisplayEvaluateKey(jsonObj);
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function DisplayEvaluateKey(v) {
    // 返回的json格式：{"func":"GetPhysicsKeys","reqId":"231","pressKey":0，result:0}
    // 0非常满意 |1满意 |2一般| 3不满意| 4非常不满意 |0xFF确认| 0xFE取消
    if (v.result == 0) {
        displayOutputInfo(2, "评价按键读取成功。按键值：" + v.pressKey);
        displayGrade(v.pressKey);
    } else {
        displayOutputInfo(3, "评价按键读取失败：" + v.errorMsg);
    }
}

function displayGrade(v) {
    if (v == 254) {
        displayOutputInfo(3, "客户【取消】评价输入。")
    } else {
        if (v < 0 || v > 4) {
            v = 5;
        }
        var ary = ['非常满意', '满意', '一般', '不满意', '非常不满意', ''];
        $('#evalGrade').text('客户评价: ' + ary[v]);
        var grade = 5 - v;
        var ps = $('.icon > path');
        for (var i = 0; i < 5; i++) {
            ps[i].setAttribute('fill', grade > i ? '#FF0000' : '#e6e6e6');
        }
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function ReadEvaluateKey() {
    // 提示信息显示
    displayOutputInfo(1, "客户评价中...... 请客户在多功能终端上按键进行服务评价，最后按“确认”按钮");
    // 清空星级显示
    displayGrade(-1);
     // 启动签名
    SendJson({func:"GetPhysicsKeys", to:1, reqId:new Date().getTime()});
}
