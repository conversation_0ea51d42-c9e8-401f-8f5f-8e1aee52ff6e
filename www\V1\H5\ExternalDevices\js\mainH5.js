

function load(){

	StartWebSocket();
}

function unload(){
	
	if(m_closed){
		return;
	}
	//反初始化
	var jsonObj = {FuncName:'camUnInitCameraLib'};
	sendWsMessage(jsonObj);

}


//初始化
function sendInitMsg(){
	
	var jsonObj = {FuncName:'camInitCameraLib'};
	sendWsMessage(jsonObj);
}


function showpicture(path)
{
	var jsonObj = {FuncName:'camShowImage',argument:{FilePath:path}};
	sendWsMessage(jsonObj);
}

function GetIDCardImage(typeNum,strFileFront,strFileBack)
{
	
	var jsonObj = {FuncName:'idcardrfidGetIDCardImage',argument:{pFileFront:strFileFront,pFileBack:strFileBack,nType:typeNum}};
	sendWsMessage(jsonObj);
}

function GetIDCardOneFingerData(filePath)
{
	var jsonObj = {FuncName:'idcardrfidGetOneFingerprintMsg',argument:{nIndex:0,filePath:filePath}};
	sendWsMessage(jsonObj);
}

function showIDcardFingerInfo(bs64,nType)
{
	/*获取指纹指拇类型*/

	if((nType >= 11 && nType <= 20) || (nType >= 97 && nType <= 99))
	{
		alert("指姆类型："+nType+" 11 右手拇指，12 右手食指，13 右手中指 ，14 右手环指 ，15 右手小指，16 左手拇指 ，17 左手食指 ，18 左手中指 ，19 左手环指 ，20 左手小指 ，97 右手不确定指位 ，98 左手不确定指位，99 其他不确定指位");
	}
	m_IDcardFPBase64 = bs64;
	IDCardFP.value = m_IDcardFPBase64;
	
}



//读取二代证
function ReadIDCardPath(filepath)
{

	var jsonObj = {FuncName:'idcardrfidReadIDCard',argument:{HeadPath:filepath}};
	sendWsMessage(jsonObj);
}
function ReadIDCardEx()
{
	var jsonObj = {FuncName:'idcardrfidReadIDCardEx',argument:{}};
	sendWsMessage(jsonObj);
}
function showIDCardImageFront(){
	
	m_isShowFileFront = true;
	m_isShowFileBack = false;
	var jsonObj = {FuncName:'FileEncodeBase64',argument:{filePath:m_strFileFront}};
	sendWsMessage(jsonObj);
	
	
	CombinePic.style.display="inline";
	CombinePic1.style.display="none";
}

function showIDCardImageBack()
{
	var nType = document.getElementById("CombineType").value;	
	if(nType== 0)
	{
		m_isShowFileBack = true;
		CombinePic1.style.display="inline";
		var jsonObj = {FuncName:'FileEncodeBase64',argument:{filePath:m_strFileBack}};
		sendWsMessage(jsonObj);
	}	
}

function showFingerPic(bs,picBs)
{
	
	if(m_fingerIndex == 1)
	{
		m_finger1Base64 = picBs;
		fingerprint1.src = "data:image/png;base64,"+bs;
		
	}else{
		m_finger2Base64 = picBs;
		fingerprint2.src = "data:image/png;base64,"+bs;
	}
	
}


//读取银行卡
function ReadBankCard(){
	
	var jsonObj = {FuncName:'ReadBankCard'};
	sendWsMessage(jsonObj);
	
}

//读取磁条卡
function ReadMagneticCard(){
	
	var jsonObj = {FuncName:'ReadMagneticCard'};
	sendWsMessage(jsonObj);
}

//读取社保卡
function ReadSBKCard()
{
	var jsonObj = {FuncName:'ReadSBKCaard'};
	sendWsMessage(jsonObj);	
}

//初始化指纹仪
function InitFingerData(){
	
	var jsonObj = {FuncName:'fingerprintInit',argument:{}};
	sendWsMessage(jsonObj);
}

//反初始化指纹仪
function UinitFingerData(){
	
	var jsonObj = {FuncName:'fingerprintUnInit',argument:{}};
	sendWsMessage(jsonObj);
	
}

//采集指纹
function GetFingerPicFromeMsg(bmpFilePath,FeatureFilePath){
	
	var jsonObj = {FuncName:'GetFingerprint',argument:{bmpPath:bmpFilePath,FeaturePath:FeatureFilePath}};
	sendWsMessage(jsonObj);
	
}

//对比指纹
function ComperaFingerPic(file1,file2){
	
	var jsonObj = {FuncName:'ContrastFingerprint',argument:{FeaturePath1:file1,FeaturePath2:file2}};
	sendWsMessage(jsonObj);
	
}

function ContrastFingerprintBase64(bs1,bs2)
{
	
	var jsonObj = {FuncName:'ContrastFingerprintBase64',argument:{Base64FPFeature1:bs1,Base64FPFeature2:bs2}};
	sendWsMessage(jsonObj);
}


//图片base64
function CaptureBase64()
{
	var filepath = document.getElementById("saveText").value;
	var jsonObj = {FuncName:'FileEncodeBase64',argument:{filePath:filepath}};
	sendWsMessage(jsonObj);
}



function showBase64info(str)
{
	
	alert("Base64数据为："+ str);
	
}

























