import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";


// 查询资讯分类列表
export function listClass(query) {
    return request({
      url: '/cmsclass/doQuery',
      method: 'post',
      data: query
    })
  }

// 查询资讯列表
export function listCms(query) {
  return request({
    url: '/cms/doQuery',
    method: 'post',
    data: query
  })
}


// 查询分类详细
export function getClass(cmsclassId) {
    return request({
      url: '/cmsclass/' + parseStrEmpty(cmsclassId),
      method: 'get'
    })
}

// 查询分类详细
export function getCms(cmsId) {
  return request({
    url: '/cms/' + parseStrEmpty(cmsId),
    method: 'get'
  })
}



// 新增分类
export function addClass(data) {
    return request({
      url: '/cmsclass/doAdd',
      method: 'post',
      data: data
    })
}

// 新增CMS
export function addCms(data) {
  return request({
    url: '/cms/doAdd',
    method: 'post',
    data: data
  })
}

// 修改分类
export function updateClass(data) {
    return request({
      url: '/cmsclass/doEdit',
      method: 'post',
      data: data
    })
}


// 修改分类
export function updateCms(data) {
  return request({
    url: '/cms/doEdit',
    method: 'post',
    data: data
  })
}

// 删除分类
export function delClass(cmsclassId) {
    return request({
      url: '/cmsclass/doDelete/' + cmsclassId,
      method: 'post'
    })
}


// 删除资讯
export function delCms(cmsId) {
  return request({
    url: '/cms/doDelete/' + cmsId,
    method: 'post'
  })
}

  