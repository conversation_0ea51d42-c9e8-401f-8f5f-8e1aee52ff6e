import Cookies from 'js-cookie'

const TokenKey = 'em_token'
const User<PERSON>ey = "em_user"

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(To<PERSON><PERSON><PERSON>, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}

export function getUser(){
  return Cookies.get(UserKey)
}

export function setUser(user){
  return Cookies.set(<PERSON><PERSON><PERSON><PERSON>, user)
}

export function removeUser(){
  return Cookies.remove(User<PERSON><PERSON>)
}
