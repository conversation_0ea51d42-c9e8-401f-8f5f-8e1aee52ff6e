import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询职业危害检测列表
export function listCheck(query) {
    return request({
      url: '/hazardcheck/doQuery',
      method: 'post',
      data: query
    })
}

// 查询职业危害检测详细
export function getCheck(hazardcheckId) {
    return request({
      url: '/hazardcheck/' + parseStrEmpty(hazardcheckId),
      method: 'get'
    })
}
  

// 新增职业危害检测
export function addCheck(data) {
    return request({
      url: '/hazardcheck/doAdd',
      method: 'post',
      data: data
    })
}

// 修改职业危害检测
export function updateCheck(data) {
  return request({
    url: '/hazardcheck/doEdit',
    method: 'post',
    data: data
  })
}

// 删除职业危害检测
export function delCheck(hazardcheckId) {
    return request({
      url: '/hazardcheck/doDelete/' + hazardcheckId,
      method: 'post'
    })
}
  