<template style="background-color:white;">

  <div class="app-container" >

    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="测项名称" prop="itemName">
        <el-input  v-model="queryParams.itemName" placeholder="请输入测项名称" clearable/>
      </el-form-item>
      <el-form-item label="状态" prop="state">
       <el-select v-model="queryParams.state" placeholder="请选择状态" clearable>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item label="检测类型" prop="inspectType">
       <el-select v-model="queryParams.inspectType" placeholder="请选择检测类型" clearable>
        <el-option
          v-for="item in inspectTypes"
          :key="item.value"
          :label="item.label"
          :value="item.value">
        </el-option>
      </el-select>
      </el-form-item>
      <el-form-item label="测项内容" prop="itemContent">
        <el-input  v-model="queryParams.itemContent" placeholder="请输入测项内容" clearable/>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>
    
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
        <el-button
          type="primary"
          plain
          icon="el-icon-copy-document"
          size="mini"
          @click="handleCopy"
        >复制</el-button>
      </el-col>
      <right-toolbar  @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="list" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
    
      <el-table-column label="测项名称" align="center" prop="itemName" :show-overflow-tooltip="true" >
      </el-table-column>
      <el-table-column label="检测类型" align="center"   >
        <template slot-scope="scope">
          <span v-if="scope.row.inspectType==1">观察</span>
          <span v-if="scope.row.inspectType==2">抄表</span>
        </template>
      </el-table-column>
      <el-table-column label="点编码" align="center" prop="pointCode"   :show-overflow-tooltip="true" />
      <el-table-column label="测项内容" align="center" prop="itemContent"   :show-overflow-tooltip="true" />
      <el-table-column label="设备" align="center" prop="inspectdevice.deviceName"   :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">启用</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>
      <el-table-column label="标准值" align="center" prop="standardValue"   :show-overflow-tooltip="true" />
       <el-table-column label="单位" align="center"   :show-overflow-tooltip="true" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.system_unit" :value="scope.row.unit"/>
        </template>
       </el-table-column>
      <el-table-column label="上限值" align="center" prop="upperValue"   :show-overflow-tooltip="true" />
      <el-table-column label="下限值" align="center" prop="lowerValue"   :show-overflow-tooltip="true" >
        
      </el-table-column>
      <el-table-column label="上上限值" align="center" prop="maxupperValue"   :show-overflow-tooltip="true" />
      <el-table-column label="下下限值" align="center" prop="maxupperValue"   :show-overflow-tooltip="true" />
      <el-table-column label="创建时间" align="center" prop="maxlowerValue" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDel(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参数配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        
        <el-form-item label="测项名称" prop="itemName">
          <el-input  v-model="form.itemName" placeholder="请输入测项名称" maxlength="50"/>
        </el-form-item>
        <el-form-item label="测项类型" prop="inspectType">
          <el-select v-model="selectvalue" placeholder="请选择测项类型" @change="seltype">
            <el-option v-for="item in inspectTypes" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="测项内容" prop="itemContent">
          <el-input type="textarea" v-model="form.itemContent"  maxlength="100" />
        </el-form-item>
        <el-form-item label="状态" prop="state">
              <el-radio-group v-model="form.state" >
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
        </el-form-item>
        <el-form-item label="设备" prop="inspectDeviceId">
          <el-select v-model="inspectDeviceId" placeholder="请选择设备" filterable clearable @change="seldevice">
            <el-option
              v-for="item in devices"
              :key="item.deviceId"
              :label="item.deviceName"
              :value="item.deviceId">
            </el-option>
          </el-select>
        </el-form-item> 
        <el-form-item label="点编码" prop="pointCode" v-show="isread">
          <el-input   v-model="form.pointCode" placeholder="请输入点编码" />
        </el-form-item>
        <el-form-item label="标准值" prop="standardValue" v-show="isread">
          <el-input type="number"  v-model="form.standardValue" placeholder="请输入标准值" />
        </el-form-item>
         <el-form-item label="单位" prop="unit" v-show="isread">
              <el-select v-model="form.unit" placeholder="请选择单位" clearable>
                  <el-option v-for="dict in dict.type.system_unit" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
        <el-form-item label="上限值" prop="upperValue" v-show="isread">
          <el-input type="number"  v-model="form.upperValue" placeholder="请输入上限值" />
        </el-form-item>
        <el-form-item label="下限值" prop="lowerValue" v-show="isread">
          <el-input type="number"  v-model="form.lowerValue" placeholder="请输入下限值" />
        </el-form-item>
        <el-form-item label="上上限值" prop="maxupperValue" v-show="isread">
          <el-input type="number"  v-model="form.maxupperValue" placeholder="请输入上上限值" />
        </el-form-item>
        <el-form-item label="下下限值" prop="maxlowerValue" v-show="isread">
          <el-input type="number"  v-model="form.maxlowerValue" placeholder="请输入下下限值" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  
  </div>
  
</template>

<script>

import { listitem, getitem,additem,updateitem,delitem,copyitems} from "@/api/inspect/item";
import { listDevice} from "@/api/device/device";
import { getsite} from "@/api/inspect/site";

export default {
  name: "Item",
  dicts: ['system_unit'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      //检测区域id
      siteId:undefined,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //检测项列表数据
      list:[],
      //设备列表
      devices:[],
      inspectDeviceId:undefined,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示抄表的参数
      isread:false,
      //是否显示弹出显示层
      viewopen:false,
      orgid:undefined,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      // 表单参数
      form: {
        inspectType:1
      },
      selectvalue:1,
      // 表单校验
      rules: {
        itemName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        state: [
          { required: true, message: "状态不能为空", trigger: 'change' }
        ],
        standardValue: [
          { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
        ],
        upperValue: [
          { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
        ],
        lowerValue: [
          { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
        ],
        maxupperValue: [
          { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
        ],
        maxlowerValue: [
          { pattern: /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/, message: '请输⼊正确的格式,可保留两位⼩数' }
        ],
      },
      //测项类型
      inspectTypes:[
        {
          value: 1,
          label: '观察'
        }, {
          value: 2,
          label: '抄表'
      }
      ],
         //状态
      options: [{
          value: 1,
          label: '启用'
        }, {
          value: 2,
          label: '停用'
      }],
    };
  },
  created() {
    const siteId = this.$route.params && this.$route.params.siteId;
    this.siteId=siteId
    console.log(siteId)
    this.getList();
    this.getsite();
  },
  methods: {



    //区域列表
    getList(){
      this.loading = true;
      this.queryParams.orderBy="creationtime desc"
      this.queryParams.siteId=this.siteId
      listitem(this.queryParams).then(response => {
          this.list = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },

    getsite(){

      getsite(this.siteId).then(response => {
        
        this.orgid=response.data.orgId
        

      });
    },
    seldevice(value){
      this.form.inspectDeviceId=value
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        inspectitemId: undefined,
        siteId: undefined,
        itemName: undefined,
        itemContent: undefined,
        nameEn:undefined,
        nameTh:undefined,
        state:1,
        inspectType:undefined,
        pointCode:undefined,
        standardValue:undefined,
        upperValue:undefined,
        lowerValue:undefined,
        maxupperValue:undefined,
        maxlowerValue:undefined,
        unit:undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if(this.queryParams.itemName == ''){
        this.queryParams.itemName=undefined
      }
      if(this.queryParams.state == ''){
        this.queryParams.state=undefined
      }
      if(this.queryParams.inspectType == ''){
        this.queryParams.inspectType=undefined
      }
      if(this.queryParams.itemContent == ''){
        this.queryParams.itemContent=undefined
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.inspectDeviceId = undefined
      this.form.inspectType=1
      this.form.siteId = this.siteId
      this.open = true;
      this.title = "新增检测项";
      this.devices=[]
      let paramorg = {"deviceOrgId":this.orgid}
      listDevice(paramorg).then(response=>{
        console.log(JSON.stringify(response))
        if(response.code==200){
          this.devices=response.data;
        }
      })
    },
    //检查类型选择
    seltype(data){
       this.form.inspectType=data
       
       if(data===1){
        this.isread=false
       }else{
        this.isread=true
       }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.inspectitemId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    handleCopy(){
      let items = this.ids
      if(items.length>0){
        this.$modal.confirm('是否确认复制检测项信息？').then(function() {
            
          
          console.log(items)
          return copyitems(items.join(","));
        }).then(() => {
          this.getList();
          this.$modal.msgSuccess("复制成功");
        }).catch(() => {});
      }else{
         this.$modal.msgError("还未选择检测项");
      }
      

    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();

      const inspectitemId = row.inspectitemId || this.ids
      getitem(inspectitemId).then(response => {
        this.form = response.data;
        this.selectvalue=this.form.inspectType
        this.inspectDeviceId = undefined
        if(this.form.inspectDeviceId != undefined){
          this.inspectDeviceId=this.form.inspectDeviceId+''
        }
        if(this.form.inspectType===1){
        this.isread=false
       }else{
        this.isread=true
       }
        this.open = true;
        this.title = "修改检测项";
      });

       this.devices=[]
      let paramorg = {"deviceOrgId":this.orgid}
      listDevice(paramorg).then(response=>{
        console.log(JSON.stringify(response))
        if(response.code==200){
          this.devices=response.data;
        }
      })

    },
    /**删除按钮操作 */
    handleDel(row){
      const inspectitemId = row.inspectitemId || this.ids
      this.$modal.confirm('是否确认删除检测项信息？').then(function() {
        return delitem(inspectitemId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },

 
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if(this.form.standardValue == ""){
            this.form.standardValue=undefined
          }
          if(this.form.unit == ""){
            this.form.unit=undefined
          }
          if(this.form.inspectDeviceId == ""){
            this.form.inspectDeviceId=undefined
          }
          if(this.form.upperValue == ""){
            this.form.upperValue=undefined
          }
          if(this.form.lowerValue == ""){
            this.form.lowerValue=undefined
          }
          if(this.form.maxupperValue == ""){
            this.form.maxupperValue=undefined
          }
          if(this.form.maxlowerValue == ""){
            this.form.maxlowerValue=undefined
          }
          if (this.form.inspectitemId != undefined) {
            updateitem(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            additem(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },



  }
};
</script>