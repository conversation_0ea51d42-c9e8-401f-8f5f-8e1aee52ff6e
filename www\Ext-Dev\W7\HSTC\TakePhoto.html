<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>拍照与水印演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="DeviceConfig.html">设备配置信息</a></li>
                <li><a href="Slideshow.html">轮播图片设置</a></li>
                <li><a href="SignAuthorization.html">签署诚信授权</a></li>
                <li><a href="CertifiedSalvageQualification.html">救助资格认证</a></li>
<!--                <li class="active"><a href="#">文档拍照</a></li>-->
            </ul>
        </div>
    </div>
</nav>

<div style="float: left;">
    <img id="product" src="image/W7_2.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>拍照与水印 演示</h1>
        <p>这个页面会自动打开安卓多功能终端上的文档摄像头，并在页面上显示文档摄像头的视频；点击【拍照】按钮，会启动安卓多功能终端上的文档拍照功能，并在页面上显示拍照的图片。拍照也支持打水印功能。</p>
    </div>

    <div class="page-header"></div>

    <div class="col-md-8" style="text-align: center; width:66%;">
        <p>
            <label>设备名称:</label>
            <select id="cameraInfo" style="width:140px;" onchange="switchCamera()"></select>
            <label style="margin-left:15px">视频格式:</label>
            <select id="mediaType" style="width:70px;" onchange="ReOpenCamera()"></select>
            <label style="margin-left:15px">分辨率:</label>
            <select id="resolution" style="width:95px;" onchange="ReOpenCamera()"></select>
            <button id="btnOpenClose" type="button" class="btn btn-primary" onclick="OpenCloseCamera()" style="margin-left:15px;margin-bottom:5px">关闭摄像头</button>
            <br>
            <label style="margin-left:15px">旋转:</label>
            <select id="rotateType" style="width:95px;" onchange="SetCameraInfo()">
                <option value="0">不旋转</option>
                <option value="90">旋转90度</option>
                <option value="180">旋转180度</option>
                <option value="270">旋转270度</option>
            </select>
            <label style="margin-left:15px">裁切:</label>
            <select id="cropType" style="width:85px;" onchange="SetCameraInfo()">
                <option value="0">不裁切</option>0-
                <option value="1">单图裁切</option>
                <option value="2">多图裁切</option>
            </select>
            <label style="margin-left:15px">图像效果:</label>
            <select id="imageType" onchange="SetCameraImageInfo()" style="width:82px;">
                <option value="0">彩色原色</option>
                <option value="1">灰度图片</option>
                <option value="2">黑白文档</option>
                <option value="3">彩色文档</option>
                <option value="4">红印文档</option>
                <option value="5">蓝印文档</option>
                <option value="6">彩色照片</option>
                <option value="7">票据增强</option>
            </select>
            <label style="margin-left:15px">文件格式:</label>
            <select id="imgFileType" onchange="SetCameraImageInfo()" style="width:60px;">
                <option value="0">JPG</option>
                <option value="1">PNG</option>
                <option value="2">TIF</option>
            </select>
            <button id="btnCapture" type="button" class="btn btn-primary" onclick="CaptureDoc()" style="margin-left:15px">拍照</button>
            <br>
            <label>亮度:
                <input id="9963776" class="cameraPU" type="range" min="1" max="100" onchange="chgPUVal(this)">
            </label>
            <label style="margin-left:20px">对比度:
                <input id="9963777" class="cameraPU" type="range" min="1" max="100" onchange="chgPUVal(this)">
            </label>
            <label style="margin-left:20px">饱和度:
                <input id="9963778" class="cameraPU" type="range" min="1" max="100" onchange="chgPUVal(this)">
            </label>
        </p>
        <img id="video" src="" style="width:640px;height:480px;">
        <p>文档摄像头视频</p>
    </div>

    <div class="col-md-4" style="text-align:left; padding-left:0; padding-right:0;">
        <label>自动拍照:</label>
        <select id="autoCapture" style="width:140px;" onchange="OnChgAutoCapture()">
            <option value="-1">不开启</option>
            <option value="0">自动检测图像变化</option>
            <option value="1">定时拍摄</option>
        </select>
        <label style="margin-left:20px;">参数或秒数:
            <input type="number" min="2" max="50" value="5" id="autoParam" style="width:45px;">
        </label>
        <br><br>
        <label class="checkbox-inline" title="在OCR识别时不要使用水印功能，否则会影响识别效果">
            <input type="checkbox" id="watermark" value="0" onchange="OnChgWatermark()"><b>拍照打水印</b>
        </label>
        <label style="margin-left:20px;">颜色:
            <input type="color" value="#7A7A7A" id="wmColor">
        </label>
        <label style="margin-left:20px;">位置:</label>
        <select id="wmLayout" style="width:90px;">
            <option value="0">左上角</option>
            <option value="1">中间</option>
            <option value="2">斜对角线</option>
            <option value="3">右下角</option>
        </select>
        <br>
        <label>字号:
            <input type="number" min="20" max="200" value="60" id="wmSize" style="width:45px;">
        </label>
        <label style="margin-left:20px;">水印文本:
            <input type="text" style="width:182px;" value="机密文件" id="wmText">
        </label>
        <br><br>
        <label >OCR语言:</label>
        <select id="language" style="width:192px;margin-right:16px"></select>
        <label class="checkbox-inline" title="OCR识别出的文件后，推送到多功能终端上显示">
            <input type="checkbox" id="uploadDevice" checked><b>终端回显</b>
        </label>
        <br>
        <label >输出文件类型:</label>
        <select id="fileType" style="width:100px;margin-right:56px"></select>
        <label class="checkbox-inline" title="OCR识别时是否检测文字方向,检测文字方向会慢一些,如果图像文件的文字能保证是正向的,可以不检测文字方向">
            <input type="checkbox" id="detectTextOrie" checked><b>检测文字方向</b>
        </label>
        <br><br>

        <button type="button" class="btn btn-primary" id="btnMergeJPG" onclick="mergeJPG()" style="margin-right:6px">合并JPG</button>
        <button type="button" class="btn btn-primary" id="btnMergePDF" onclick="mergePDF()" style="margin-right:6px">合并PDF</button>
        <button type="button" class="btn btn-primary" id="btnOCR" onclick="ocrFile()" style="margin-right:6px">OCR识别</button>
        <button type="button" class="btn btn-primary" id="btnClearFile" onclick="clearImageFile()">清空文件</button>
        <div class="col-sm-12" style="padding: 0;margin: 10px 0;overflow-y: scroll;height:360px;">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th width="60%" style="text-align: center;">文件名</th>
                    <th style="text-align: center;">操作</th>
                </tr>
                </thead>
                <tbody id="tabBody">
                </tbody>
            </table>
        </div>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<audio id="audioCapture">
    <source src="../../../audio/Capture.wav" type="audio/wav">
</audio>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">拍照文件查看</h4>
            </div>
            <div class="modal-body">
                <img id="imgView" src="" style="max-width:860px">
<!--                <img id="imgView2" src="" style="max-width:860px">-->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>


<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/DocOCR.js"></script>
</body>
</html>