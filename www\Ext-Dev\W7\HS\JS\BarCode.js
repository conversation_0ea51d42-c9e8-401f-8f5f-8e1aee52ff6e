var _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
var _ws;
var _connected = false;
var _devNum = -1;

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "GetCameraInfo":
            DisplayDevInfo(jsonObj);
            break;
        case "OpenCamera":
            if (jsonObj.result == 0 || jsonObj.result != 9 || jsonObj.result != -2) {
                // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常， -2:camera is open,也忽略这个异常
                var reqId = new Date().getTime();
                // 打开多功能终端上的文档摄像头成功后，获取文档摄像头的视频
                SendJson({func:"GetCameraVideoBuff", to:1, devNum:_devNum, enable:"true", reqId:reqId-1});
                SetCameraInfo();
            } else {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
        case "GetCameraVideoBuff":
            DisplayVideo(jsonObj);
            break;
        case "CameraCapture":
            DisplayCaptureImg(jsonObj);
            break;
        case "ScanBarCode":
            DisplayScanBarCodeInfo(jsonObj);
            break;
        case "RecogBarCode":
            DisplayRecogBarCodeInfo(jsonObj);
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function DisplayScanBarCodeInfo(v) {
    // 返回的json格式：{"func":"ScanBarCode","reqId":"2132131","result":0，“codeNum”:"条码信息"}
    if (v.result == 0) {
        document.getElementById("strResult").innerText = "条码扫描成功. 条码信息: \r\n" + v.codeNum;
        displayOutputInfo(2, "条码扫描成功.");
    } else {
        displayOutputInfo(3, "条码扫描失败.<br>" + v.errorMsg);
    }
}

function DisplayRecogBarCodeInfo(v) {
    if (v.result == 0) {
        var s;
        if (v.codeInfos) {
            var a = v.codeInfos;
            s = "识别到" + a.length + "个条码:\r\n";
            for (var i = 0; i < a.length; i++) {
                s += "内容: " + a[i].codeNum + "\r\n";
                s += "类型: " + a[i].codeType + "\r\n";
                s += "--------\r\n"
            }
        } else {
            s = "没有识别到条码.";
        }
        document.getElementById("strResult").innerText = s;
        displayOutputInfo(2, "条码识别成功.");
    } else {
        displayOutputInfo(3, "条码识别失败.<br>" + v.errorMsg);
    }
}

function DisplayCaptureImg(v) {
    if (v.result == 0) {
        //document.getElementById("imgView").src = "data:" + v.mime + ";base64," + v.imgBase64[0];
        displayOutputInfo(1, '条码识别中...');
        SendJson({func: 'RecogBarCode', imageBase64: v.imgBase64[0], reqId: new Date().getTime()});
    } else {
        displayOutputInfo(3, '拍照失败: ' + v.errorMsg);
    }
}

var _w = 0, _h = 0;
function DisplayVideo(v) {
    if (_w != v.width || _h != v.height) {
        //设置视频显示的宽高（宽度控制为640px）
        $("#video").css('width', '640px').css('height', 640 * v.height / v.width + 'px');
        _w = v.width, _h = v.height;
    }
    //显示视频
    if (v.imgBase64Str || v.imageBase64Str) {
        document.getElementById("video").src = "data:" + v.mime + ";base64," + (v.imgBase64Str ? v.imgBase64Str : v.imageBase64Str);
    }
}

function DisplayDevInfo(v) {
    if (v.result == 0) {
        _devInfos = v.devInfo;
        if (_devInfos.length == 0) {
            displayOutputInfo(3, "获取设备信息时，返回的devInfos为空.");
            return;
        }

        displayCamera();
        displayResolution();
        OpenCloseCamera(1);
    } else {
        displayOutputInfo(3, v.func + "<br>" + v.errorMsg);
    }
}

// 刷新显示 设备名称 下拉列表
function displayCamera() {
    var obj = document.getElementById("cameraInfo");
    var lastSelectedIndex = obj.options.selectedIndex;
    obj.options.length = 0;
    for (var i = 0; i < _devInfos.length; i++) {
        obj.options.add(new Option(_devInfos[i].devName, _devInfos[i].id));
        if (lastSelectedIndex == -1 && _devInfos[i].camMode == 0) {
            // 默认打开文档摄像头
            lastSelectedIndex = i;
        }
    }
    if (lastSelectedIndex >= 0 && lastSelectedIndex < _devInfos.length) {
        obj.options.selectedIndex = lastSelectedIndex;
    }
}

// 刷新显示 分辨率下拉列表
function displayResolution() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var index2 = 0;
    var obj = document.getElementById("resolution");
    var resolutions = _devInfos[index1].mediaTypes[index2].resolutions;
    //清空resolution:select数据
    obj.options.length = 0;
    var minWidth = _devInfos[index1].camMode == 0 ? 2048 : 1280;
    var maxWidth = _devInfos[index1].camMode == 0 ? 3200 : 1920;
    var resIndex = -1;
    for (var i = 0; i < resolutions.length; i++) {
        obj.options.add(new Option(resolutions[i], i));
        if (resIndex < 0) {
            var ary = resolutions[i].split('x');
            if (ary[0] >= minWidth && ary[0] < maxWidth ) {
                // 默认使用2048以上的第一个分辨率
                resIndex = i;
            }
        }
    }
    if (resIndex < 0) {
        resIndex = 0;
    }
    obj.options.selectedIndex = resIndex;
}


function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

// 打开(1)或关闭(0)人像摄像头,无参数时根据按钮上的文字确定
function OpenCloseCamera(val) {
    var open = false;
    if (typeof(val) == "undefined") {
        open = document.getElementById("btnOpenClose").innerText != "关闭文档摄像头";
    } else {
        open = val != 0;
    }
    var now = new Date().getTime();
    var oldDevNum = _devNum;
    if (_devNum >= 0) {
        // 打开过先关闭,关闭前先停掉视频
        SendJson({func:"GetCameraVideoBuff", to:1, devNum:_devNum, enable:"false", reqId: now++});
        SendJson({func:"CloseCamera", to:1, devNum:_devNum, reqId: now++});
        _devNum = -1;
    }
    if (open) {
        var v = $("#cameraInfo").val();
        _devNum = v == null ? 0 : parseInt(v);
        if (oldDevNum != _devNum) {
            //刷新显示 分辨率下拉列表
            displayResolution();
        }
        var resolutionNum = parseInt($("#resolution").val());
        SendJson({func:"OpenCamera", to:1, devNum:_devNum, mediaNum:0, resolutionNum:resolutionNum, reqId: now++});
    }
}

function ReOpenCamera() {
    // 重新打开多功能终端上的文档摄像头
    OpenCloseCamera(1);
}

function SetCameraInfo() {
    var cropType = 1; // 单图裁切
    var rotateAngle = parseInt($("#rotateType").val());
    displayOutputInfo(0);
    SendJson({func:"SetCameraImageInfo", to:1, devNum:_devNum, cropType:cropType, rotate:rotateAngle, reqId: new Date().getTime()});
}

function BarCodeScan() {
    SendJson({func:"ScanBarCode",to:1,reqId: new Date().getTime()});
}

function BarCodeRecognize() {
    var now = new Date().getTime();
    var cropType = 1; // 单图裁切
    var cmd = {func:"CameraCapture", to:1, devNum:_devNum, mode:'base64', cropType:cropType, imageType:0, fillBorderType:0, removalForeign:0, reqId:now};
    SendJson(cmd);
}

function switchAuto() {
    var cmd = {func:"CameraAutoCapture", to:1, devNum:_devNum, reqId: new Date().getTime()};
    if ($("#chkAuto").prop('checked')) {
        cmd.enable = true;
        cmd.detectionType = 0;
        cmd.param = 5;
    } else {
        cmd.enable = false;
    }
    SendJson(cmd);
}

$(function () {
    SendJson({func:"GetCameraInfo", to:1, reqId:new Date().getTime()});
});

$(window).unload(function(){
    // 关闭页面之前,关闭摄像头
    if (_devNum >= 0) {
        OpenCloseCamera(0);
    }
});
