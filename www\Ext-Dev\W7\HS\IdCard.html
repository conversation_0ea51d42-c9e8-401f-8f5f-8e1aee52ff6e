<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>身份证读取演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="FileDisplay.html">文件播放</a></li>
                <li class="active"><a href="#">身份证读取</a></li>
                <li><a href="ReadFinger.html">指纹读取</a></li>
                <li><a href="IdCardMatch.html">人证比对</a></li>
                <li><a href="DocOCR.html">OCR识别与水印</a></li>
                <li><a href="ReadSign.html">手写签名</a></li>
                <li><a href="ReadKeyboard.html">评价按键读取</a></li>
                <li><a href="Barcode.html">条码扫描与识别</a></li>
                <li><a href="LayoutRecognition.html">版面识别</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/W7-0911.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>身份证读取 演示</h1>
        <p>这个页面演示了如何从安卓多功能终端上读取身份证信息，然后在页面上显示。</p>
    </div>

    <div class="page-header">
    </div>

    <div class="col-md-9">
        <table class="table table-bordered">
            <tr><td width="120" align="right">姓名</td><td id="name"></td></tr>
            <tr><td align="right">性别</td><td id="gender"></td></tr>
            <tr><td align="right">民族</td><td id="folk"></td></tr>
            <tr><td align="right">出生年月</td><td id="birth"></td></tr>
            <tr><td align="right">住址</td><td id="address"></td></tr>
            <tr><td align="right">身份证号码</td><td id="ID"></td></tr>
            <tr><td align="right">发证机关</td><td id="issue"></td></tr>
            <tr><td align="right">有效期</td><td id="startEndDate"></td></tr>
        </table>
    </div>
    <div class="col-md-2" style="text-align: center">
        <img id="pic" src="" style="width:160px; height: 200px;">
        <p>身份证照片</p>
        <p><button type="button" class="btn btn-primary" onclick="ReadIdCard()">读取身份证</button></p>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/IdCard.js"></script>
</body>
</html>