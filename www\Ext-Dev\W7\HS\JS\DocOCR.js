var _url, _ws;
var _connected = false;
var _devInfos = null;
var _devNum = -1;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}


/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "GetCameraInfo":
            DisplayDevInfo(jsonObj);
            break;
        case "OpenCamera":
            if (jsonObj.result == 0 || jsonObj.result != 9 || jsonObj.result != -2) {
                // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常， -2:camera is open,也忽略这个异常
                _devNum = 0;
                document.getElementById("btnOpenClose").innerText = "关闭文档摄像头";
                var reqId = new Date().getTime();
                // 打开多功能终端上的文档摄像头成功后，获取文档摄像头的视频
                SendJson({func:"GetCameraVideoBuff", to:1, devNum:_devNum, enable:"true", reqId:reqId++});
                // 获取摄像头的PU值
                SendJson({func:"GetCameraPuValue", to:1, devNum:_devNum, reqId:reqId});
                SetCameraInfo();
            } else {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
        case "CloseCamera":
            document.getElementById("btnOpenClose").innerText = "打开文档摄像头";
            $(".cameraPU").attr("disabled","disabled");
            break;
        case "GetCameraVideoBuff":
            // 获取到人像摄像头的视频,显示
            DisplayVideo(jsonObj);
            break;
        case "GetCameraPuValue":
            DisplayPuValue(jsonObj);
            break;
        case "CameraCapture":
            // 显示拍照照片
            DisplayCaptureImg(jsonObj);
            break;
        case "FileToBase64":
            DisplayFileView(jsonObj);
            break;
        case "MergeFile":
            DisplayMergeFile(jsonObj);
            break;
        case "FileOCR":
            DisplayOcrFile(jsonObj);
            break;
        case "UploadBase64File":
            DisplayUploadFile(jsonObj);
            break;
        case "GetOcrSupportInfo":
            displayOcrSupportInfo(jsonObj);
            // 获取多功能终端上的文档摄像头的设备信息(主要是分辨率)
            SendJson({func:"GetCameraInfo", to:1, reqId:new Date().getTime()});
            break;
        case "DisplayLocalFile":
            if (jsonObj.result == 0) {
                displayOutputInfo(2, "文件展示成功", 1);
            } else {
                displayOutputInfo(3, "文件展示失败<br>" + jsonObj.errorMsg, 1);
            }
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function Between(v, maxV, minV) {
    return v <= maxV && v >= minV;
}

function DisplayDevInfo(v) {
    if (v.result == 0) {
        _devInfos = v.devInfo;
        if (_devInfos.length == 0) {
            displayOutputInfo(3, "获取设备信息时，返回的devInfos为空.");
            return;
        }

        displayCamera();
        displayMediaType();
        displayResolution();
        switchCamera();
    } else {
        displayOutputInfo(3, v.func + "<br>" + v.errorMsg);
    }
}

// 刷新显示 设备名称 下拉列表
function displayCamera() {
    var obj = document.getElementById("cameraInfo");
    var lastSelectedIndex = obj.options.selectedIndex;
    obj.options.length = 0;
    for (var i = 0; i < _devInfos.length; i++) {
        obj.options.add(new Option(_devInfos[i].devName, _devInfos[i].id));
    }
    if (lastSelectedIndex >= 0 && lastSelectedIndex < _devInfos.length) {
        obj.options.selectedIndex = lastSelectedIndex;
    }
}

// 刷新显示 视频格式 下拉列表
function displayMediaType() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var mediaTypes = _devInfos[index1].mediaTypes;
    var obj = document.getElementById("mediaType");
    obj.options.length = 0;
    for (var i = 0; i < mediaTypes.length; ++i) {
        obj.options.add(new Option(mediaTypes[i].mediaType, i));
    }
}

// 刷新显示 分辨率下拉列表
function displayResolution() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var index2 = document.getElementById("mediaType").options.selectedIndex;
    var obj = document.getElementById("resolution");
    var resolutions = _devInfos[index1].mediaTypes[index2].resolutions;
    //清空resolution:select数据
    obj.options.length = 0;
    for (var i = 0; i < resolutions.length; i++) {
        obj.options.add(new Option(resolutions[i], i));
    }
}


function switchCamera() {
    var reqId = new Date().getTime();
    if (_devNum >= 0) {
        //关闭之前的摄像头
        SendTxt('{"func":"CloseCamera","reqId":"' + reqId + '","devNum":' + _devNum + '}');
    }

    //刷新显示 视频格式 下拉列表
    displayMediaType();

    //刷新显示 分辨率下拉列表
    displayResolution();

    //开启摄像头
    _devNum = $("#cameraInfo").val() == null ? 0 : parseInt($("#cameraInfo").val());
    reqId++;

    var mediaType = parseInt($("#mediaType").val());
    var resolutionNum = parseInt($("#resolution").val());
    SendJson({func:"OpenCamera", to:1, devNum:_devNum, mediaNum:mediaType, resolutionNum:resolutionNum, reqId: reqId});
}

var _w = 0, _h = 0;
function DisplayVideo(v) {
    if (_w != v.width || _h != v.height) {
        //设置视频显示的宽高（宽度控制为640px）
        $("#video").css('width', '640px').css('height', 640 * v.height / v.width + 'px');
        _w = v.width, _h = v.height;
    }
    //显示视频
    document.getElementById("video").src = "data:" + v.mime + ";base64," + v.imageBase64Str;
}

function DisplayCaptureImg(v) {
    if (v.result == 0) {
        audioCapture.play();
        for (var i = 0; i < v.imagePath.length; i++) {
            displayFile(v.imagePath[i]);
            // DisplayImg(v.mime, v.imgBase64[i]);
        }
    } else {
        displayOutputInfo(3, '拍照失败: ' + v.errorMsg);
    }
}

var _path = null;
var _th = '<tr><th>文件名</th><th>操作</th></tr>';
var _tdOpration = '<td><a onclick="vf()">查看</a> <a onclick="df()">删除</a></td>';
function displayFile(file) {
    var pos = file.lastIndexOf('\\');
    if (_path == null) {
        _path = file.substring(0, pos);
    }
    var html = '<tr><td>' + file.substring(pos + 1) + '</td>' + _tdOpration + '</tr>';
    $("#tabBody").append(html);
}

function DisplayImg(mime, imgBase64Str) {
    document.getElementById("imgView").src = "data:" + mime + ";base64," + imgBase64Str;
}

function vf() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    var file = _path + '\\' + $(target).parent().prev().text();

    // 把文件转为base64,用于显示查看
    SendJson({func:'FileToBase64', filePath:file, reqId:new Date().getTime()});
}

function DisplayFileView(v) {
    document.getElementById("imgView").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    $('#myModal').modal();
}

// 删除拍照文件
function df() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    $(target).parent().parent().remove();
}

// 删除全部拍照文件
function clearImageFile() {
    $("#tabBody").empty();
}

//显示合并成功的PDF文件
function DisplayMergeFile(v) {
    if (v.result == 0) {
        if (v.filePath) {
            displayOutputInfo(2, (v.fileType == 0 ? "PDF" : "JPG") + "文件合并成功。" + v.filePath, 1);
            OpenFileByPath(v.filePath);
        }
    } else {
        displayOutputInfo(3, (v.fileType == 0 ? "PDF" : "JPG") + "文件合并失败。" + v.func + "<br>" + v.errorMsg, 1);
    }
}

var _uploadFileName = null, _uploadFileReqId = 0;
function DisplayOcrFile(v) {
    if (v.result == 0) {
        if (v.filePath) {
            displayOutputInfo(2, "OCR识别成功。" + v.filePath);
            if ($("#uploadDevice")[0].checked) {
                // OCR识别出的文件后，推送到多功能终端上显示
                _uploadFileName = v.filePath.substr(v.filePath.lastIndexOf("\\") + 1);
                _uploadFileReqId = new Date().getTime();
                SendJson({func:"UploadBase64File", to:1, name:_uploadFileName, fileBase64:v.fileBase64, reqId: _uploadFileReqId});
            }
            OpenFileByPath(v.filePath);
        }
    } else {
        displayOutputInfo(3, "文件OCR失败。" + v.func + "<br>" + v.errorMsg);
    }
}

function DisplayUploadFile(v) {
    if (v.result == 0) {
        if (v.reqId == _uploadFileReqId && _uploadFileName != null) {
            displayOutputInfo(2, "上传OCR生成的文件到终端设备成功。", 1);
            SendJson({func:"DisplayLocalFile", to:1, fileName:_uploadFileName, reqId: new Date().getTime()});
            _uploadFileReqId = 0;
            _uploadFileName = null;
        }
    } else {
        displayOutputInfo(3, "上传OCR生成的文件到终端设备时失败。" + "<br>" + v.errorMsg, 1);
    }
}

function DisplayPuValue(v) {
    if (v.result == 0) {
        // {"cameraId":0,"from":1,"func":"GetCameraPuValue","puValues":
        // [{"id":9963776,"value":50},{"id":9963777,"value":65},{"id":9963778,"value":50}],"reqId":"125","result":0}
        if (v.puValues) {
            for (var i = 0; i < v.puValues.length; ++i) {
                var id = v.puValues[i].id;
                $("#" + id).val(v.puValues[i].value);
            }
            $(".cameraPU").removeAttr("disabled");
        }
    } else {
        displayOutputInfo(3, "获取摄像头亮度、对比度、饱和度失败" + "<br>" + v.errorMsg, 1);
    }
}

function chgPUVal(obj) {
    SendJson({func:"SetCameraPuValue", to:1, devNum:_devNum, puValue:{id:parseInt(obj.id), value:parseInt(obj.value)}, reqId: new Date().getTime()});
}

function OpenFileByPath(filePath) {
    if (location.protocol.substr(0,4) == 'http') {
        var pos = filePath.lastIndexOf('\\');
        pos = filePath.lastIndexOf('\\', pos - 1);
        var url = location.origin + '/tmp/' + filePath.substr(pos + 1).replace('\\','/');
        window.open(url);
    }
}

function SetCameraInfo() {
    var cropType = parseInt($("#cropType").val());
    var rotateAngle = parseInt($("#rotateType").val());
    displayOutputInfo(0);
    SendJson({func:"SetCameraImageInfo", to:1, devNum:_devNum, cropType:cropType, rotate:rotateAngle, reqId: new Date().getTime()});
}

function CaptureDoc() {
    displayOutputInfo(0);
    var imgFileType = parseInt($("#imgFileType").val());
    var now = new Date().getTime();
    var cmd = {func:"CameraCapture", to:1, devNum:_devNum, mode:'base64', imgFileType:imgFileType, reqId:now};
    if ($("#watermark").prop('checked')) {
        // 拍照打水印
        cmd.watermark = {layout: parseInt($("#wmLayout").val()),
                         fontSize: parseInt($("#wmSize").val()),
                         color: $("#wmColor").val(),
                         text: $("#wmText").val()};
    }
    SendJson(cmd);
}

// 打开(1)或关闭(0)人像摄像头,无参数时根据按钮上的文字确定
function OpenCloseCamera(val) {
    var open = false;
    if (typeof(val) == "undefined") {
        open = document.getElementById("btnOpenClose").innerText != "关闭文档摄像头";
    } else {
        open = val != 0;
    }
    var now = new Date().getTime();
    if (_devNum >= 0) {
        // 打开过先关闭,关闭前先停掉视频
        SendJson({func:"GetCameraVideoBuff", to:1, devNum:_devNum, enable:"false", reqId: now++});
        SendJson({func:"CameraAutoCapture", to:1, devNum:_devNum, enable:false, reqId: now++});
        SendJson({func:"CloseCamera", to:1, devNum:_devNum, reqId: now++});
        _devNum = -1;
    }
    if (open) {
        var mediaType = parseInt($("#mediaType").val());
        var resolutionNum = parseInt($("#resolution").val());
        SendJson({func:"OpenCamera", to:1, devNum:0, mediaNum:mediaType, resolutionNum:resolutionNum, reqId: now++});
    }
}

function ReOpenCamera() {
    // 重新打开多功能终端上的文档摄像头
    OpenCloseCamera(1);
}


function SetCameraImageInfo(){
    var imageType = parseInt($("#imageType").val());
    var now = new Date().getTime();
    SendJson({func:"SetCameraImageInfo", to:1, devNum:1, imageType:imageType, reqId:now});
}

function displayOutputInfo(disp, s, add) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        var h = "<b>" + s + "</b>";
        if (add) {
            var h0 = $('#outInfo').html();
            if (h0.length > 0) {
                h = h0 + "<br>" + h;
            }
        }
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html(h);
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function getImgFileArray() {
    var fileArray = new Array();
    var trs = $('#tabBody tr');
    for (i = 0; i < trs.length; i++) {
        fileArray.push(_path + '\\' + trs[i].children[0].innerText);
    }
    return fileArray;
}

function mergePDF() {
    // 合并PDF,只能在PC的websocket服务中执行（因而没有to:1属性），功能终端上没有合并功能.
    // 合并的文件类型 fileType: 0 PDF; 1-TIFF; 2-JPG
    SendJson({func: 'MergeFile', fileType:0, imagePath:getImgFileArray(), reqId: new Date().getTime()});
    displayOutputInfo(1, 'PDF文件合并中...');
}

function mergeJPG() {
    // 合并JPG,只能在PC的websocket服务中执行（因而没有to:1属性），功能终端上没有合并功能.
    // 合并的文件类型 fileType: 0 PDF; 1-TIFF; 2-JPG
    //var type = parseInt($("#wmText").val());
    SendJson({func: 'MergeFile', fileType:2, imagePath:getImgFileArray(), combineType:1, reqId: new Date().getTime()});
    displayOutputInfo(1, 'JPG文件合并中...');
}

function ocrFile() {
    // OCR识别,只能在PC的websocket服务中执行（因而没有to:1属性），功能终端上没有OCR识别功能.
    var strLang = $("#language").find("option:selected").text(), strExtName = $("#fileType").find("option:selected").text();
    var chkTxtOri = $("#detectTextOrie")[0].checked;
    SendJson({func: 'FileOCR', language:strLang, extName:strExtName, detectTextOrientation:chkTxtOri,
            imagePath:getImgFileArray(), mode: 'base64&path', reqId: new Date().getTime()});
    displayOutputInfo(1, 'OCR识别中...');
}

function displayOcrSupportInfo(v) {
    if (v.result == 0) {
        var obj = document.getElementById("language");
        obj.options.length = 0;
        for (var i = 0; i < v.languages.length; i++) {
            // 语言ID是从1开始
            obj.options.add(new Option(v.languages[i], i + 1));
        }
        // 默认选中'Simplified chinese+English'
        obj.selectedIndex = 98;

        obj = document.getElementById("fileType");
        obj.options.length = 0;
        for (var i = 0; i < v.fileTypes.length; i++) {
            obj.options.add(new Option(v.fileTypes[i], i));
        }
    }
}

function OnChgWatermark() {
    var v = "disabled";
    if ($("#watermark").prop('checked')) {
        $("#wmColor").removeAttr(v);
        $("#wmLayout").removeAttr(v);
        $("#wmText").removeAttr(v);
        $("#wmSize").removeAttr(v);
    } else {
        $("#wmColor").attr(v, v);
        $("#wmLayout").attr(v, v);
        $("#wmText").attr(v, v);
        $("#wmSize").attr(v, v);
    }
}

var _timer = 0;
function OnChgAutoCapture() {
    var type = parseInt($("#autoCapture").val());
    // var cmd = {func:"CameraAutoCapture", to:1, devNum:_devNum, reqId: new Date().getTime()};
    // if (type >= 0) {
    //     cmd.enable = true;
    //     cmd.detectionType = type;
    //     cmd.param = parseInt($("#autoParam").val());
    // } else {
    //     cmd.enable = false;
    // }
    // SendJson(cmd);

    if (_timer > 0) {
        window.clearInterval(_timer);
        _timer = 0;
    }
    if (type >= 0) {
        var param = parseInt($("#autoParam").val());
        _timer = window.setInterval(CaptureDoc, param * 1000);
    }
}

$(function () {
    OnChgWatermark();
    // 获取OCR支持信息
    SendJson({func:"GetOcrSupportInfo", reqId:new Date().getTime()});
});

$(window).unload(function(){
    // 关闭页面之前,关闭摄像头
    if (_devNum >= 0) {
        OpenCloseCamera(0);
    }
});
