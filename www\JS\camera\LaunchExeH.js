function DisplayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["#b5daf1", "#9de09d", "#f3a960"];
        $('#outInfo').css("display", "").css("background-color", ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function DisplayImage(v) {
    if (v.photos) {
        for (var i = 0; i < v.photos.length; i++) {
            var h = '<img src="data:' + v.mime + ';base64,' + v.photos[i] + '">';
            $("#divImages").prepend(h);
        }
        DisplayOutputInfo(2, "收到Exe程序的拍照结果，共 " + v.photos.length + " 个文件.");
    }
}

function launchExe(exeName) {
    // 禁用按钮，防止重复点击
    document.getElementById("btnStart").disabled = true;
    //jQuery.support.cors = true;

	
    $.ajax({
        type: 'POST',
        url: "/video=photo",
        dataType: "JSON",
        contentType: "application/json;",
        data: JSON.stringify({"camidx":"0","timeout":"50","caption":"请扫描文件然后按【保存】"}),
        success: function(v,xhr,status) {
            if (v.code == "0") {
                DisplayOutputInfo(2, v.message);
                DisplayImage(v);
            } else {
                DisplayOutputInfo(3, v.message);
            }
            document.getElementById("btnStart").disabled = false;
        },
        error: function (msg) {
            console.log(msg);
        }
    });
	
}


