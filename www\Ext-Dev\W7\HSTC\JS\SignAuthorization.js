var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    //_url = "ws://************:10086";
    console.log(_url);
}

initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e.data);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

function SendBlob(blob) {
    _connected ? _ws.send(blob) : ConnectServer(SendBlob, blob)
}

function SendHalfBinMsg(json, ...blobs) {
    // 消息息为“半二进制”方式。消息字节格式如下：
    // [协议头,固定字节 0x22                       1个字节]
    // [文本区(JSON字符串)的字节数,                 4个字节, 高位在前]
    // [二进制数据区 的字节数,                      4个字节, 高位在前]
    // [文本区(JSON字符串)的内容, ascii编码 ......]
    // [二进制数据区(签署后的pdf等相关信息)......]

    var strJson = JSON.stringify(json);
    //var lenJson = strJson.length;
    var blobStrJson = new Blob([strJson]);
    var lenJson = blobStrJson.size;
    var lenBlob = 0;
    var blobCount = blobs.length;
    for (var i = 0; i < blobCount; ++i) {
        lenBlob += blobs[i].size;
    }
    console.log(lenJson, lenBlob, strJson);

    var headAndLength = new Uint8Array(9);
    headAndLength[0] = 0x22;
    headAndLength[1] = (lenJson >> 24) & 0xFF;
    headAndLength[2] = (lenJson >> 16) & 0xFF;
    headAndLength[3] = (lenJson >> 8) & 0xFF;
    headAndLength[4] = (lenJson) & 0xFF;
    headAndLength[5] = (lenBlob >> 24) & 0xFF;
    headAndLength[6] = (lenBlob >> 16) & 0xFF;
    headAndLength[7] = (lenBlob >> 8) & 0xFF;
    headAndLength[8] = (lenBlob) & 0xFF;

    var blobMsg = null;
    switch (blobCount) {
        case 1:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0]]);
            break;
        case 2:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1]]);
            break;
        case 3:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1], blobs[2]]);
            break;
        case 4:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1], blobs[2], blobs[3]]);
            break;
    }
    SendBlob(blobMsg);
}

// 用UploadFile发送文件
function sendFile(file, type) {
    var SEC_MAX_SIZE = 4194304;
    var fileSize = file.size;
    var sectionCount = Math.ceil(fileSize / SEC_MAX_SIZE);
    //var encFileName = EncodeUtf8(file.name);
    var json = {
        func: "UploadFile",
        to: 1,
        type: type,
        reqId: new Date().getTime().toString(10),
        fileInfo: {fileCount: 1, fileNum: 0, fileSize: fileSize, sectionCount: sectionCount, fileName: file.name},
        content: [0, 0]
    }

    for (var i = 0; i < sectionCount; ++i) {
        json.fileInfo.fileOffset = i * SEC_MAX_SIZE;
        json.fileInfo.sectionNum = i;
        if (sectionCount == 1) {
            json.content[1] = fileSize;
            SendHalfBinMsg(json, file);
        } else {
            var start = i * SEC_MAX_SIZE;
            var end = min(start + SEC_MAX_SIZE, fileSize);
            json.content[1] = end - start;
            var fileSection = file.slice(start, end);
            SendHalfBinMsg(json, fileSection);
        }
    }
}

function base64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
        .replace(/\-/g, '+')
        .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
        outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
}

function arrayBufferToBase64(buffer) {
    var binary = '';
    var bytes = new Uint8Array(buffer);
    var len = bytes.byteLength;
    for (var i = 0; i < len; i++) {
        binary += String.fromCharCode(bytes[i]);
    }
    return window.btoa(binary);
}

function EncodeUtf8(s1) {
    var s = escape(s1);
    var sa = s.split("%");
    var retV = "";
    if (sa[0] != "") {
        retV = sa[0];
    }
    for (var i = 1; i < sa.length; i++) {
        if (sa[i].substring(0, 1) == "u") {
            retV += Hex2Utf8(Str2Hex(sa[i].substring(1, 5)));

        } else retV += "%" + sa[i];
    }
    return retV;
}

function Str2Hex(s) {
    var c = "";
    var n;
    var ss = "0123456789ABCDEF";
    var digS = "";
    for (var i = 0; i < s.length; i++) {
        c = s.charAt(i);
        n = ss.indexOf(c);
        digS += Dec2Dig(eval(n));
    }
    return digS;
}

function Dec2Dig(n1) {
    var s = "";
    var n2 = 0;
    for (var i = 0; i < 4; i++) {
        n2 = Math.pow(2, 3 - i);
        if (n1 >= n2) {
            s += '1';
            n1 = n1 - n2;
        } else {
            s += '0';
        }
    }
    return s;
}

function Dig2Dec(s) {
    var retV = 0;
    if (s.length == 4) {
        for (var i = 0; i < 4; i++) {
            retV += eval(s.charAt(i)) * Math.pow(2, 3 - i);
        }
        return retV;
    }
    return -1;
}

function Hex2Utf8(s) {
    var retS = "";
    var tempS = "";
    var ss = "";
    if (s.length == 16) {
        tempS = "1110" + s.substring(0, 4);
        tempS += "10" + s.substring(4, 10);
        tempS += "10" + s.substring(10, 16);
        var sss = "0123456789ABCDEF";
        for (var i = 0; i < 3; i++) {
            retS += "%";
            ss = tempS.substring(i * 8, (eval(i) + 1) * 8);
            retS += sss.charAt(Dig2Dec(ss.substring(0, 4)));
            retS += sss.charAt(Dig2Dec(ss.substring(4, 8)));
        }
        return retS;
    }
    return "";
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(data) {
    //console.log(typeof (data));
    var head = data.slice(0, 9);
    var reader = new FileReader();
    reader.readAsArrayBuffer(head);
    reader.onload = function (e) {
        var buf = new Uint8Array(reader.result);
        if (buf[0] == 0x22 && buf[1] == 0x0) {
            // 是半二进制信息，拆分json串与bin内容
            var lenJson = buf[1] << 24 | buf[2] << 16 | buf[3] << 8 | buf[4];
            var lenBin = buf[5] << 24 | buf[6] << 16 | buf[7] << 8 | buf[8];
            reader.readAsText(data.slice(9, 9 + lenJson), 'utf-8');
            reader.onload = function (e) {
                //console.info(reader.result);
                var jsonObj = JSON.parse(reader.result);
                console.log(jsonObj);
                var binData = data.slice(9 + lenJson, 9 + lenJson + lenBin);
                switch (jsonObj.func) {
                    case "SigningAuthorization":
                        displaySigningAuthorizationResult(jsonObj, binData);
                        break;
                }
            }
        }
    };
}

function displaySigningAuthorizationResult(v, binData) {
    // {
    //     "allograph": false,
    //     "fileInfos": [
    //     {
    //         "endIndex": 274650,
    //         "infoName": "faceDetectImg.jpg",
    //         "startIndex": 0
    //     },
    //     {
    //         "endIndex": 286854,
    //         "infoName": "signImg.png",
    //         "startIndex": 274650
    //     },
    //     {
    //         "endIndex": 453030,
    //         "infoName": "signPdf.pdf",
    //         "startIndex": 286854
    //     },
    //     {
    //         "endIndex": 510809,
    //         "infoName": "finger.png",
    //         "startIndex": 453030
    //     }
    // ],
    //     "from": 1,
    //     "func": "SigningAuthorization",
    //     "idCard": {
    //         "address": "湖南省桃源县车湖垸乡墟场",
    //         "birthDate": "19930906",
    //         "headPic": "Qk3OlwAAAAAAADYA.......",
    //         "idCardNum": "******************",
    //         "name": "张航嘉",
    //         "nation": "汉",
    //         "sex": "男",
    //         "signOffice": "桃源县公安局",
    //         "usefulEndDate": "20200429",
    //         "usefulStartDate": "20100429"
    //      },
    //     "reqId": "1656486554674",
    //     "result": 0
    // }
    if (v.result == 0) {
        displayOutputInfo(2, "签署诚信授权成功.");
        $('#divResult').css("display", "");
        var idCard = v.idCard;
        $('#idName').text(idCard.name);
        $('#idSex').text(idCard.sex);
        $('#idNation').text(idCard.nation);
        $('#idNum').text(idCard.idCardNum);
        $('#idBirthDate').text(idCard.birthDate);
        $('#idAddress').text(idCard.address);
        $('#idSignOffice').text(idCard.signOffice);
        $('#idValidityDate').text(idCard.usefulStartDate + " - " + idCard.usefulEndDate);
        $('#labelSign').text(v.allograph ? "签名 [代理签名]" : "签名 [本人签名]");
        document.getElementById("idHead").src = "data:bmp;base64," + idCard.headPic;

        var files = v.fileInfos;
        for (var i = 0; i < files.length; i++) {
            var infoName = files[i].infoName; // signImg.png
            var startIndex = files[i].startIndex;
            var endIndex = files[i].endIndex;
            var ary = infoName.split(".");
            var obj = document.getElementById(ary[0]);
            if (obj) {
                var fileData = binData.slice(startIndex, endIndex);
                if (obj.src) {
                    window.URL.revokeObjectURL(obj.src);
                    obj.src = "";
                }
                if (ary[1] == "pdf") {
                    var pdfData = new Blob([fileData], { type: 'application/pdf' });
                    obj.src = getObjectURL(pdfData);
                } else {
                    obj.src = getObjectURL(fileData);
                }
                //simulateDownload(infoName, fileData);

                // var reader = new FileReader();
                // reader.myInfoName = infoName;
                // reader.readAsDataURL(fileData);
                // reader.onload = function (e) {
                //     console.info(reader.myInfoName, reader.result);
                //     if (reader.result) {
                //         var ary2 = reader.myInfoName.split(".");
                //         var obj2 = document.getElementById(ary2[0]);
                //         if (obj2) {
                //             obj2.src = "data:jpeg" + reader.result.substr(reader.result.indexOf(";base64,"));
                //         }
                //     }
                // }
            }
        }
    } else {
        $('#divResult').css("display", "none");
        var info = "签署诚信授权 失败<br>result = " + v.result;
        if (v.errorMsg) {
            info += "<br>" + v.errorMsg;
        }
        displayOutputInfo(3, info);
    }
}

//建立一个可存取到该file的url
function getObjectURL(file) {
    var url = null;
    // 下面函数执行的效果是一样的，只是需要针对不同的浏览器执行不同的 js 函数而已
    if (window.createObjectURL != undefined) {   // basic
        url = window.createObjectURL(file);
    } else if (window.URL != undefined) {        // mozilla(firefox)
        url = window.URL.createObjectURL(file);
    } else if (window.webkitURL != undefined) {  // webkit or chrome
        url = window.webkitURL.createObjectURL(file) ;
    }
    return url;
}

function simulateDownload(fileName, fileData) {
    var downloadElement = document.createElement('a'); //创建一个a 虚拟标签
    var href = window.URL.createObjectURL(fileData); // 创建下载的链接
    downloadElement.href = href;
    downloadElement.download = decodeURI(fileName);
    document.body.appendChild(downloadElement);
    downloadElement.click(); // 点击下载
    document.body.removeChild(downloadElement); // 下载完成移除元素
    window.URL.revokeObjectURL(href);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "UploadFile":
            if (jsonObj.result == 0) {
                displayOutputInfo(2, "上传文件成功.");
                startSign2();
            } else {
                displayOutputInfo(3, "上传文件失败.<br>result = " + jsonObj.result);
            }
            break;
        case "SigningAuthorization":
            if (jsonObj.result == 0) {
                displayOutputInfo(2, "签署诚信授权成功.");
                startSign2();
            } else {
                displayOutputInfo(3, "签署诚信授权失败.<br>result = " + jsonObj.result);
            }
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                var info = jsonObj.func + "<br>";
                if (jsonObj.errorMsg) {
                    info += jsonObj.errorMsg;
                }
                displayOutputInfo(3, info);
            }
            break;
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

// 开始[开始签署诚信授权], 先上传诚信授权文件
function startSign() {
    // type: 传文件的用途。1:签署诚信签名pdf文件.   2首页轮播图
    var objPdfFile = document.getElementById("filePdf");
    if (objPdfFile.files.length <= 0) {
        alert("请先选择【诚信授权文件】.");
    }  else if (document.getElementById('ttsText').value.length == 0) {
        alert("请输入【要朗读的文本】.");
    } else {
        sendFile(objPdfFile.files[0], 1);
    }
}

// 诚信授权文件上传完成之后，下SigningAuthorization指令
function startSign2() {
    var txt = document.getElementById('ttsText').value;
    SendJson({func: "SigningAuthorization", reqId: new Date().getTime().toString(10), to:1, speechText: txt});
}

function LoadFromTxt() {
    document.getElementById("fileTxt").click();
}

function onChgFilePDF() {
    var files = document.getElementById("filePdf").files;
    if (files.length > 0) {
        $("#filePdf").parent().prev().prop("placeholder", files[0].name);
    }
}

function onChgFileTxt() {
    var fileList = document.getElementById("fileTxt").files;
    if (fileList.length > 0) {
        var reader = new FileReader();
        reader.readAsText(fileList[0], "UTF-8");
        reader.onload = function (e) {
            document.getElementById('ttsText').value = e.target.result;
            document.getElementById("fileTxt").value = '';
        }
    }
}

function onChgTtsTxt() {
    var hasFile = document.getElementById("filepDF").files.length > 0;
    var hasTxt = document.getElementById("ttsText").value.length > 0;
    document.getElementById('uploadBtn').disabled = (!hasFile || !hasTxt);
}