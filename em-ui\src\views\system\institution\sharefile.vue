<template>
  <div class="app-container" style="background-color:white;">

    <h4 class="form-header h4">分享文件</h4>

     <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" >输入密码</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading"  ref="table"  :data="shareList">


      <el-table-column label="文件" align="center"  key="fileName" prop="fileName" />
     
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
           <el-button size="mini" type="text" icon="el-icon-download" @click="handleDown(scope.row)" >下载</el-button>
        </template>
      </el-table-column>
    </el-table>

     <!-- 添加或修改用户配置对话框 -->
    <el-dialog title="分享密码" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="workorgform" :model="workorgform"  label-width="80px">
       
            
        
            <el-form-item label="密码" prop="pwd">
               <el-input v-model="workorgform.pwd" placeholder="请输入分享密码" />
            </el-form-item>
          
       
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>

import {listShare,addShare,filelist} from "@/api/institution/share";

export default {
  name: "Sharefile",
  data() {
    return {
       // 遮罩层
      loading: true,
      //分享密码
      pwd:undefined,
      url:undefined,
      //制度化管理
      institutionId:undefined,
      // 制度化信息
      form: {},
      //分享列表
      shareList:[],
       // 查询参数
      queryParams: {

      },
      //用户部门信息
      workorgform:{},
      // 是否显示弹出层
      open:true,
    };
  },
  created() {
    const shareId = this.$route.params && this.$route.params.shareId;
    console.log(shareId)
    this.url = shareId
  
  },
  methods: {

    handleAdd(){
       this.open=true;
       this.workorgform.pwd=""
    },

    getList(){
      this.loading = true;
      this.queryParams.url=this.url
      this.queryParams.pwd=this.pwd
      filelist(this.queryParams).then((response)=>{
          this.shareList = response.data;
          this.loading = false;
      }).catch(error => {
          this.loading = false;
          // this.open=true;
          // this.workorgform.pwd="密码错误重新输入"
       })
    },

     /** 提交按钮 */
    submitForm(){

      if(this.workorgform.pwd != undefined && this.workorgform.pwd != ""){
          this.pwd=this.workorgform.pwd 
          this.getList()
          this.open=false
      }else{
           this.$modal.msgError("请输入密码");
      }

    },

    /** 删除按钮操作 */
    handleDown(row) {
      const fileName = row.fileName || this.ids;
      const downurl = process.env.VUE_APP_BASE_API+"/share/download/"+this.url+"/"+fileName+"?pwd="+this.pwd;
      window.open(downurl)
    },


    /** 单击选中行数据 */
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },

    // 保存选中的数据编号
    getRowKey(row) {
      return row.roleId;
    },


  },
};
</script>