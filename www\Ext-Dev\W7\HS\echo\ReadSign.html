<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>手写签名演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>手写签名 演示</h1>
        <p>这个页面演示了如何启动安卓多功能终端上的手写签名功能，然后在页面上显示签名图片。</p>
    </div>

    <div class="page-header">
    </div>

    <div class="col-md-12" style="text-align: center">
        <img id="pic" src="" style="width:320px; height:240px;">
        <p>签名图片</p>
        <p><button type="button" class="btn btn-primary" onclick="StartSign()">开始手写签名</button></p>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../../langs/zhCN.js"></script>
<script src="JS/ReadSign.js"></script>
</body>
</html>