/*
    Version: 1.0 Timestamp: 2021.4.23
*/
html,body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
.ifram{
    width: 100%;
    height: 100%;
}

.left{
    width: 250px;
    min-width: 250px;
    height: 100%;
    border:1px solid #000;
}
.mindle{
    width: calc(100%);
    height: 100%;
    border-top:1px solid #000;
    border-bottom:1px solid #000;
}
.right{
    width: 150px;
    min-width: 150px;
    height: 100%;
    border: 1px solid #000;
}

.left-one{
    height: 15%;
}
.left-one-left{
    float: left;
    width: 35%;
    margin: 5px 5px 0 15px;
}
.left-one-left img{
    width: 80%;
}
.left-one-right{
    float: left;
    width: 45%;
    margin: 10px 5px 0 0;
}
.left-one-right select{
    width: 95%;
    height: 25px;
    margin-top: 3px;
}
.left-two{
    height: 85%;
    padding: 0 0 1% 0;
}
.left-two-lable{
    width: 40%;
    height: 30px;
    margin: 3px 3px 0px 3px;
    display:inline-block;
}
.left-two-set{
    width: 35%;
    height: 30px;
    text-align: center;
    margin: 3px 3px 0px 3px;
    display:inline-block;
}
.left-two-select{
    width: 40%;
    height: 30px;
    margin: 3px 3px 3px 3px;
}

.mindle-top {
    height: calc(99% - 50px);
    text-align: center;
}
.mindle-top-img{
    position: relative;
    background-color:white;
}
.mindle-botom {
    height: 50px;
    line-height: 50px; /* 设置垂直居中 */
    text-align:center;
    border-top:1px solid #000;
}
.mindle-botom button{
    width: 100px;
    height: 30px;  
}

.right-div{
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: scroll;
}

.mask-layer{
    filter:alpha(opacity=10);
    opacity:.5;
    left:0px;
    top:0px;
    position:fixed;
    height:100%;
    width:100%;
    overflow:hidden;
    z-index:997;
}

.mask-layer-img-div{
    position: relative;
    left: 50%;
    top: 50%;
    margin-top: -50px; /* 高度的一半 */
    margin-left: -50px; /* 宽度的一半 */
    height:100px;
    width:100px;
    z-index:999;
}

.mask-layer-img-div img{
    width: 100px;
}

.mask-layer-img-div-span{
    width: 100px;
    font-size: 15px;
    color: black;
    margin: 10px;
}