var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    //_url = "ws://************:10086";
    console.log(_url);
}

initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e.data);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

function SendBlob(blob) {
    _connected ? _ws.send(blob) : ConnectServer(SendBlob, blob)
}

function SendHalfBinMsg(json, ...blobs) {
    // 消息息为“半二进制”方式。消息字节格式如下：
    // [协议头,固定字节 0x22                       1个字节]
    // [文本区(JSON字符串)的字节数,                 4个字节, 高位在前]
    // [二进制数据区 的字节数,                      4个字节, 高位在前]
    // [文本区(JSON字符串)的内容, ascii编码 ......]
    // [二进制数据区(签署后的pdf等相关信息)......]

    var strJson = JSON.stringify(json);
    //var lenJson = strJson.length;
    var blobStrJson = new Blob([strJson]);
    var lenJson = blobStrJson.size;
    var lenBlob = 0;
    var blobCount = blobs.length;
    for (var i = 0; i < blobCount; ++i) {
        lenBlob += blobs[i].size;
    }
    console.log(lenJson, lenBlob, strJson);

    var headAndLength = new Uint8Array(9);
    headAndLength[0] = 0x22;
    headAndLength[1] = (lenJson >> 24) & 0xFF;
    headAndLength[2] = (lenJson >> 16) & 0xFF;
    headAndLength[3] = (lenJson >> 8) & 0xFF;
    headAndLength[4] = (lenJson) & 0xFF;
    headAndLength[5] = (lenBlob >> 24) & 0xFF;
    headAndLength[6] = (lenBlob >> 16) & 0xFF;
    headAndLength[7] = (lenBlob >> 8) & 0xFF;
    headAndLength[8] = (lenBlob) & 0xFF;

    var blobMsg = null;
    switch (blobCount) {
        case 1:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0]]);
            break;
        case 2:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1]]);
            break;
        case 3:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1], blobs[2]]);
            break;
        case 4:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1], blobs[2], blobs[3]]);
            break;
    }
    SendBlob(blobMsg);
}

// 用UploadFile发送文件
function sendFiles() {
    var SEC_MAX_SIZE = 4194304;
    var files = document.getElementById("fileJpg").files;
    var fileCount = files.length;
    var json = {
        func: "UploadFile",
        to: 1,
        type: 2, // 传文件的用途。1:签署诚信签名pdf文件. 2首页轮播图
        reqId: new Date().getTime().toString(10),
        fileInfo: {fileCount: fileCount, fileNum: 0, fileSize: 0, sectionCount: 1, fileName: ""},
        content: [0, 0]
    }

    for (var i = 0; i < fileCount; ++i) {
        var file = files[i];
        displayOutputInfo(2, "正在上传 " + file.name);
        json.fileInfo.fileNum = i;
        json.fileInfo.fileName = file.name;
        json.fileInfo.fileSize = file.size;
        var sectionCount = Math.ceil(file.size / SEC_MAX_SIZE);
        json.fileInfo.sectionCount = sectionCount;

        for (var j = 0; j < sectionCount; ++j) {
            json.fileInfo.fileOffset = j * SEC_MAX_SIZE;
            json.fileInfo.sectionNum = j;
            if (sectionCount == 1) {
                json.content[1] = file.size;
                SendHalfBinMsg(json, file);
            } else {
                var start = j * SEC_MAX_SIZE;
                var end = min(start + SEC_MAX_SIZE, file.size);
                json.content[1] = end - start;
                var fileSection = file.slice(start, end);
                SendHalfBinMsg(json, fileSection);
            }
        }
    }
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(data) {
    //console.log(typeof (data));
}

function displaySigningAuthorizationResult(v, binData) {
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "UploadFile":
            if (jsonObj.result == 0) {
                displayOutputInfo(2, "轮播图片上传成功.");
            } else {
                displayOutputInfo(3, "轮播图片上传失败.<br>result = " + jsonObj.result);
            }
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                var info = jsonObj.func + "<br>";
                if (jsonObj.errorMsg) {
                    info += jsonObj.errorMsg;
                }
                displayOutputInfo(3, info);
            }
            break;
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

// 开始上传
function startUpload() {
    var objJpgFile = document.getElementById("fileJpg");
    if (objJpgFile.files.length <= 0) {
        alert("请先选择【轮播图片文件】.");
    } else {
        displayOutputInfo(2, "轮播图片上传中...");
        if (_connected) {
            sendFiles();
        } else {
            ConnectServer(sendFiles, null);
        }
    }
}

function onChgFileJpg() {
    var str = "";
    var files = document.getElementById("fileJpg").files;
    for (var i = 0; i < files.length; ++i) {
        if (i > 0) {
            str += "; ";
        }
        str += files[i].name;
    }
    $("#fileJpg").parent().prev().prop("placeholder", str);
}
