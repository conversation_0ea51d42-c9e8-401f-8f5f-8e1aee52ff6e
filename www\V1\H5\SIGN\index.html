<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <!-- utf-8编码-->
    <meta charset="utf-8">
    <title>CamSDK</title>
</head>
<body >
  
    <div
		
		<div style="float:left">
		
			<div id="video1">
				<img id="video" style="width:506px;height:380px" >
			</div>
			
			<p>	
			<td>手写板来源：
		<select style="width:90;" name="signType" id="signType" onchange = "SetSignType(signType)">
			<option value="0">拍摄仪采集</option>
			<option value="1">本地图片</option>
			<option value="2">无</option>
		</select>
	</td>
	<label id="ImageLabel">请输入图片路径</label>
	<td >
		<input type = "text" name="Imagepath" id="Imagepath"  value = """ >
	</td>
	<p>
	<td>保存手写板签名格式：
		<select style="width:90;" name="saveSign" id="saveSign" onchange = "saveSign(saveSign)">
			<option value=".jpg">jpg</option>
			<option value=".bmp">bmp</option>
			<option value=".png">png</option>
			<option value=".tif">tif</option>
			<option value="">base64</option>
		</select>
	</td>
	<input type="checkbox" id="AutoCrop" onchange="AutoCropChanged()">自动裁切</input>
	<input type="checkbox" id="DounmentEn" onchange="DounmentEn()">文档优化</input>
	<input type="checkbox" id="ManualCrop" onchange="ManualCrop()">手动裁切</input>
	<p>
	<td>存储路径：
		<input type = "text" name="path" id="path"  value = "C:\tmp" onchange="getPath()" >
	</td>
	<td>
		<a id="saveFolder" style = "width:60;" href="C:\tmp">openFolder</a>
	</td>
	<p>
	<button width=80 onclick="sign()">签名</button>	
			<p>	
			
		</div>
	</div>

     <!-- SDK封装 -->
    <script src="js/globeVar.js"></script>
    <script src="js/mainH5.js"></script>
    <script src="js/WebSocket.js"></script>

    <!--页面逻辑-->
   <script src="js/sign.js"></script> 


</body>
</html>