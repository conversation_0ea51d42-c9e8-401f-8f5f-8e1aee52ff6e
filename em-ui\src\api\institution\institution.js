import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询制度化分类列表
export function listClass(query) {
    return request({
      url: '/institutionclass/doQuery',
      method: 'post',
      data: query
    })
}

// 查询制度化列表
export function listInstitution(query) {
  return request({
    url: '/institution/doQuery',
    method: 'post',
    data: query
  })
}


// 查询分类详细
export function getClass(institutionclassId) {
  return request({
    url: '/institutionclass/' + parseStrEmpty(institutionclassId),
    method: 'get'
  })
}

// 查询制度化详细
export function getInstitution(institutionId) {
  return request({
    url: '/institution/' + parseStrEmpty(institutionId),
    method: 'get'
  })
}

// 新增分类
export function addClass(data) {
  return request({
    url: '/institutionclass/doAdd',
    method: 'post',
    data: data
  })
}

// 新增制度化
export function addInstitution(data) {
  return request({
    url: '/institution/doAdd',
    method: 'post',
    data: data
  })
}


// 修改分类
export function updateClass(data) {
  return request({
    url: '/institutionclass/doEdit',
    method: 'post',
    data: data
  })
}

// 修改制度化
export function updateInstitution(data) {
  return request({
    url: '/institution/doEdit',
    method: 'post',
    data: data
  })
}


// 删除制度化
export function delInstitution(institutionId) {
  return request({
    url: '/institution/doDelete/' + institutionId,
    method: 'post'
  })
}


