<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>救助资格认证 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="DeviceConfig.html">设备配置信息</a></li>
                <li><a href="Slideshow.html">轮播图片设置</a></li>
                <li><a href="SignAuthorization.html">签署诚信授权</a></li>
                <li class="active"><a href="#">救助资格认证</a></li>
<!--                <li><a href="TakePhoto.html">文档拍照</a></li>-->
            </ul>
        </div>
    </div>
</nav>

<div style="float: left;">
    <img id="product" src="image/W7_2.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>救助资格认证 演示</h1>
        <p>点击【开始救助资格认证】，在一体机会开始救助资格认证。认证过程中，需要在一体机上读取身份证。认证成功后，页面会显示认证人员的身份信息。</p>
    </div>

    <div class="page-header"></div>

    <div class="col-md-12" style="text-align: center">
        <p><button id="startBtn" type="button" class="btn btn-primary" onclick="startCertification()">开始救助资格认证</button></p>
    </div>

    <div id="divResult" class="col-md-12" style="display:none">
        <div class="col-md-4">
            <label>姓名：</label><span id="idName" style="padding-right:20px;"></span>
            <label>性别：</label><span id="idSex" style="padding-right:20px;"></span>
            <label>民族：</label><span id="idNation" style="padding-right:20px;"></span><br>
            <label>出生：</label><span id="idBirthDate"></span><br>
            <label>身份证号：</label><span id="idNum"></span><br>
            <label>住址：</label><span id="idAddress"></span><br>
            <label>发证：</label><span id="idSignOffice"></span><br>
            <label>有效期：</label><span id="idValidityDate"></span><br>
        </div>
        <div class="col-md-4">
            <label>身份证照片</label><br>
            <img id="idHead" src="" style="width:102px;height:126px;">
            <br><div id="certifyInfo" style="font-size:18px;padding-top:10px;"></div>
        </div>
        <div class="col-md-4">
            <label>现场头像</label><br>
            <img id="faceDetectImg" src="" style="width:320px;height:240px;">
        </div>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/CertifiedSalvageQualification.js"></script>
</body>
</html>