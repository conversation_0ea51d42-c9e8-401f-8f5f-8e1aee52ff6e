<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Camera</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/en.js"></script>
    <script src="../../JS/cropBox.js"></script>
    <script src="../../JS/camera/camera.js"></script>
    <link rel="stylesheet" href="../../CSS/cropBox.css"/>
    <link rel="stylesheet" href="../../CSS/camera/camera.css"/>
</head>
<body>
    <table class="ifram">
        <tr>
            <td class="left" valign="top">
                <div class="left-one">
                    <div class="left-one-left">
                        <img id="cameraImage" src="../../image/camera.png"/>
                    </div>
                    <div class="left-one-right">
                        <span>Basic function demo</span>
                    </div>
                </div>
                <div class="left-two">
                    <div>
                        <label class="left-two-lable">Device:</label>
                        <select id="cameraInfo" class="left-two-select" onchange="switchCameraInfo_Fun()"></select><br>
                        <!--<label class="left-two-lable">File type:</label>
                        <select id="file_format" class="left-two-select">
                            <option value="0">jpg</option>
                            <option value="1">tif</option>
                            <option value="2">png</option>
                            <option value="3">bmp</option>
                        </select><br>-->
                        <label class="left-two-lable">Video Compression:</label>
                        <select id="mediaType" class="left-two-select" onchange="setCameraInfo_Fun()"></select><br>
                        <label class="left-two-lable">Resolution:</label>
                        <select id="resolution" class="left-two-select" onchange="setCameraInfo_Fun()"></select><br>
                        <label class="left-two-lable">Rotate:</label>
                        <select id="rotateType" class="left-two-select" onchange="OnRotateChange()">
                            <option value="0"> 0°</option>
                            <option value="90">90°</option>
                            <option value="180">180°</option>
                            <option value="270">270°</option>
                            <option value="361" id="opOcrDirection" disabled>Auto text direction</option>
                        </select><br>
                        <div style="display: none">
                        <label class="left-two-lable">OCR language:</label>
                        <select id="language" class="left-two-select" onchange="setCameraInfo_Fun()"></select><br>
                        </div>
                        <label class="left-two-lable">Cropping:</label>
                        <select id="crop_type" class="left-two-select" onchange="setCameraImageInfo_Fun()">
                            <option value="0">Not cropped</option>0-
                            <option value="1">Single graph cropped</option>
                            <option value="2">Multi graph cropped</option>
                            <!--<option value="3">Flattens the surface</option>-->
                            <option value="9">Custom cropped</option>
                        </select><br>
                        <label class="left-two-lable">Effects:</label>
                        <select id="image_type" class="left-two-select">
                            <option value="0">Color(Original)</option>
                            <option value="6">Color(Enhanced)</option>
                            <option value="1">Grayscale</option>
                            <option value="2">B&W(Document)</option>
                            <option value="4">B&W(Red stamp)</option>
                            <option value="5">B&W(Blue stamp)</option>
                            <!--<option value="7">Bill enhancement</option>-->
                        </select><br>
                        <label class="left-two-lable">Edge fixing:</label>
                        <select id="fill_border" class="left-two-select">
                            <option value="0">No fill</option>
                            <option value="1">Mapping fill</option>
                            <option value="2">Auto fill white</option>
                            <option value="3">Auto fill solid color</option>
                        </select><br>
                        <label class="left-two-lable">Removal:</label>
                        <select id="foreign_remove" class="left-two-select">
                            <option value="0">None</option>
                            <!--<option value="1">Finger Removal(only capture book)</option>-->
                            <option value="2">Punch Hole Removal</option>
                        </select>
                        <label class="left-two-lable">USB Button:</label>
                        <select id="selExtButton" class="left-two-select" onchange="OnExtButtonChange()">
                            <option value="None" selected>Disable</option>
                            <option value="Notify">Notify</option>
                            <option value="CameraCapture">Camera Capture</option>
                            <option value="CameraCaptureBook" disabled title="Does not apply on this page">Camera Capture Book</option>
                            <option value="RecogBarCode">Recog Barcode</option>
                        </select>
                        <label class="left-two-lable">Display results:</label>
                        <textarea id="resultArea"></textarea>
                    </div>
                </div>
            </td>
            <td class="mindle">
                <div class="mindle-top">
                    <div class="mindle-top-img"> 
                        <img id="pic"/>
                    </div>
                    <div id="vertical-line" class="mindle-top-line" hidden>
                    </div>
                </div>
                <div class="mindle-botom" >
                    <button id="RecogBarCode" onclick="recogBarCode_Fun()">Bar code</button>
                    <button id="CaptureBase64" onclick="cameraCapture_Fun()">Capture</button>
                    <button id="clearImage" onclick="clearImageFile()">Clear</button>
                </div>
            </td>
            <td class="right">
                <div class="right-div" id="right_div">
                    <table>
                        <tbody id="image_data" align="center"></tbody>
                    </table>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>
