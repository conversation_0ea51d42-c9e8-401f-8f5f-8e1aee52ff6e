<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>手写签名演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="FileDisplay.html">文件播放</a></li>
                <li><a href="IdCard.html">身份证读取</a></li>
                <li><a href="ReadFinger.html">指纹读取</a></li>
                <li><a href="IdCardMatch.html">人证比对</a></li>
                <li><a href="DocOCR.html">OCR识别与水印</a></li>
                <li class="active"><a href="#">手写签名</a></li>
                <li><a href="ReadKeyboard.html">评价按键读取</a></li>
                <li><a href="Barcode.html">条码扫描与识别</a></li>
                <li><a href="LayoutRecognition.html">版面识别</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/W7-0911.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>手写签名 演示</h1>
        <p>这个页面演示了如何启动安卓多功能终端上的手写签名功能,把手写签名叠加在指定的图片上，然后在页面上显示叠加了签名后的图片。</p>
    </div>

    <div class="page-header">
    </div>

    <div class="col-md-6" style="text-align: center">
        <div style="width:480px;height: 360px;">
            <canvas id="cvs2" class="demo-canvas-centerlize" width="960" height="720" style="transform: scale(0.5);"></canvas>
        </div>
        <p>签名前图片</p>
        <input id="btnUpfile" type="file" accept="image/png, image/bmp, image/jpg, image/jpeg" style="display: none;">
        <button type="button" class="btn btn-primary" onclick="StartSign()">选择签名前图片,开始手写签名</button>
    </div>

    <div class="col-md-6" style="text-align: center">
        <img id="pic" src="" style="width:480px; height:360px;">
        <p>签名后图片</p>
    </div>

    <div class="col-md-12" style="display: none">
        <div class="col-md-6">
            <p>签名轨迹数据</p>
            <pre id="strokes" style="width: 100%;height: 300px"></pre>
        </div>
        <div class="col-md-6">
            <p>签名轨迹数据格式说明</p>
            <pre style="width: 100%;height: 300px">
starTime: 签名开始的时间(时间戳,从1970年1月1日开始所经过的毫秒数)。
strokes:  签名笔画的数组, 每个元素表示一笔。
strokes元素: 由多个笔迹点的数据依次组成的数组。
             每个笔迹点的数据为:座标x,座标y,压感,时间(与starTime的差值)
            </pre>
        </div>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="../../../JS/common/jsonTools.js"></script>
<script src="JS/ReadSign.js"></script>
</body>
</html>