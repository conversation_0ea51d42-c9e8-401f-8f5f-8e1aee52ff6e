var Lang = window.navigator.language=='zh-CN'?0:1;

console.log(Lang);
//html/js/main.js
var main_Upload = ['上传返回值','Upload return value'];
var main_Base64 = ['拍照为Base64返回值','Return value for Base64'];
var main_NotOpen = ['连接已断开或服务端未开启','The connection is disconnected or the server is not opened'];
var main_Disconnect = ['断开连接','Disconnect'];


//JS/camera/camera.js
var Cam_Bar = ['条码识别中...','Bar code recognition in progress...'];
var Cam_Doc = ['高拍仪设备信息为空','Device information is empty'];
var Cam_Stream = ['视频流base64为空','Base64 video stream is empty'];
var Cam_Res = ['条码识别结果:','Results:'];
var Cam_Cont = ['内容: ','Content: '];
var Cam_Type = ['类型: ','Type: '];
var Cam_Rec = ['没有识别到条码。','No barcode recognized.'];

//  JS/camera/idFace.js
var IDC_Low = ['浏览器版本太低！请使用Chrome、Firefox、IE10+浏览器！','Browser version is too low! Please use chrome, Firefox, ie10 + browser!'];
var IDC_Dis = ['未连接websocket服务器，请确保已运行服务端！!!!','Websocket server is not connected, please make sure the server is running!!!   '];
var IDC_Name = ['姓名: ','Name: '];
var IDC_Folk = ['  民族: ','  folk: '];
var IDC_Gender = ['  性别: ','  gender: '];
var IDC_Bir = ['  出生日期: ','  Birthday: '];
var IDC_Id = ['  身份证号: ','  ID: '];
var IDC_Addr = ['  地址: ','  Address: '];
var IDC_Iss = ['  发证: ','  issue: '];
var IDC_Life = ['  有效期: ','  useful life: '];
var IDC_fp = ['  指纹: ','  Fingerprint: '];
var IDC_Com = ['开始比对','Start compare'];
var IDC_PPF = ['请先准备好比对的头像文件。','Please prepare the right Face image file first.'];
var IDC_PSF = ['请先选择人像摄像头','Please select portrait camera first'];
var IDC_SComp = ['停止比对','Stop compare'];
var IDC_FCP = ['人脸比对通过, 相似度 ','Face comparison passed, similarity '];
var IDC_Vivo = ['活体检测通过, ','In vivo test passed, '];
var IDC_Point = ['分','points'];


// JS/readCard/readCard.js
var RCard_Fun = ['方法名称为','The func name:']


// JS/scanner/scanner.js
var SCAN_null = ['扫描纸张base64为空','Scan paper for Base64 is empty'];
var SCAN_finish = ['扫描已完成','Scan finish'];

//   JS/index.js
var INDEX_NotS = ['目前还未开发,请先试用CamerSDK和ScannerSDK,谢谢!','It hasn\'t been developed yet. Please try camersdk and scannersdk first. Thank you!']