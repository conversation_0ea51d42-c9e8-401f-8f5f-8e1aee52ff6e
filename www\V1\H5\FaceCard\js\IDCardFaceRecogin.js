
//模式
var g_obj =  "";

//图片保存的目录
let g_savePicDir = "c:/tmp/";

function isIE() {
    if (!!window.ActiveXObject || "ActiveXObject" in window)
        return true;
    else
        return false;
}


//加载页面完成
window.onload = function () {

    IDCardFaceRecogin.InitSDKAndInitUI(g_obj);

}
//页面卸载
window.onunload = function () {

    IDCardFaceRecogin.UInitCamera(g_obj);
}

//初始化并配置UI

let IDCardFaceRecogin = {

    first:1,
    devIndex : 1,
    comparePath:"",
    compareBase64:"",
    previewDevIndex:0,
    realPicPath:"",
    encodeBase64Tag:0,

    //初始化UI
    InitSDKAndInitUI:function (obj){

        if (this.first == 1)
        {
           StartWebSocket();
            this.first = 0;
        }

    },



//反初始化
    UInitCamera:function (obj) {
        unload();
    },

    //比对方式发生了变化
    SelectCompareType:function()
    {

    },
    //采集
    Capture:function()
    {
        let selectIndex =document.getElementById("compareType").selectedIndex;

        this.ShowComparePic("",true);

        this.ShowCompareReslut("比对分数：",false);
        this.compareBase64 = "";
        this.comparePath = "";

        if (selectIndex == 0)
        {
            //采集身份证
            this.CaptureIDCard();
        }else if(selectIndex == 1){
            //文件
            this.CaptureFile();
        }

      //  this.compareBase64 = g_obj.EncodeBase64(0, this.comparePath);

    },

    //开始比对
    StartCompare:function()
    {
        var ret = -1;
        this.ShowRealPic("",true);
        if (this.compareBase64.length > 10)
        {
            this.realPicPath = g_savePicDir + "realPic.bmp";

            var jsonObj = {FuncName:'camMatchFaceByBase64Video',argument:{personFace:this.compareBase64,videoFilePath:this.realPicPath,ldelayTime:0}};
            sendWsMessage(jsonObj);


        }else
        {
            this.ShowCompareReslut("比对失败！",true);
        }


    },


    //采集文件
    CaptureFile:function()
    {

        if (!isIE()) {

         // alert("无法获取本地路径，请使用IE演示");
         
		 this.comparePath = prompt("请输入路径：");
		 
		 
        }else{
			
			 var inputObj=document.createElement('input')
			inputObj.setAttribute('id','_ef');
			inputObj.setAttribute('type','file');
			inputObj.setAttribute("style",'visibility:hidden');
			document.body.appendChild(inputObj);
			inputObj.click();
			this.comparePath = inputObj.value;
		}

        this.EncodeBase64(this.comparePath,0);

    




    },

    //转换Base64
    EncodeBase64:function(filePath,tag)
    {
        this.encodeBase64Tag = tag;
        var jsonObj = {FuncName:'FileEncodeBase64',argument:{filePath:filePath}};
        sendWsMessage(jsonObj);


    },
    //采集身份证
    CaptureIDCard:function()
    {

        ReadIDCard();

    },

    //显示比对结果
    ShowCompareReslut:function(info,error)
    {
        let labe = document.getElementById("compareReslut");
        labe.value = info;
        labe.style.background = error ? "red" : "white";
    },

    //显示比对图片
    ShowComparePic:function(info,isBase64)
    {
        let im = document.getElementById("comparePicImg");
        if(im == null)
        {
            return;
        }
        if (info == "")
        {
            im.style.background = "white";
           // return;
        }

        if(isBase64)
        {
            im.src = "data:image/png;base64," + info;
        }else
        {
            im.src = "file://" + info;
        }

    },
    //显示现场图片
    ShowRealPic:function(info,isBase64)
    {
        let im = document.getElementById("realPicImg");
        if(im == null)
        {
            return;
        }
        if (info == "")
        {
            im.style.background = "white";
           // return;
        }
        if(isBase64)
        {
            im.src = "data:image/png;base64," + info;
        }else
        {
            im.src = "file:///" + info;
        }
    },



}

