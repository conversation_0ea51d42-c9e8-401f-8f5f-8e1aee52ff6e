import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询检测项列表
export function listitem(query) {
    return request({
      url: '/inspectitem/doQuery',
      method: 'post',
      data: query
    })
}

  // 查询检测项详细
  export function getitem(inspectitemId) {
    return request({
      url: '/inspectitem/' + parseStrEmpty(inspectitemId),
      method: 'get'
    })
}

// 新增检测项
export function additem(data) {
    return request({
      url: '/inspectitem/doAdd',
      method: 'post',
      data: data
    })
}

// 修改检测项
export function updateitem(data) {
    return request({
      url: '/inspectitem/doEdit',
      method: 'post',
      data: data
    })
}


// 删除检测项
export function delitem(inspectitemId) {
    return request({
      url: '/inspectitem/doDelete/' + parseStrEmpty(inspectitemId),
      method: 'post'
    })
  }

// 删除检测项
export function copyitems(ids) {
  return request({
    url: '/inspectitem/copyitem?ids=' + parseStrEmpty(ids),
    method: 'get'
  })
}

