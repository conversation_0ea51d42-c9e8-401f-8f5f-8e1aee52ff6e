var _headBase64 = null;
var _devInfos = null;
var _devNum1 = -1, _devNum2 = -1;
var _myWs = new MzWsConnect(location.port, onTxtMessage, onError, ClearOutputInfo);
var _timer = 0;

/**
 * 处理服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.func, jsonObj.errorMsg);
        displayOutputInfo(3, jsonObj.func + "    " + jsonObj.errorMsg)
        if (jsonObj.func == "FaceMatch") {
            var btn = document.getElementById("btnStartStop");
            btn.innerText = Lang.IDC.startCompare;
        }
    } else {
        switch (jsonObj.func) {
            case "GetCameraVideoBuff":
                DisplayVideo(jsonObj);
                break;
            case "GetCameraInfo":
                DisplayDevInfo(jsonObj.devInfo);
                break;
            case "CameraCaptureBase64":
                DisplayCaptureFile(jsonObj);
                break;
            case "FaceMatch":
                DisplayMatchResult(jsonObj);
                break;
            case "ReadIDCardInfo":
                DisplayIDCardInfo(jsonObj);
                break;
            case "Notify":
                // 收到通知
                if (jsonObj.event == "OnUsbKeyPress") {
                    // 收到USB按键按下的通知
                    // {func:"Notify","event":"OnUsbKeyPress","reqId":1636099207839.0,"result":0,"time":"1636099214"}
                    // DisplayUsbKeyPress(jsonObj);
                } else if (jsonObj.event == "OnDeviceChanged") {
                    // 设备有变化,格式如下:
                    // {func:"Notify","event":"OnDeviceChanged","devName":"\\\\?\\USB#VID_0C45&PID_6366...",func:"OnDeviceChanged","result":0,"type":1}
                    // type: 1设备插入; 2设备已拔出
                    Wait1secGetCameraInfo();
                }
                break;
        }
    }
}

// 处理连接错误
function onError(e, msg) {
    displayOutputInfo(3, msg);
}

function DisplayDevInfo(devInfos) {
    _devInfos = devInfos;
    // {"devInfo":[{"camMode":99,"devName":"Integrated Camera","funcType":1,"id":0,
    //  "resolutionList":["1280x720","960x540","848x480","640x480","640x360","424x240","352x288","320x240","320x180"]}],
    //  func:"GetCameraInfo","reqId":"111","result":0}
    var selDev1 = document.getElementById("selDev1");
    var selDev2 = document.getElementById("selDev2");
    var selectedIndex1 = selDev1.selectedIndex, selectedIndex2 = selDev2.selectedIndex;
    selDev1.options.length = 0;
    selDev2.options.length = 0;

    for (var i = 0; i < _devInfos.length; i++) {
        var dev = _devInfos[i];
        if (dev.funcType == 1) {
            // 在左边的设备下拉列表中，只显示文档摄像头(funcType=1)；
            selDev1.options.add(new Option(dev.devName, dev.id));
        } else if (dev.funcType == 2) {
            // 在右边的设备下拉列表中，只显示人像摄像头(funcType=2)
            selDev2.options.add(new Option(dev.devName, dev.id));
        }
    }

    if (selectedIndex1 >= selDev1.options.length || selectedIndex1 < 0) {
        selectedIndex1 = 0;
    }
    selDev1.selectedIndex = selectedIndex1;

    if (selectedIndex2 >= selDev2.options.length || selectedIndex2 < 0) {
        selectedIndex2 = 0;
    }
    selDev2.selectedIndex = selectedIndex2;
}

function Wait1secGetCameraInfo() {
    if (_timer) {
        window.clearTimeout(_timer);
    }
    _timer = window.setTimeout(GetCameraInfo,1000);
}

// 获取设备信息
function GetCameraInfo() {
    _myWs.SendJson({func:"GetCameraInfo", reqId: new Date().getTime()});
}

function openCamera1() {
    var obj = document.getElementById("selDev1");
    var index = obj.selectedIndex;
    _devNum1 = obj.options[index].value;
    var resNum = 0;
    _headBase64 = null;

    var reqId = new Date().getTime();
    _myWs.SendJson({func:"OpenCamera", reqId:reqId, devNum:_devNum1, mediaNum:0, resolutionNum:resNum, fps:5});

    // 设置单图裁切(1)
    reqId++;
    _myWs.SendJson({func:"SetCameraImageInfo", reqId:reqId, devNum:_devNum1, cropType:1});

    // 获取摄像头视频
    reqId++;
    _myWs.SendJson({func:"GetCameraVideoBuff", reqId:reqId, devNum:_devNum1 ,enable:true});
}

function openCamera2() {
    var obj = document.getElementById("selDev2");
    var index = obj.selectedIndex;
    var devNum2 = obj.options[index].value;
    if (devNum2 != _devNum2) {
        var reqId = new Date().getTime();

        // 先关闭之前打开的人像摄像头
        if (_devNum2 >= 0) {
            _myWs.SendJson({func:"CloseCamera", reqId:reqId, devNum:_devNum2});
            reqId++;
        }
        _devNum2 = devNum2;

        // 找到640x480的分辨率，人像比对一般使用640x480即可
        var resNum = 0;
        for (var i = 0; i < _devInfos.length; i++) {
            if (_devInfos[i].id == _devNum2) {
                var firstList = _devInfos[i].mediaTypes;
                var resList = firstList[0].resolutions;
                for (var j = 0; j < resList.length; j++) {
                    if (resList[j] == '640x480') {
                        resNum = j;
                        break;
                    }
                }
                break;
            }
        }

        // 打开新选择的人像摄像头
        _myWs.SendJson({func:"OpenCamera", reqId:reqId, devNum:_devNum2, mediaNum:0, resolutionNum:resNum, fps:5});
        reqId++;

        // 打开人像摄像头的视频预览
        _myWs.SendJson({func:"GetCameraVideoBuff",reqId:reqId, devNum:_devNum2, enable:true});
    }
}

var _reqIdaceMatch = 0;

function DisplayVideo(v) {
    if (v.result == 0) {
        var imgId = (v.devNum == _devNum1) ? 'imgHead' : 'imgVideo';
        var obj = document.getElementById(imgId);
        obj.className = "stdImg";
        obj.src = "data:image/jpg;base64," + v.imgBase64Str;
    }
}

function DisplayCaptureFile(v) {
    if (v.devNum == _devNum1) {
        // 拍照成功后立即关闭摄像头
        var reqId = new Date().getTime();
        _myWs.SendJson({func:"CloseCamera", reqId:reqId, devNum: _devNum1});
        _devNum1 = -1;

        // 显示拍照的照片
        _headBase64 = v.imgBase64Str;
        var obj = document.getElementById('imgHead');
        obj.className = "stdImg";
        obj.src = "data:image/jpg;base64," + _headBase64;
    }
}

// 显示从身份证读取到的信息
function DisplayIDCardInfo(v) {
    var ci = v.cardInfo;
    // {"address":"长沙市...","birth":"19900101","endDate":"20301031","fingerFeature":"","folk":"汉","gender":"男","id":"430.....",
    //  "imageBase64":"...","imagePath":"F:\\...\\1628740497.bmp","issue":"长沙市公安局","mime":"image/bmp","name":"XXX","startDate":"20101030"}
    if (ci) {
        _headBase64 = ci.imageBase64;
        var obj = document.getElementById('imgHead');
        obj.className = "";
        obj.src = "data:image/jpg;base64," + _headBase64;

        var s = Lang.IDC.name + ci.name + "  " + Lang.IDC.folk + ci.folk + "  " + Lang.IDC.gender + ci.gender + "  " + Lang.IDC.birthday + ci.birth + "\r\n";
        s += Lang.IDC.id + ci.id + "\r\n";
        s += Lang.IDC.address + ci.address + "\r\n";
        s += Lang.IDC.issue + ci.issue + "\r\n";
        s += Lang.IDC.usefulLife + ci.startDate + " - " + ci.endDate + "\r\n";
        if(ci.fingerFeature && ci.fingerFeature[0].fpPath && ci.fingerFeature[0].type) {
            s += Lang.IDC.fpPath + ci.fingerFeature[0].fpPath + "\r\n";
            s += Lang.IDC.fingerFeature + ci.fingerFeature[0].type;
        }
        document.getElementById('idCardInfo').textContent = s;
    }
}

// 读取二代身份证的信息及人像
function readIdCard() {
    if (_devNum1 >= 0) {
        // 关闭文档摄像头
        var reqId = new Date().getTime();
        _myWs.SendJson({func:"CloseCamera", reqId: new Date().getTime(), devNum:_devNum1});
        _devNum1 = -1;
    }
    _myWs.SendJson({func:"ReadIDCardInfo", reqId: new Date().getTime(), idType:0});
}

function capterImg() {
    // 不指定文件名，会自动生成文件名  "imagePath":"pic1.jpg"
    _myWs.SendJson({func:"CameraCaptureBase64", reqId:new Date().getTime(), devNum: _devNum1});
}

function openHeadImage() {
    imgFile.click();
}

function chooseImgFile() {
    var file = imgFile.files[0];
    //readFile(file);
    var reader = new FileReader();
    reader.readAsDataURL(file);//转化成base64数据类型
    reader.onload = function (e) {
        document.getElementById("imgHead").src = this.result;
        _headBase64 = this.result.substring(23);
    }
}

function startStopMatch() {
    var btn = document.getElementById("btnStartStop");
    btn.disabled = true;
    if (btn.innerText == Lang.IDC.startCompare) {
        if (_headBase64 == null) {
            displayOutputInfo(3, Lang.IDC.PPF);
            return;
        }

        if (_devNum2 == -1) {
            var obj = document.getElementById("selDev2");
            var index = obj.selectedIndex;
            _devNum2 = obj.options[index].value;
        }

        if (_devNum2 == -1) {
            displayOutputInfo(3, Lang.IDC.PSF);
            return;
        }

        var reqId = new Date().getTime();
        var cmd = {func:"FaceMatch", reqId:reqId, image2DevNum:_devNum2, sendMatchedImage: (chkSendMatchedImg.checked) ? 1 : 0};
        if (chkPlayWav.checked) {
            // playWav属性是指要播放的声音文件的路径，可以是相对路径（相对于 SDK安装目录的media子目录），也可指定服务端的绝对路径
            cmd.playWav = "recognitionPass.wav";
        }
        cmd.liveFace = chkLiveCheck.checked ? 1 : 0;
        cmd.matchedSimilarity = inputSimilarity.value;
        cmd.runSecond = inputRunSeconds.value;
        cmd.stopMode = selStopMode.selectedIndex;

        // 指定人像的 image1Path 与 image1Base64 指定一个即可
        cmd.image1Base64 = _headBase64;
        _myWs.SendJson(cmd);
        btn.innerText = Lang.IDC.stopCompare;
    } else {
        // 停止之前的人像对比
        _myWs.SendJson({func:"FaceMatch",reqId:new Date().getTime(), stop:1});
        btn.innerText = Lang.IDC.startCompare;
    }
    btn.disabled = false;
}

function DisplayMatchResult(v) {
    // {func:"FaceMatch","reqId":"223388","checkResult":1,"isLiveFace":0,"liveFace":1,"liveScore":0,
    //  "matchedSimilarity":80,"result":0,"similarity":17}

    // FaceMatchr返回Json的属性说明:
    // checkResult: 人脸检测的结果,从低到高按位表示:是否检测到彩色人脸,彩色人脸特征比对是否通过,是否检测到红外人脸,是否活体
    // similarity:  检测到的人脸相似度
    // matchedSimilarity: 参数中要求的人脸相似度,相似度>=这个值时停止(优先级第二), 0表示未设置
    // isLiveFace:  是活体
    // liveFace: 参数中是否开启活体检测
    // liveScore: 活体检测得到的分数
    // imageBase64: 在检测匹配时,在回应的json中包含匹配的视频图像

    // FaceMatch在发消息启动或停止时，会有立即的回应消息，这些回应消息中不包含检测结果，下面只处理包含检测结果的消息
    if (typeof (v.checkResult) !== "undefined") {
        // 显示详细的比对结果
        document.getElementById("state1").style.color = (v.checkResult & 1) ? "green" : "darkgrey";
        document.getElementById("state5").style.color = (v.checkResult & 2) ? "green" : "red";
        document.getElementById("state2").style.color = "black";
        document.getElementById("state3").style.color = (v.checkResult & 4) ? "green" : "darkgrey";
        document.getElementById("state4").style.color = "black";
        document.getElementById("state6").style.color = (v.checkResult & 8) ? "green" : "red";

        // 显示相似度与分值
        document.getElementById("state5").innerText = (v.checkResult & 2) ? Lang.IDC.Passed : Lang.IDC.unPassed;
        document.getElementById("state7").innerText = v.similarity + "%";

        document.getElementById("state6").innerText = (v.checkResult & 8) ? Lang.IDC.Passed : Lang.IDC.unPassed;
        document.getElementById("state8").innerText = v.liveScore + Lang.IDC.score;

        if (v.imageBase64) {
            document.getElementById('imgMatched').src = "data:image/jpg;base64," + v.imageBase64;
        }
    }

    if (v.stoped) {
        document.getElementById("btnStartStop").innerText = Lang.IDC.startCompare;
    }
}


function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["#b5daf1", "#9de09d", "#f3a960"];
        $('#outInfo').css("display", "").css("background-color", ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function ClearOutputInfo() {
    displayOutputInfo(0);
}

//加载页面完成，等500毫秒后 初始化OCXobject
window.onload = function () {
    GetCameraInfo();
    //测试用
    //var v = {checkResult:0x07};
    //DisplayMatchResult(v);
};

$(window).unload(function(){
    // 关闭页面之前,关闭连接
    if (_myWs.connected) {
        _myWs.Close();
    }
});
