<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>身份证及指纹 演示</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../JS/common/html5shiv.min.js"></script>
    <script src="../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>身份证及指纹 演示</h1>
        <p>这个页面演示了从身份证读卡器读取身份证信息以及指纹信息。从指纹设备读取指纹，并进行指纹比对。</p>
    </div>

    <div class="col-md-7">
        <label>身份证模拟照片:</label>
        <select id="simulateImageType" style="width:150px;margin-right:40px">
            <option value="0">不生成</option>
            <option value="1">生成不合并</option>
            <option value="2">生成左右合并</option>
            <option value="3">生成上下合并</option>
            <option value="4">生成上下合并有空隙</option>
        </select>
        <button type="button" class="btn btn-primary" id="btnAuto" onclick="readIdCard()">读取二代身份证</button>
        <br>
        <div class="col-md-10" style="padding-left:0; padding-right:0;">
            <label>身份证信息:</label><br>
            <textarea id="idCardInfo" readonly="true" style="width:99%; height:210px;"></textarea>
        </div>
        <div class="col-md-2">
            <label>身份证照片:</label><br>
            <img id="imgHead" style="width:102px; height:126px;"/>
        </div>
    </div>

    <div class="col-md-5" style="text-align:center; padding-left: 40px;">
        <div class="col-md-6" style="text-align:center;">
            <button type="button" class="btn btn-primary" id="btnReadFP1" onclick="ReadFP(1)">采集指纹1</button>
            <br>
            <label>采集指纹1:</label><br>
            <img id="imgFP1" style="width:102px; height:126px;">
        </div>
        <div class="col-md-6" style="text-align:center;">
            <button type="button" class="btn btn-primary" id="btnReadFP2" onclick="ReadFP(2)">采集指纹2</button>
            <br>
            <label>采集指纹2:</label><br>
            <img id="imgFP2" style="width:102px; height:126px;">
        </div>

        <div class="col-md-12" style="text-align:center; line-height:24px;">
            <br><span id="fpIdCard">没有读取身份证</span><br>
            <select id="fp1" style="width:105px;" onchange="onChangeFP()">
                <option value="1" disabled>采集指纹1</option>
                <option value="2" disabled>采集指纹2</option>
                <option value="3" disabled>身份证指纹1</option>
                <option value="4" disabled>身份证指纹2</option>
            </select> 与
            <select id="fp2" style="width:105px;" onchange="onChangeFP()">
                <option value="1" disabled>采集指纹1</option>
                <option value="2" disabled>采集指纹2</option>
                <option value="3" disabled>身份证指纹1</option>
                <option value="4" disabled>身份证指纹2</option>
            </select>
            <button type="button" class="btn btn-primary" id="btnMatch" disabled onclick="MatchFP()">指纹比对</button>
        </div>
    </div>
    <div class="col-md-12">
        <label>生成的身份证模拟照片:</label><br>
        <img id="imgIdCard1" style="display: none; width:400px;margin:10px;">
        <img id="imgIdCard2" style="display: none; width:400px;margin:10px;">
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<audio id="audioCapture">
    <source src="../../audio/Capture.wav" type="audio/wav">
</audio>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">拍照文件查看</h4>
            </div>
            <div class="modal-body">
                <img id="imgView" src="" style="max-width:860px">
<!--                <img id="imgView2" src="" style="max-width:860px">-->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="vertical-line" style="display: none"></div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../langs/zhCN.js"></script>
<script src="../../JS/MzWsConnect.js"></script>
<script src="../../JS/otherDev/IdFinger.js"></script>
</body>
</html>