<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="标题" prop="title">
        <el-input
          v-model="queryParams.title"
          placeholder="请输入标题"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="分类" prop="cmsclassId">
        <el-select v-model="queryParams.cmsclassId" placeholder="分类" clearable>
          <el-option
            v-for="dict in classlist"
            :key="dict.cmsclassId"
            :label="dict.className"
            :value="dict.cmsclassId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
        >删除</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="cmsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="标题图" align="center" prop="titleImg" width="100">
         <template slot-scope="scope">
            <image-preview :src="scope.row.titleImg"/>
         </template>
      </el-table-column> -->
      <el-table-column
        label="标题"
        align="center"
        prop="title"
        :show-overflow-tooltip="true"
      />
      <!-- <el-table-column
        label="副标题"
        align="center"
        prop="subtitle"
        :show-overflow-tooltip="true"
      /> -->
      <el-table-column label="分类" align="center" prop="cmsclass.className" width="100">
      </el-table-column>
      <el-table-column label="状态" align="center" prop="state" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">正式</span>
          <span v-if="scope.row.state==2">草稿</span>
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="type" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.type==1">新闻</span>
          <span v-if="scope.row.type==2">公告</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="creationtime" width="100">
        <template slot-scope="scope">
         <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
         
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"

          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改公告对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="780px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题" prop="title">
              <el-input v-model="form.title" placeholder="请输入标题" />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="副标题" prop="subtitle">
              <el-input v-model="form.subtitle" placeholder="请输入副标题" />
            </el-form-item>
          </el-col> -->
          <el-col :span="12">
            <el-form-item label="分类" prop="cmsclassId">
              <el-select v-model="form.cmsclassId" placeholder="请选择分类" clearable>
                <el-option
                  v-for="dict in classlist"
                  :key="dict.cmsclassId"
                  :label="dict.className"
                  :value="dict.cmsclassId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择类型" clearable>
                 <el-option
                  v-for="dict in typeoptions"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.state">
                <el-radio
                  v-for="dict in options"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <!-- <el-col :span="24">
            <el-form-item label="标题图" prop="title">
              <image-upload v-model="form.titleImg" :limit="1"  :fileType="['png', 'jpg', 'jpeg']"/>
            </el-form-item>
          </el-col> -->
          <el-col :span="24">
            <el-form-item label="内容">
              <editor v-model="form.content" :min-height="192"/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listClass,listCms,addCms,updateCms,getCms,delCms } from "@/api/system/cms";

export default {
  name: "Notice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 公告表格数据
      cmsList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeTitle: undefined,
        createBy: undefined,
        status: undefined
      },
      //资讯分类
      classlist:[],
      //类型
      typeoptions: [{
          value: 1,
          label: '新闻'
        }, {
          value: 2,
          label: '公告'
      }],
        //状态
      options: [{
          value: 1,
          label: '正式'
        }, {
          value: 2,
          label: '草稿'
      }],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        title: [
          { required: true, message: "标题不能为空", trigger: "blur" }
        ],
        cmsclassId: [
          { required: true, message: "分类不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getclasslist();
    this.getList();
  },
  methods: {
    /** 查询公告列表 */
    getList() {
      this.loading = true;
      this.queryParams.orderBy="creationtime desc"
      listCms(this.queryParams).then(response => {
        this.cmsList = response.data;
        this.total = response.totalCount;
        this.loading = false;
      });
    },
    getclasslist(){
        let queryclass = {};
        queryclass.state=1;
        listClass(queryclass).then(response => {
          this.classlist = response.data
        })
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        cmsId: undefined,
        cmsclassId: undefined,
        title: undefined,
        corp: undefined,
        subtitle: undefined,
        excerpt: undefined,
        titleImg: undefined,
        type: undefined,
        state: undefined,
        content: undefined,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cmsId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加资讯";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const cmsId = row.cmsId || this.ids
      getCms(cmsId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改资讯";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.cmsId != undefined) {
            updateCms(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCms(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const cmsId = row.cmsId || this.ids
      this.$modal.confirm('是否确认删除资讯？').then(function() {
        return delCms(cmsId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    }
  }
};
</script>
