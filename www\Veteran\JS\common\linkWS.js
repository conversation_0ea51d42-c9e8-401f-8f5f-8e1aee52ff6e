/**
 * 高拍仪公用js
 */
var url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
var websocket;
var connected = false;

/**
 * 初始化webSocket连接
 * @param callback
 * @param value
 * @constructor
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        websocket = new WebSocket(url);
    } else if (window.WebSocket) {
        websocket = new WebSocket(url);
    } else if ('MozWebSocket' in window) {
        websocket = new MozWebSocket(url);
    } else {
        alert(Lang.WS.lowVer);
    }

    websocket.onopen = function() {
        connected = true;
        callback(value);
    }
    websocket.onclose = function (e) {
        connected = false;
    }
    websocket.onmessage = function (e) {
        onMessage(e);
    }
    websocket.onerror = function (e) {
        alert(Lang.WS.disConn)
    };
}


/**
 * 接收服务器消息
 * @param e
 */
function onMessage(e) {
    // var jsonObj = eval('(' + e.data + ')');
    var jsonObj = JSON.parse(e.data.replace(/[\r\n]/g,""));
    //通用回调处理函数 jsonObj 遵循文档返回数据格式要求
    produceMessage(jsonObj);
}

/**
 * 向服务器发送信息的共享方法
 * @param jsonStr
 */
function sendMessage(jsonStr) {
    connected ? websocket.send(jsonStr) : alert(Lang.WS.disConn)
}


/******************* 以下为高拍仪函数 *************************/

function GetCameraInfo(reqId){
    var data = JSON.stringify({'func': 'GetCameraInfo', 'reqId':reqId});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function SetCameraInfo(reqId, devNum, mediaNum, resolutionNum) {
    var data = JSON.stringify({'func': 'SetCameraInfo', 'reqId':reqId, 'devNum':devNum, 'mediaNum':mediaNum,'resolutionNum': resolutionNum});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function GetCameraVideoBuff(reqId, devNum, enable){
    var data = JSON.stringify({'func': 'GetCameraVideoBuff', 'reqId':reqId, 'devNum':devNum, 'enable':enable});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function OpenCamera(reqId, devNum){
    var data = JSON.stringify({'func': 'OpenCamera', 'reqId':reqId, 'devNum':devNum});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function CloseCamera(reqId, devNum){
    var data = JSON.stringify({'func': 'CloseCamera', 'reqId':reqId, 'devNum':devNum});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}

function SetCameraImageInfo(reqId, devNum, cropType, imageType, fillBorderType, removalForeign, rotate){
     var data = JSON.stringify({'func': 'SetCameraImageInfo', 'reqId':reqId, 'devNum':devNum, 'cropType':cropType, 'imageType':imageType, 
							  'fillBorderType':fillBorderType,'removalForeign': removalForeign,'rotate': rotate});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function CameraCapture(reqId, devNum, imgPath) {
    var data = JSON.stringify({'func': 'CameraCapture', 'reqId':reqId, 'devNum':devNum, 'imgPath': imgPath});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function CameraCaptureBase(reqId, devNum){
    var data = JSON.stringify({'func': 'CameraCaptureBase64', 'reqId':reqId, 'devNum':devNum});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function CameraRecogBarCode(reqId, devNum){
    var data = JSON.stringify({'func': 'RecogBarCode', 'reqId':reqId, 'devNum':devNum});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}

/******************* 以下为扫描仪函数 *************************/
function GetScannerName(reqId)
{
    var data = JSON.stringify({'func': 'GetScannerName', 'reqId':reqId});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function OpenScanner(reqId, devNum)
{
    var data = JSON.stringify({'func': 'OpenScanner', 'reqId':reqId, 'devIndex': devNum});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function GetScannerInfo(reqId, devNum)
{
    var data = JSON.stringify({'func': 'GetScannerInfo', 'reqId':reqId, 'devIndex': devNum});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}

function CloseScanner(reqId)
{
    var data = JSON.stringify({'func': 'CloseScanner', 'reqId':reqId});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function SetScannerInfo(reqId, scanSource, scanMode, scanResolution, scanPaperSize){
     var data = JSON.stringify({'func': 'SetScannerInfo', 'reqId':reqId, 'scanSource':scanSource, 'scanMode':scanMode, 'scanResolution':scanResolution,
								'scanPaperSize': scanPaperSize});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function SetScannerImageInfo(reqId, cropType, imageType, fillBorderType, removalForeign, rotate){
     var data = JSON.stringify({'func': 'SetScannerImageInfo', 'reqId':reqId, 'cropType':cropType, 'imageType':imageType, 'fillBorderType':fillBorderType,
								'removalForeign': removalForeign, 'rotate': rotate});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}
function Scan(reqId, devNum, scanSource, scanMode, scanResolution, scanPaperSize)
{
    var data = JSON.stringify({'func': 'Scan', 'reqId':reqId, 'devIndex': devNum, 'scanSource':scanSource, 'scanMode':scanMode, 
                                'scanResolution':scanResolution,'scanPaperSize': scanPaperSize});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}


/******************* 以下为读卡器函数 *************************/
function ReadIDCardInfo(reqId, idType)
{
    var data = JSON.stringify({'func': 'ReadIDCardInfo', 'reqId':reqId, 'idType':idType});
    connected ? sendMessage(data) : ConnectServer(sendMessage, data)
}


