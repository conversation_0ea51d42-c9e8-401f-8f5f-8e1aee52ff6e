﻿


//开启webSocket
function StartWebSocket(){
	 var url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
	if ('WebSocket' in window){
            ws = new WebSocket(url);
    } else if('MozWebSocket' in window){
        ws = new MozWebSocket(url);
    } else {
		alert("浏览器版本过低，请升级您的浏览器。\r\n浏览器要求：IE10+/Chrome14+/FireFox7+/Opera11+");
	}
   
	ws.onopen = function() {
      m_isConnectWS = true;
	  unload();
	  sendInitMsg();//初始化
	  m_closed = false;
   };
	
	
   ws.onmessage = function (evt) {
   	if(typeof(evt.data)=="string"){
   		
   		var str = evt.data;
   		
   		if(str.length <= 0){
   			
   			return;
   		}
		
		if(str.indexOf("FileEncodeBase64") >=0){
			
			var strs= new Array(); 
			strs=str.split(m_splitTag);
			var bas = strs[2];
			
			if(m_isShowFileFront)
			{
				CombinePic.src = "data:image/png;base64,"+bas;
				m_isShowFileFront = false;
				showIDCardImageBack();
			}
			else if(m_isShowFileBack)
			{
				
				CombinePic1.src = "data:image/png;base64,"+bas;
				m_isShowFileBack = false;
			}
			else if(m_isIDCardHeadBs)
			{
				imgPreview.src = "data:image/png;base64,"+bas;;
				m_isIDCardHeadBs = false;
			}
			
		
		
		
			return;
		}
		else if(str.indexOf("GetFingerprint") >=0)
		{
			//采集指纹
			
			
			var strs= new Array(); 
			strs=str.split(m_splitTag);
			var bas = strs[2];
			if(bas.length < 10)
			{
				alert("采集失败");
				return;
			}
			var picBs = strs[3];
			showFingerPic(bas,picBs);
			return;
		}
		else if(str.indexOf("idcardrfidGetOneFingerprintMsg") >=0)
		{
			//身份证指纹
			
			var strs= new Array(); 
			strs=str.split(m_splitTag);
			
			
			var bas = strs[2];
			if(bas.length < 10)
			{
				alert("无指纹数据");
				return;
			}
			var ntype = strs[3];
			showIDcardFingerInfo(bas,ntype);
		}
	
		
		if(str.indexOf(m_splitTag)>=0){
		
		}else{
			//处理其他请求
			console.log(str);
			handleJsonStrMessage(str);
		}
		
		
   		
   	}
  
 	};
	
   ws.onclose = function()
   { 
      m_isConnectWS = false;

		StartWebSocket();
   };
	
}

function sendWsMessage(jsonObj){
	var jsonStr = JSON.stringify(jsonObj);
	ws.send(jsonStr);
}

function handleJsonStrMessage(str){
	
	
	var jsonOBJ = JSON.parse(str);
	var name = jsonOBJ.FuncName;
	var re = jsonOBJ.result;
	//初始化
	if( name == "camInitCameraLib"){
		
		if (re == 0){
			
	
			
		}else{
			//alert("初始化失败" + re);
		}
			
	}
	
	//二代证
	else if(name == "idcardrfidReadIDCard"){
		
		idMsg.value = re;
		m_isIDCardHeadBs = true;
		var jsonObj = {FuncName:'FileEncodeBase64',argument:{filePath:m_IDcardHeaderPic}};
		sendWsMessage(jsonObj);
		
	}
	else if (name == "idcardrfidReadIDCardEx")
	{
		
		idMsg.value = re;
	
		var strs=re.split("|");
		imgPreview.src = "data:image/png;base64,"+strs[9];
	}
	else if(name == "idcardrfidGetIDCardImage")
	{
		if(re == 0)
		{
			showIDCardImageFront();
		}
	}
	//社保卡
	else if(name == "ReadSBKCaard"){
		
		alert(re);
	}
	
	//读取的银行卡
	else if(name == "ReadBankCard"){
		
		alert(re);
	}
	//读取的磁条卡
	else if(name == "ReadMagneticCard"){
		alert(re);
	}
	//初始化指纹仪
	else if(name == "fingerprintInit"){
		if (re == "0"){
			
			alert("初始化指纹仪成功");
			
		}else{
			
			alert("初始化指纹仪失败");
		}
		
	}
	//反初始化指纹仪
	else if(name == "fingerprintUnInit"){
		if (re == "0"){
			
			alert("反初始化指纹仪成功");
			
		}else{
			
			alert("反初始化指纹仪失败");
		}
	}
	
	//比对指纹
	else if(name == "ContrastFingerprint"){
		
		if(re == "-100"){
			
			alert("请先采集指纹");
			
		}else if(re == "0"){
			
			alert("指纹匹配成功");
		}else {
			
			alert("匹配失败，错误码"+re);
		}
		
	}
	else if(name == "ContrastFingerprintBase64")
	{
		if(re == "-100"){
			
			alert("请先采集指纹");
			
		}else if(re == "0"){
			
			alert("指纹匹配成功");
		}else {
			
			alert("匹配失败，错误码"+re);
		}
		
		
	}
	
	
	
	
}

	
	

