var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "Sign":
            DisplaySignInfo(jsonObj);
            break;
        case "DecryptAES":
            DisplayDecryptStocks(jsonObj);
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function DisplaySignInfo(v) {
    // 返回的json格式：{"signImageBase64:"字节码png","func":"Sign","reqId":"2132131","result":0}
    if (v.result == 0) {
        document.getElementById("pic").src = "data:" + v.mime + ";base64," + v.signImageBase64;
        displayOutputInfo(2, "获取签名成功");

        if (v.strokes) {
            // AES解密签名轨迹数据
            SendJson({func:"DecryptAES", src: v.strokes, reqId:new Date().getTime()});
        }
    } else {
        displayOutputInfo(3, "获取签名失败：" + v.errorMsg);
    }
}

function DisplayDecryptStocks(v) {
    if (v.result == 0) {
        var obj = document.getElementById("strokes");
        var jsonObj = JSON.parse(v.dest);
        obj.innerText = formatJson(jsonObj, cbOnFormatError);
        obj.parentElement.parentElement.style.display = "";
        displayOutputInfo(2, "AES解密签名轨迹成功");

        if (v.jsonFile) {
            // 发消息给终端设备,回显签名信息
            var ary = v.jsonFile.split("\\"), n = ary.length;
            var url = "http://" + v.serverIP + ":" + location.port + "/Ext-Dev/echo/ReadSign.html?client=" + ary[n-2] +"&file=" + ary[n-1];
            SendJson({func:"Navigation", to:1, url:url, reqId: new Date().getTime()});

            // for debug
            window.open(url);
        }
    } else {
        displayOutputInfo(3, "AES解密签名轨迹失败：" + v.errorMsg);
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function StartSign() {
    document.getElementById("btnUpfile").click();
}

function SendSignCmd(imgData) {
    // 隐藏签名轨迹数据
    document.getElementById("strokes").parentElement.parentElement.style.display = "none";
    // 提示信息显示
    displayOutputInfo(1, "签名读取中...... 请在多功能终端上签名并点击“确认”按钮");
     // 启动签名
    var pos = imgData.indexOf(";base64,");
    var mime = imgData.substring(5, pos);
    SendJson({func:"Sign", to:1, saveJson:1, bgImgBase64:imgData.substring(pos + 8), mime:mime, reqId:new Date().getTime()});
}

function cbOnFormatError(errInfo) {
    displayOutputInfo(3, "格式化Json时错误: " + errInfo);
}

$("#btnUpfile").change(function () {
    // 本地显示图片
    var file = $('#btnUpfile')[0].files[0];
    readFile(file);
});


function readFile(file) {
    if (!/image\/\w+/.test(file.type)) {
        alert("请确保文件为图像类型");
        return false;
    }//判断是否图片，在移动端由于浏览器对调用file类型处理不同，虽然加了accept = 'image/*'，但是还要再次判断
    var reader = new FileReader();
    reader.readAsDataURL(file);//转化成base64数据类型
    reader.onload = function (e) {
        drawToCanvas(this.result);
        SendSignCmd(this.result)
    }
}

function drawToCanvas(imgData) {
    var cvs = document.querySelector('#cvs2');
    cvs.style.display = "";
    var ctx = cvs.getContext('2d');
    var img = new Image;
    img.src = imgData;
    img.onload = function () {//必须onload之后再画
        ctx.drawImage(img, 0, 0, 960, 720);
    };
}
