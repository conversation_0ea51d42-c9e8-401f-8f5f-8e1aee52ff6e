var _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
var _ws;
var _connected = false;
var _devInfos = null;
var _devNum = -1;
var _setRemovalForeign = false; //是否设置了‘去除手指’

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        alert(Lang.WS.disConn);
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.func, jsonObj.errorMsg);
        alert(jsonObj.func + "\r\n" + jsonObj.errorMsg);
    } else {
        switch (jsonObj.func) {
            case "GetCameraVideoBuff":
                DisplayVideo(jsonObj);
                break;
            case "GetCameraInfo":
                DisplayDevInfo(jsonObj.devInfo);
                break;
            case "CameraCaptureBook":
                DisplayCaptureBook(jsonObj);
                break;
            case "FileToBase64":
                DisplayFileView(jsonObj);
                break;
            case "MergeFile":
                DisplayMergeFile(jsonObj);
                break;
            case "Notify":
                // 收到通知
                if (jsonObj.event == "OnUsbKeyPress") {
                    // 收到USB按键按下的通知
                    // {"func":"Notify","event":"OnUsbKeyPress","reqId":1636099207839.0,"result":0,"time":"1636099214"}
                    // DisplayUsbKeyPress(jsonObj);
                } else if (jsonObj.event == "OnDeviceChanged") {
                    // 设备有变化,格式如下:
                    // {"func":"Notify","event":"OnDeviceChanged","devName":"\\\\?\\USB#VID_0C45&PID_6366...","func":"OnDeviceChanged","result":0,"type":1}
                    // type: 1设备插入; 2设备已拔出
                    GetCameraInfo();
                }
                break;
            case "FileOCR":
                DisplayConvertToFile(jsonObj);
                break;
            case "SetCameraInfo":
            case "SetStillPinInfo":
                message.run("切换分辨率成功","success",1500);
                break;
            default:
                console.log(msg);
                break;
        }
    }
}

// 获取设备信息
function GetCameraInfo() {
    SendTxt('{"func":"GetCameraInfo","reqId":"' + new Date().getTime() + '"}');
}

//显示合并成功的PDF文件
function DisplayMergeFile(jsonObj) {
    if (jsonObj.filePath) {
        alert(Lang.MSG.mergePdfOk + "\r\n" + jsonObj.filePath);
        if (location.protocol.substr(0,4) == 'http') {
            var pos = jsonObj.filePath.lastIndexOf('\\');
            pos = jsonObj.filePath.lastIndexOf('\\', pos - 1);
            var url = location.origin + '/tmp/' + jsonObj.filePath.substr(pos + 1).replace('\\','/');
            window.open(url);
        }
    }
}

//显示识别成功的文件
function DisplayConvertToFile(jsonObj) {
    if (jsonObj.filePath) {
        alert(Lang.MSG.mergePdfOk + "\r\n" + jsonObj.filePath);
        //var pos = jsonObj.filePath.lastIndexOf('\\');
        //pos = jsonObj.filePath.lastIndexOf('\\', pos - 1);
        //var url = location.origin + '/tmp/' + jsonObj.filePath.substr(pos + 1).replace('\\','/');
        //window.open(url);
    }
}


var _lastVideoLeft = 0, _lastVideoTop = 0, _lastVideoW = 0, _lastVideoH = 0;
var _w = 0, _h = 0, _wp = 0, _hp = 0;
function DisplayVideo(v) {
    if (_w != v.width || _h != v.height) {
        //设置视频显示的宽高（高度控制为484px）
        _hp = 484, _wp = _hp * v.width / v.height, _w = v.width, _h = v.height;
        $("#pic").css('width', _wp + 'px').css('height', _hp + 'px');
    }

    //显示视频
    document.getElementById("pic").src = "data:" + v.mime + ";base64," + v.imgBase64Str;

    // 显示视频中分线,用于对齐书本中缝
    var p = $("#pic").offset();
    if (p.left != _lastVideoLeft || p.top != _lastVideoTop || _lastVideoW != _wp || _lastVideoH != _hp) {
        $("#vertical-line").css('top', p.top + 'px').css('left', (p.left + _wp/2 - 2) + 'px').css('height', _hp + 'px').css('display','');
        _lastVideoLeft = p.left, _lastVideoTop = p.top, _lastVideoW = _wp, _lastVideoH = _hp;
    }
}


function DisplayDevInfo(devInfos) {
    _devInfos = devInfos;
    if (_devInfos.length == 0) {
        alert(Lang.cam.doc);
        return;
    }
    //初始化设备信息
    displayCamera();
    displayMediaType();
    displayResolution();

    switchCameraInfo_Fun();
}


//初始化信息
$(function () {
    //获取摄像头设备列表
    GetCameraInfo();
})

//设备信息的onchange事件
function switchCameraInfo_Fun() {
    var reqId = new Date().getTime();
    if (_devNum >= 0) {
        //关闭之前的摄像头
        SendTxt('{"func":"CloseCamera","reqId":"' + reqId + '","devNum":' + _devNum + '}');
        reqId++;
    }

    //刷新显示 视频格式 下拉列表
    displayMediaType();

    //刷新显示 分辨率下拉列表
    displayResolution();

    //开启摄像头
    _devNum = $("#cameraInfo").val() == null ? 0 : parseInt($("#cameraInfo").val());
    var cmd = '{"func":"OpenCamera","reqId":"' + reqId + '","devNum":' + _devNum + ',"mediaNum":0,"resolutionNum":0,"fps":5}';
    SendTxt(cmd);
    reqId++;

    // 获取预览视频
    cmd = '{"func":"GetCameraVideoBuff","reqId":"' + reqId + '","devNum":' + _devNum + ',"enable":"true"}';
    SendTxt(cmd);

    _setRemovalForeign = false;
}

// 刷新显示 设备名称 下拉列表
function displayCamera() {
    var obj = document.getElementById("cameraInfo");
    var lastSelectedIndex = obj.options.selectedIndex;
    obj.options.length = 0;
    for (var i = 0; i < _devInfos.length; i++) {
        obj.options.add(new Option(_devInfos[i].devName, _devInfos[i].id));
    }
    if (lastSelectedIndex >= 0 && lastSelectedIndex < _devInfos.length) {
        obj.options.selectedIndex = lastSelectedIndex;
    }
}

// 刷新显示 视频格式 下拉列表
function displayMediaType() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var mediaTypes = _devInfos[index1].mediaTypes;
    var obj = document.getElementById("mediaType");
    obj.options.length = 0;
    if(_devInfos[index1].stillPin != undefined) {
        obj.options.add(new Option(_devInfos[index1].stillPin.mediaType, 0));
    }
    else {
        for (var i = 0; i < mediaTypes.length; ++i) {
            obj.options.add(new Option(mediaTypes[i].mediaType, i));
        }
    }
}

// 刷新显示 分辨率下拉列表
function displayResolution() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var index2 = document.getElementById("mediaType").options.selectedIndex;
    var obj = document.getElementById("resolution");
    var resolutions = _devInfos[index1].mediaTypes[index2].resolutions;
    //清空resolution:select数据
    obj.options.length = 0;

    if(_devInfos[index1].stillPin != undefined) {
        _isStillPin = true;
        var SPresolutions = _devInfos[index1].stillPin.resolutions;
        for (var i = 0; i < SPresolutions.length; i++) {
            obj.options.add(new Option(SPresolutions[i], i));
        }
    }
    else{
        _isStillPin = false;
        var resolutions = _devInfos[index1].mediaTypes[index2].resolutions;
        for (var i = 0; i < resolutions.length; i++) {
            obj.options.add(new Option(resolutions[i], i));
        }
    }
}

//设置设备信息
function setCameraInfo_Fun() {
    //分辨率的值
    var resolutionNum = $("#resolution").val() == null ? 0 : parseInt($("#resolution").val());
    var mediaNum = $("#mediaType").val() == null ? 0 : parseInt($("#mediaType").val());
    var reqId = new Date().getTime();
    var cmd;
    if(_isStillPin){
        cmd = JSON.stringify({'func': 'SetStillPinInfo', 'reqId':reqId, 'devNum':_devNum, 'resolutionNum': resolutionNum});
    }
    else{
        cmd = JSON.stringify({'func': 'SetCameraInfo', 'reqId':reqId, 'devNum':_devNum, 'mediaNum':mediaNum,'resolutionNum': resolutionNum});
    }
    SendTxt(cmd);
}

function DisplayCaptureBook(json) {
    if (json.pageL) {
        displayFile(json.pageL);
        DisplayImg(json.mime, json.pageLBase64Str);
        document.getElementById("imgL").src = "data:" + json.mime + ";base64," + json.pageLBase64Str;
    } else {
        document.getElementById("imgL").src = "";
    }
    if (json.pageR) {
        displayFile(json.pageR);
        DisplayImg(json.mime, json.pageRBase64Str);
        document.getElementById("imgR").src = "data:" + json.mime + ";base64," + json.pageRBase64Str;
    } else {
        document.getElementById("imgR").src = "";
    }
    if (json.pageL || json.pageR) {
        audioCapture.play();
    }
}

var _path = null;
var _th = '<tr><th>文件名</th><th>操作</th></tr>';
var _tdOpration = '<td><a onclick="vf()">查看</a> <a onclick="df()">删除</a></td>';
function displayFile(file) {
    var pos = file.lastIndexOf('\\');
    if (_path == null) {
        _path = file.substring(0, pos);
    }
    var html = '<tr><td>' + file.substring(pos + 1) + '</td>' + _tdOpration + '</tr>';
    $("#tbFile").append(html);
}

//添加图片项
function DisplayImg(mime, imgBase64Str) {
    var h = '<div><img width="300" style="padding:5px;" src="' + 'data:' + mime + ';base64,' + imgBase64Str + '"></div>';
    $("#bottom_div").prepend(h);
}

// 清空图片展示列表
function clearImageFile() {
    $("#image_data").empty();
    $("#tbFile").empty().append(_th);
}

function removeFinger() {
    if (!_setRemovalForeign) {
        // 设置去除手指  RemovalForeign 异物去除　０-无　1-去除手指　２－去除装订孔
        var reqId = new Date().getTime() - 1;
        var cmd = JSON.stringify({func: 'SetCameraImageInfo', reqId:reqId, devNum:_devNum, removalForeign: 1});
        SendTxt(cmd);
        _setRemovalForeign = true;
    }
}

// 拍书
function captureBook() {
    removeFinger();
    var reqId = new Date().getTime();
    // 拍书时mode支持 base64 与 path 两种方式, path方式时回应消息中包含照片文件的路径, 两种方式也可同时使用
    var cmd = JSON.stringify({func: 'CameraCaptureBook', reqId:reqId, devNum:_devNum, mode:'base64,path'});
    SendTxt(cmd);
}

function  convertToFile_Fun() {
    var reqId = new Date().getTime();
    var fileArray = new Array();
    var trs = $('#tbFile tr');
    for (i = 1; i < trs.length; i++) {
        fileArray.push(_path + '\\' + trs[i].children[0].innerText);
    }
    var cmd = JSON.stringify({func: 'FileOCR', reqId: reqId, language: "Simplified chinese+English", indexExtName: 0, DetectTextOrientation: false, imagePath: fileArray});
    SendTxt(cmd);
}
function mergePDF() {
    var reqId = new Date().getTime();
    var fileArray = new Array();
    var trs = $('#tbFile tr');
    for (i = 1; i < trs.length; i++) {
        fileArray.push(_path + '\\' + trs[i].children[0].innerText);
    }
    // fileType: 0 PDF; 1-TIFF(暂不支持)
    var cmd = JSON.stringify({func: 'MergeFile',reqId:reqId, fileType:0,imagePath:fileArray,mode:'path;base64'});
    SendTxt(cmd);
}

function vf() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    var file = _path + '\\' + $(target).parent().prev().text();

    // 把文件转为base64,用于显示查看
    var reqId = new Date().getTime();
    var cmd = JSON.stringify({func:'FileToBase64', reqId:reqId, filePath:file});
    SendTxt(cmd);
}

function DisplayFileView(v) {
    var img = document.getElementById("imgView");
    img.src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    $("#divView").css('display', '').css('width', '').css('height', '');
    if (img.height > $("#divView").innerHeight()) {
        $("#divView").css('height', img.height + 'px');
    }
    if (img.width > $("#divView").innerWidth()) {
        $("#divView").css('width', img.width + 'px');
    }
}

// 删除拍照文件
function df() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    $(target).parent().parent().remove();
}

// 关闭图片查看
function closeView() {
    $("#divView").css('display', 'none');
    document.getElementById("imgView").src = "";
}

function onSpeedChange() {
    var v = $("#speed").val();
    $("#speedNum").text(v);
    if (document.getElementById("autoCapture").checked) {
        sendAutoCaptureCmd(true, v);
    }
}

function OnAutoCaptureChange() {
    removeFinger();
    var v = $("#speed").val();
    sendAutoCaptureCmd(document.getElementById("autoCapture").checked, v);
}

// 发送自动连拍的命令
function sendAutoCaptureCmd(enable, param) {
    var reqId = new Date().getTime();
    var cmd = JSON.stringify({func: 'CameraAutoCapture', reqId: reqId, "devNum": _devNum, enable: enable,
        isBook: true, detectionType: 0, param: param, mode: "base64,path"});
    // 自动连拍CameraAutoCapture命令在启动（enable为true）时是个一问多答的命令，除此命令的回应命令（func为CameraAutoCapture）外，
    // 在自动拍成功后，还会有func为CameraCapture或CameraCaptureBook的回应消息。
    // 属性isBook为true,则连拍时会按拍书来处理，回应消息是CameraCaptureBook
    SendTxt(cmd);
}