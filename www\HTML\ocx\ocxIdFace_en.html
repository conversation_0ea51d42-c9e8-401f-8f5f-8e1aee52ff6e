<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset=utf-8 http-equiv="Content-Language" content="en"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title>CamSDK Client For OCX</title>
    <script src="../../langs/en.js"></script>
    <style>
        body {
            line-height: 32px;
            padding: 2px 5px 2px 5px;
            background-color: #f5f5f5;
            text-align: left;
            font-size: 18px;
        }

        label, input, button, select, option, textarea {
            font-size: 18px;
        }

        input[type=checkbox] {
            width: 20px;
            height: 20px;
        }
    </style>
</head>
<body>
<OBJECT ID="OCXobject" name="OCXobject" width="0" height="0" CLASSID="CLSID:DA096059-7D27-42E9-BDED-5BF4317CAC77">
</OBJECT>
<h1>Compare face and ID card For OCX</h1>
This demo demonstrates the scene of witness comparison. It is best to have an ID card reader and a binocular camera, and at least one high racket with a vice head is required. Binocular camera is much higher than ordinary camera in the accuracy of living body detection。
<table cellspacing="8" cellpadding="8" border="1">
    <tr>
        <td align="center" valign="middle" width="36%"><b>Select or obtain standard face images</b></td>
        <td align="center" valign="middle" width="32%"><b>Video and comparison parameters</b></td>
        <td align="center" valign="middle" width="32%"><b>Comparison results</b></td>
    </tr>
    <tr>
        <td>
            Direct
            <button onclick="openHeadImage()">Open face image file</button>
            <input id="imgFile" type="file" onchange="chooseImgFile()" accept="image/jpg, image/jpeg"
                   style="display: none">
            <br>
            or have Doc camera<select id="selDev1" style="width:200px;"></select>
            <button onclick="openCamera1()">Open</button>
            then
            <button onclick="capterImg()">Shot</button>
            <br>
            or have ID card reader
            <button onclick="readIdCard()">Read ID card</button>
            <br>ID information: <br><textarea id="idCardInfo" readonly="true" style="width:98%;height:110px;"></textarea>
        </td>
        <td>
            <input type="checkbox" checked id="chkLiveCheck"><label for="chkLiveCheck">Turn on live detection</label><br>
            Matching minimum similarity(0-100)<input id="inputSimilarity" type="number" value="80" style="width:50px"/><br>
            Stop by：
            <select id="selStopMode">
                <option value="0">Manual</option>
                <option value="1" selected>Auto</option>
                <option value="2">Timing</option>
            </select><br>
            Stop after<input id="inputRunSeconds" type="number" value="60" style="width:50px"/>seconds<br>
            <input type="checkbox" checked id="chkSendMatchedImg"><label
                for="chkSendMatchedImg">The returned result contains the matching image</label><br>
            <input type="checkbox" checked id="chkPlayWav"><label for="chkPlayWav">Play sound when matching is successful</label><br>
            Face Camera:<select id="selDev2" style="width:200px;"></select>
            <button onclick="openCamera2()">Open the face camera</button>
            <button onclick="startStopMatch()" id="btnStartStop">Start Compare</button>
        </td>
        <td align="center" valign="middle" style="line-height: 56px; font-size: 32px;">
            <span id="state1" style="color:darkgrey">Face detected in color video</span><br>
            <span id="state2" style="color:darkgrey">Face comparison passed</span><br>
            <span id="state3" style="color:darkgrey">Face detected in infrared video</span><br>
            <span id="state4" style="color:darkgrey">Vivo detection passed</span><br><br>
        </td>
    </tr>
    <tr>
        <td>
            Video or face image:<br>
            <img id="imgHead" style="width:384px;height:288px;"/>
        </td>
        <td>
            Face video:<br>
            <img id="imgVideo" style="width:384px;height:288px;"/>
        </td>
        <td>
            Matching face:<br>
            <img id="imgMatched" style="width:384px;height:288px;"/>
        </td>
    </tr>
</table>

<script type="text/javascript" src="js/common/json2.js"></script>
<script type="text/javascript">
    var _bOpened = false;
    var _headBase64 = null;
    var _devInfos = null;
    var _devNum1 = -1, _devNum2 = -1;
</script>
<script language="JavaScript" for="OCXobject" event="OnRecvMsg(eventType, msg)" type="text/javascript">
    //console.log(eventType, msg);
    switch (eventType) {
        case 2:
            // EVENT_TYPE_ON_FAIL
            if (confirm("Connect failed, The server may not be opened.\r\nDo you want to retry the connection in 3 seconds？")) {
                window.setTimeout(initOCXobject, 3000);
            }
            break;

        case 3:
            // EVENT_TYPE_ON_OPEN
            _bOpened = true;
            // document.getElementById('connState').innerText = "已连接";
            // 获取设备信息
            GetCameraInfo();
            break;

        case 4:
            // EVENT_TYPE_ON_MESSAGE
            var jsonObj = JSON.parse(msg);
            if (jsonObj.errorMsg) {
                console.log(jsonObj.func, jsonObj.errorMsg);
                alert(jsonObj.func + "\r\n" + jsonObj.errorMsg);
            } else {
                switch (jsonObj.func) {
                    case "GetCameraVideoBuff":
                        DisplayVideo(jsonObj);
                        break;
                    case "GetCameraInfo":
                        DisplayDevInfo(jsonObj.devInfo);
                        break;
                    case "CameraCaptureBase64":
                        DisplayCaptureFile(jsonObj);
                        break;
                    case "FaceMatch":
                        DisplayMatchResult(jsonObj);
                        break;
                    case "ReadIDCardInfo":
                        DisplayIDCardInfo(jsonObj);
                        break;
                    case "Notify":
                        // 收到通知
                        if (jsonObj.event == "OnUsbKeyPress") {
                            // 收到USB按键按下的通知
                            // {"func":"Notify","event":"OnUsbKeyPress","reqId":1636099207839.0,"result":0,"time":"1636099214"}
                            // DisplayUsbKeyPress(jsonObj);
                        } else if (jsonObj.event == "OnDeviceChanged") {
                            // 设备有变化,格式如下:
                            // {"func":"Notify","event":"OnDeviceChanged","devName":"\\\\?\\USB#VID_0C45&PID_6366...","func":"OnDeviceChanged","result":0,"type":1}
                            // type: 1设备插入; 2设备已拔出
                            GetCameraInfo();
                        }
                        break;
                }
            }
            break;
        case 5:
            // EVENT_TYPE_ON_CLOSE
            _bOpened = false;
            //document.getElementById('connState').innerText = "连接关闭";
            break;
    }
</script>

<script type="text/javascript">
    function isIE() {
        if (!!window.ActiveXObject || "ActiveXObject" in window)
            return true;
        else
            return false;
    }

    function initOCXobject() {
        var ret = OCXobject.Init();
        console.log("OCXobject.Init() = ", ret);
        if (ret == 1795) {
            alert("websocket connect failed");
            OCXobject.Uninit();
            window.setTimeout(initOCXobject, 500);
        }
    }

    function DisplayDevInfo(devInfos) {
        _devInfos = devInfos;

        // {"devInfo":[{"camMode":99,"devName":"Integrated Camera","funcType":1,"id":0,
        //  "resolutionList":["1280x720","960x540","848x480","640x480","640x360","424x240","352x288","320x240","320x180"]}],
        //  "func":"GetCameraInfo","reqId":"111","result":0}

        var selDev1 = document.getElementById("selDev1");
        var selDev2 = document.getElementById("selDev2");
        var selectedIndex1 = selDev1.selectedIndex, selectedIndex2 = selDev2.selectedIndex;
        selDev1.options.length = 0;
        selDev2.options.length = 0;

        for (var i = 0; i < _devInfos.length; i++) {
            var dev = _devInfos[i];
            if (dev.funcType == 1) {
                // 在左边的设备下拉列表中，只显示文档摄像头(funcType=1)；
                selDev1.options.add(new Option(dev.devName, dev.id));
            } else if (dev.funcType == 2) {
                // 在右边的设备下拉列表中，只显示人像摄像头(funcType=2)
                selDev2.options.add(new Option(dev.devName, dev.id));
            }
        }

        if (selectedIndex1 >= selDev1.options.length || selectedIndex1 < 0) {
            selectedIndex1 = 0;
        }
        selDev1.selectedIndex = selectedIndex1;

        if (selectedIndex2 >= selDev2.options.length || selectedIndex2 < 0) {
            selectedIndex2 = 0;
        }
        selDev2.selectedIndex = selectedIndex2;
    }

    // 获取设备信息
    function GetCameraInfo() {
        var cmd = '{"func":"GetCameraInfo","reqId":"' + new Date().getTime() + '"}';
        OCXobject.SendTxt(cmd);
    }

    function openCamera1() {
        var obj = document.getElementById("selDev1");
        var index = obj.selectedIndex;
        _devNum1 = obj.options[index].value;
        var resNum = 0;
        _headBase64 = null;

        var reqId = new Date().getTime();
        var cmd = '{"func":"OpenCamera","reqId":"' + reqId + '","devNum":' + _devNum1 +
            ',"mediaNum":0,"resolutionNum":' + resNum + ',"fps":5}'
        OCXobject.SendTxt(cmd);

        // 设置单图裁切(1)
        reqId++;
        cmd = '{"func":"SetCameraImageInfo","reqId":"' + reqId + '","devNum":' + _devNum1 + ',"cropType":1}';
        OCXobject.SendTxt(cmd);

        // 获取摄像头视频
        reqId++;
        cmd = '{"func":"GetCameraVideoBuff","reqId":"' + reqId + '","devNum":' + _devNum1 + ',"enable":"true"}';
        OCXobject.SendTxt(cmd);
    }

    function openCamera2() {
        var obj = document.getElementById("selDev2");
        var index = obj.selectedIndex;
        var devNum2 = obj.options[index].value;
        if (devNum2 != _devNum2) {
            var reqId = new Date().getTime();

            // 先关闭之前打开的人像摄像头
            if (_devNum2 >= 0) {
                var cmd = '{"func":"CloseCamera","reqId":"' + reqId + '","devNum":' + _devNum2 + '}'
                OCXobject.SendTxt(cmd);
                reqId++;
            }
            _devNum2 = devNum2

            // 找到640x480的分辨率，人像比结一般使用640x480即可
            var resNum = 0;
            for (var i = 0; i < _devInfos.length; i++) {
                if (_devInfos[i].id == _devNum2) {
                    var firstList = _devInfos[i].mediaTypes;
                    var resList = firstList[0].resolutions;
                    for (var j = 0; j < resList.length; j++) {
                        if (resList[j] == '640x480') {
                            resNum = j;
                            break;
                        }
                    }
                    break;
                }
            }

            // 打开新选择的人像摄像头
            var cmd = '{"func":"OpenCamera","reqId":"' + reqId + '","devNum":' + _devNum2 +
                ',"mediaNum":0,"resolutionNum":' + resNum + ',"fps":5}'
            OCXobject.SendTxt(cmd);
            reqId++;

            // 打开人像摄像头的视频预览
            cmd = '{"func":"GetCameraVideoBuff","reqId":"' + reqId + '","devNum":' + _devNum2 + ',"enable":"true"}';
            OCXobject.SendTxt(cmd);
        }
    }

    var _reqIdaceMatch = 0;

    function DisplayVideo(v) {
        if (v.result == 0) {
            var imgId = (v.devNum == _devNum1) ? 'imgHead' : 'imgVideo';
            document.getElementById(imgId).src = "data:image/jpg;base64," + v.imgBase64Str;
        }
    }

    function DisplayCaptureFile(v) {
        if (v.devNum == _devNum1) {
            // 拍照成功后立即关闭摄像头
            var reqId = new Date().getTime();
            var cmd = '{"func":"CloseCamera","reqId":"' + reqId + '","devNum":' + _devNum1 + '}';
            OCXobject.SendTxt(cmd);
            _devNum1 = -1;

            // 显示拍照的照片
            _headBase64 = v.imgBase64Str;
            document.getElementById('imgHead').src = "data:image/jpg;base64," + _headBase64;
        }
    }

    // 显示从身份证读取到的信息
    function DisplayIDCardInfo(v) {
        var ci = v.cardInfo;
        // {"address":"长沙市...","birth":"19900101","endDate":"20301031","fingerFeature":"","folk":"汉","gender":"男","id":"430.....",
        //  "imageBase64":"...","imagePath":"F:\\...\\1628740497.bmp","issue":"长沙市公安局","mime":"image/bmp","name":"XXX","startDate":"20101030"}
        if (ci) {
            _headBase64 = ci.imageBase64;
            document.getElementById('imgHead').src = "data:image/jpg;base64," + _headBase64;

            var s = "Name: " + ci.name + "  Folk: " + ci.folk + "  Gender" + ci.gender + "  Birthday: " + ci.birth + "\r\n";
            s += "ID: " + ci.id + "\r\n";
            s += "Address: " + ci.address + "\r\n";
            s += "Issue: " + ci.issue + "\r\n"
            s += "Useful life: " + ci.startDate + " - " + ci.endDate;
            document.getElementById('idCardInfo').textContent = s;
        }
    }

    // 读取二代身份证的信息及人像
    function readIdCard() {
        if (_devNum1 >= 0) {
            // 关闭文档摄像头
            var reqId = new Date().getTime();
            var cmd = '{"func":"CloseCamera","reqId":"' + new Date().getTime() + '","devNum":' + _devNum1 + '}';
            OCXobject.SendTxt(cmd);
            _devNum1 = -1;
        }
        var cmd = '{"func":"ReadIDCardInfo","reqId":"' + new Date().getTime() + '","idType":0}';
        OCXobject.SendTxt(cmd);
    }

    function capterImg() {
        // 不指定文件名，会自动生成文件名  "imagePath":"pic1.jpg"
        var reqId = new Date().getTime();
        var cmd = '{"func":"CameraCaptureBase64","reqId":"' + reqId + '","devNum":' + _devNum1 + '}';
        OCXobject.SendTxt(cmd);
    }

    function openHeadImage() {
        imgFile.click();
    }

    function chooseImgFile() {
        var file = imgFile.files[0];
        //readFile(file);
        var reader = new FileReader();
        reader.readAsDataURL(file);//转化成base64数据类型
        reader.onload = function (e) {
            document.getElementById("imgHead").src = this.result;
            _headBase64 = this.result.substring(23);
        }
    }

    function startStopMatch() {
        var btn = document.getElementById("btnStartStop");
        btn.disabled = true;
        if (btn.innerText == 'Start Compare') {
            if (_headBase64 == null) {
                alert("Please prepare the right face file。");
                return;
            }

            if (_devNum2 == -1) {
                var obj = document.getElementById("selDev2");
                var index = obj.selectedIndex;
                _devNum2 = obj.options[index].value;
            }

            if (_devNum2 == -1) {
                alert("Please choose the face file first");
                return;
            }

            var reqId = new Date().getTime();
            var cmd = '{"func":"FaceMatch","reqId":"' + reqId + '","image2DevNum":' + _devNum2;
            cmd += ',"sendMatchedImage":' + (chkSendMatchedImg.checked ? 1 : 0);
            if (chkPlayWav.checked) {
                // playWav属性是指要播放的声音文件的路径，可以是相对路径（相对于 SDK安装目录的media子目录），也可指定服务端的绝对路径
                cmd += ',"playWav":"recognitionPass.wav"';
            }
            cmd += ',"liveFace":' + (chkLiveCheck.checked ? 1 : 0);
            cmd += ',"matchedSimilarity":' + inputSimilarity.value;
            cmd += ',"runSecond":' + inputRunSeconds.value;
            cmd += ',"stopMode":' + selStopMode.selectedIndex;

            // 指定人像的 image1Path 与 image1Base64 指定一个即可
            cmd += ',"image1Base64":"'
            cmd += _headBase64;
            cmd += '"}';
            OCXobject.SendTxt(cmd);
            btn.innerText = 'Stop compare';
        } else {
            // 停止之前的人像对比
            var reqId = new Date().getTime();
            var cmd = '{"func":"FaceMatch","reqId":"' + reqId + '","stop":1}';
            OCXobject.SendTxt(cmd);
            btn.innerText = 'Start compare';
        }
        btn.disabled = false;
    }

    function DisplayMatchResult(v) {
        // {"func":"FaceMatch","reqId":"223388","checkResult":1,"isLiveFace":0,"liveFace":1,"liveScore":0,
        //  "matchedSimilarity":80,"result":0,"similarity":17}

        // FaceMatchr返回Json的属性说明:
        // checkResult: 人脸检测的结果,从低到高按位表示:是否检测到彩色人脸,彩色人脸特征比对是否通过,是否检测到红外人脸,是否活体
        // similarity:  检测到的人脸相似度
        // matchedSimilarity: 参数中要求的人脸相似度,相似度>=这个值时停止(优先级第二), 0表示未设置
        // isLiveFace:  是活体
        // liveFace: 参数中是否开启活体检测
        // liveScore: 活体检测得到的分数
        // imageBase64: 在检测匹配时,在回应的json中包含匹配的视频图像

        // FaceMatch在发消息启动或停止时，会有立即的回应消息，这些回应消息中不包含检测结果，下面只处理包含检测结果的消息
        if (typeof (v.checkResult) !== "undefined") {
            // 显示详细的比对结果
            document.getElementById("state1").style.color = (v.checkResult & 1) ? "green" : "darkgrey";
            document.getElementById("state2").style.color = (v.checkResult & 2) ? "green" : "darkgrey";
            document.getElementById("state3").style.color = (v.checkResult & 4) ? "green" : "darkgrey";
            document.getElementById("state4").style.color = (v.checkResult & 8) ? "green" : "darkgrey";

            // 显示相似度与分值
            document.getElementById("state2").innerText = "Face comparison passed, Similarity " + v.similarity + "%"
            document.getElementById("state4").innerText = "In vivo test passed, " + v.liveScore + "points"

            if (v.imageBase64) {
                document.getElementById('imgMatched').src = "data:image/jpg;base64," + v.imageBase64;
            }
        }

        if (v.stoped) {
            document.getElementById("btnStartStop").innerText = 'Start compare';
        }
    }

    //加载页面完成，等500毫秒后 初始化OCXobject
    window.onload = function () {
        if (isIE()) {
            window.setTimeout(initOCXobject, 500);
        } else {
            alert("Web use OCX, please open it in IE browser.");
        }
    };

    //页面卸载,反初始化OCXobject
    window.onunload = function () {
        if (isIE()) {
            OCXobject.Uninit();
            console.log("OCXobject.Uninit()");
        }
    };
</script>
</body>
</html>
