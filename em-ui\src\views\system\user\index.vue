<template>
  <div class="app-container">
    <el-row :gutter="20">
      <splitpanes :horizontal="this.$store.getters.device === 'mobile'" class="default-theme">
        <!--部门数据-->
        <pane size="16">
          <el-col>
            <div class="head-container">
              <el-input v-model="deptName" placeholder="请输入部门名称" clearable size="small" prefix-icon="el-icon-search" style="margin-bottom: 20px" />
            </div>
            <div class="head-container">
              <el-tree :data="deptOptions" :props="defaultProps" :expand-on-click-node="false" :filter-node-method="filterNode" ref="tree" node-key="id" default-expand-all highlight-current @node-click="handleNodeClick" />
            </div>
          </el-col>
        </pane>
        <!--用户数据-->
        <pane size="84">
          <el-col>
            <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
              <el-form-item label="姓名" prop="workerName">
                <el-input v-model="queryParams.workerName" placeholder="请输入用户姓名" clearable style="width: 240px" @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="工号" prop="workerCode">
                <el-input v-model="queryParams.workerCode" placeholder="请输入用户工号" clearable style="width: 240px" @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="手机号码" prop="iphone">
                <el-input v-model="queryParams.iphone" placeholder="请输入手机号码" clearable style="width: 240px" @keyup.enter.native="handleQuery" />
              </el-form-item>
              <el-form-item label="状态" prop="state">
                <el-select v-model="queryParams.state" placeholder="用户状态" clearable style="width: 240px">
                  <el-option v-for="dict in options" :key="dict.value" :label="dict.label" :value="dict.value" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
                <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
              <el-col :span="1.5">
                <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" >新增</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate" >修改</el-button>
              </el-col>
              <el-col :span="1.5">
                <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" >删除</el-button>
              </el-col>
             
              <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
              <el-table-column type="selection" width="50" align="center" />
              <el-table-column label="工号" align="center" key="workerCode" prop="workerCode" v-if="columns[0].visible" />
              <el-table-column label="姓名" align="center" key="workerName" prop="workerName" v-if="columns[1].visible" :show-overflow-tooltip="true" />
              <el-table-column label="所属公司" align="center" key="corp"  v-if="columns[2].visible" :show-overflow-tooltip="true" >
                 <template slot-scope="scope">
                  <span v-if="scope.row.corp=='js'">江苏</span>
                  <span v-if="scope.row.corp=='tz'">泰州</span>
                  <span v-if="scope.row.corp=='dy'">东营</span>
                  <span v-if="scope.row.corp=='jn'">济宁</span>
                  <span v-if="scope.row.corp=='tg'">泰国</span>
                </template>
              </el-table-column>
              <el-table-column label="微信ID" align="center" key="wxId" prop="wxId" v-if="columns[3].visible" :show-overflow-tooltip="true" />
              <el-table-column label="手机号码" align="center" key="iphone" prop="iphone" v-if="columns[4].visible" width="120" />
              <el-table-column label="状态" align="center" key="state" v-if="columns[5].visible">
                <template slot-scope="scope">
                  <el-switch v-model="scope.row.state" :active-value=1 :inactive-value=2 @change="handleStatusChange(scope.row)"></el-switch>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" align="center" prop="creationtime" v-if="columns[6].visible" width="160">
                <template slot-scope="scope">
                  <span>{{ parseTime(scope.row.creationtime) }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
                <template slot-scope="scope" >
                  <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" >修改</el-button>
                  <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" >删除</el-button>
                  <el-dropdown size="mini" @command="(command) => handleCommand(command, scope.row)" >
                    <el-button size="mini" type="text" icon="el-icon-d-arrow-right">更多</el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item command="handleOrg" icon="el-icon-connection" >所属部门</el-dropdown-item>
                      <el-dropdown-item command="handleCredent" icon="el-icon-circle-check" >资质</el-dropdown-item>
                       <el-dropdown-item command="handleCareer" icon="el-icon-s-check" >职业体检</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />
          </el-col>
        </pane>
      </splitpanes>
    </el-row>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="姓名" prop="workerName">
              <el-input v-model="form.workerName" placeholder="请输入姓名" maxlength="30" />
            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="工号" prop="workerCode">
              <el-input v-model="form.workerCode" placeholder="请输入工号" maxlength="10" />
            </el-form-item>
          </el-col>
          
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="手机号" prop="iphone">
              <el-input v-model="form.iphone" placeholder="请输入手机号" maxlength="11" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="微信ID">
               <el-input v-model="form.wxId" placeholder="请输入微信ID" maxlength="30" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属公司" prop="corp">
                <el-select v-model="form.corp" placeholder="请选择公司">
                <el-option
                  v-for="item in corps"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.state">
                <el-radio v-for="dict in options" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" :visible.sync="upload.open" width="400px" append-to-body>
      <el-upload ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>

     <!-- 职业体检对话框 -->
    <el-dialog :title="title" :visible.sync="careeropen" width="600px" append-to-body>
      <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAddCareer"
        >新增</el-button>
      </el-col>
    </el-row>
      <el-table v-loading="loading" :data="careerdata" >
      <el-table-column label="年度" align="center" prop="year" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-download"
            @click="handleDown(scope.row)"
          >下载</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDeleteCareer(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
      </el-table>
        <pagination
      v-show="total>0"
      :total="total"
      :page.sync="careerParams.pageNum"
      :limit.sync="careerParams.pageSize"
      @pagination="getCareer"
    />
    </el-dialog>

      <!-- 新增职业体检对话框 -->
     <el-dialog :title="title" :visible.sync="addcareeropen" width="400px" append-to-body>
        <el-form ref="careerform" :model="careerform" :rules="careerrules" label-width="80px">
          <el-row>
            <el-form-item label="年度" prop="year">
               <el-date-picker  v-model="careerform.year" type="year" placeholder="选择年" format="yyyy"
      value-format="yyyy"> </el-date-picker>
            </el-form-item>
           <el-form-item label="文件" prop="imgPath">
            <file-upload v-model="careerform.filePath" :limit="1"  />
           </el-form-item>
          </el-row>
        </el-form>
         <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="careersubmitForm">确 定</el-button>
          <el-button @click="careercancel">取 消</el-button>
        </div>
     </el-dialog>



  </div>
</template>

<script>
import { listUser, getUser, delUser, addUser, updateUser, deptTreeSelect,listCareer,addCareer,delCareer } from "@/api/system/user";
import { getToken } from "@/utils/auth";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";
import { Splitpanes, Pane } from "splitpanes";
import "splitpanes/dist/splitpanes.css";

export default {
  name: "User",
  components: { Treeselect, Splitpanes, Pane },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 用户表格数据
      userList: null,
      // 弹出层标题
      title: "",
      // 部门树选项
      deptOptions: undefined,
      // 是否显示弹出层
      open: false,
      //是否显示职业体检
      careeropen:false,
      //查询职业体检参数
      careerParams:{},
      //职业体检数据
      careerdata:undefined,
      //是否显示新增职业体检
      addcareeropen:false,
      //职业体检参数
      careerform:{},
      // 部门名称
      deptName: undefined,
      // 默认密码
      initPassword: undefined,
      // 日期范围
      dateRange: [],
      // 岗位选项
      postOptions: [],
      // 角色选项
      roleOptions: [],
      // 表单参数
      form: {},
      defaultProps: {
        children: "children",
        label: "label"
      },
      teststate:1,
       //状态
      options: [{
          value: 1,
          label: '正常'
        }, {
          value: 2,
          label: '停用'
      }],
      //公司
      corps:[{
          value: 'js',
          label: '江苏'
        },{
          value: 'dy',
          label: '东营'
      }, {
          value: 'jn',
          label: '济宁'
      }, {
          value: 'tg',
          label: '泰国'
      }],
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: undefined,
        phonenumber: undefined,
        status: undefined,
        deptId: undefined
      },
      // 列信息
      columns: [
        { key: 0, label: `工号`, visible: true },
        { key: 1, label: `姓名`, visible: true },
        { key: 2, label: `所属公司`, visible: true },
        { key: 3, label: `微信ID`, visible: true },
        { key: 4, label: `手机号码`, visible: true },
        { key: 5, label: `状态`, visible: true },
        { key: 6, label: `创建时间`, visible: true }
      ],
      // 表单校验
      rules: {
        workerName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
          { min: 2, max: 20, message: '姓名长度必须介于 2 和 20 之间', trigger: 'blur' }
        ],
        workerCode: [
          { required: true, message: "工号不能为空", trigger: "blur" }
        ],
        corp: [
          { required: true, message: "公司不能为空", trigger: "change" }
        ],
        iphone: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
            message: "请输入正确的手机号码",
            trigger: "blur"
          }
        ]
      },
      //职业体检表单验证
      careerrules:{
         year: [
          { required: true, message: "年度不能为空", trigger: "blur" }
        ],
      },
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    }
  },
  created() {
    this.getList();
    this.getDeptTree();
  },
  methods: {
    /** 查询用户列表 */
    getList() {
      this.loading = true;
      listUser(this.queryParams).then(response => {
          this.userList = response.data;
          console.log(JSON.stringify(this.userList))
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 筛选节点
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    // 节点单击事件
    handleNodeClick(data) {
      this.queryParams.organId = data.id;
      this.handleQuery();
    },
    // 用户状态修改
    handleStatusChange(row) {
      let text = row.state === 1 ? "启用" : "停用";
      this.$modal.confirm('确认要"' + text + '""' + row.workerName + '"用户吗？').then(function() {
          getUser(row.workerId).then(response => {
            let updateuser = response.data;
            if(updateuser.state == 1){
              updateuser.state=2;
            }else{
               updateuser.state=1;
            }
            updateUser(updateuser).then(response => {
              
            });
          });
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
         
      }).catch(function() {
        row.state = row.state === "1" ? "2" : "1";
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        workerId: undefined,
        corp: undefined,
        workerCode: undefined,
        workerName: undefined,
        iphone: undefined,
        wxId: undefined,
        state: 1,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if(this.queryParams.workerName == ''){
        this.queryParams.workerName=undefined
      }
      if(this.queryParams.workerCode == ''){
        this.queryParams.workerCode=undefined
      }
      if(this.queryParams.iphone == ''){
        this.queryParams.iphone=undefined
      }
      if(this.queryParams.state == ''){
        this.queryParams.state=undefined
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.deptId = undefined;
      this.$refs.tree.setCurrentKey(null);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.userId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    // 更多操作触发
    handleCommand(command, row) {
      switch (command) {
        case "handleOrg":
          this.handleOrg(row);
          break;
        case "handleCredent":
          this.handleCredent(row);
          break;
        case "handleCareer":
          this.handleOpenCareer(row);
          break;
        default:
          break;
      }
    },
    /**职业体检窗口 */
    handleOpenCareer(row){
      this.careeropen=true;
      this.title="职业体检"
      this.careerParams.workerId=row.workerId;
      this.careerParams.orderBy="year desc";
      this.getCareer();
    },
    /**查询职业体检 */
    getCareer(){
      this.loading = true;
      listCareer(this.careerParams).then(response => {
          this.careerdata = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },
    /**新增职业体检按钮操作 */
    handleAddCareer(){
      this.careerform={}
      this.addcareeropen=true
    },
    //职业体检取消按钮
    careercancel(){
      this.addcareeropen=false;
      this.careerform={};
    },
    /**职业提交按钮 */
    careersubmitForm:function(){
       this.$refs["careerform"].validate(valid => {
        if (valid) {
            console.log(JSON.stringify(this.careerform))
            this.careerform.workerId=this.careerParams.workerId
             addCareer(this.careerform).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.addcareeropen = false;
              this.getCareer();
            });
        }
      });
    },

     /** 删除职业体检按钮操作 */
    handleDeleteCareer(row) {
      this.$modal.confirm('是否确认删除该职业体检？').then(function() {
        return delCareer(row.carcheckId);
      }).then(() => {
        this.getCareer();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /**下载目标责任书 */
    handleDown(row){
      const downurl = process.env.VUE_APP_BASE_API+row.filePath;
      console.log(downurl)
      window.open(downurl)
    },


    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加用户";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const userId = row.workerId || this.ids;
      getUser(userId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改用户";
      });
    },
    /** 分配部门操作 */
    handleOrg: function(row) {
      const userId = row.workerId;
      let data = {id: 'pj1pHjhRL1cz5xTT', url:'./xdemui/xj/user-auth/org/'+userId, name: '所属部门', closable: true, openwin: false,backgroundColor: 'white'};
      parent.postMessage(data, "*");
      // this.$router.push("/xj/user-auth/org/" + userId);
    },

    /** 分配角色操作 */
    handleCredent: function(row) {
      const userId = row.workerId;
      let data = {id: 'pj1pHjhRL1cz11TT', url:'./xdemui/xj/user-auth/credent/'+userId, name: '资质', closable: true, openwin: false,backgroundColor: 'white'};
      parent.postMessage(data, "*");
      // this.$router.push("/xj/user-auth/credent/" + userId);
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.workerId != undefined) {
            updateUser(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addUser(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const userIds = row.workerId || this.ids;
      this.$modal.confirm('是否确认删除用户?').then(function() {
        return delUser(userIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/user/export', {
        ...this.queryParams
      }, `user_${new Date().getTime()}.xlsx`)
    },
    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "用户导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download('system/user/importTemplate', {
      }, `user_template_${new Date().getTime()}.xlsx`)
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    }
  }
};
</script>