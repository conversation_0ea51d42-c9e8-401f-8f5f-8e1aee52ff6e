/*
    Version: 1.0 Timestamp: 2021.4.23
*/
html,body{
    margin: 0;
    font: 14px BlinkMacSystemFont,Roboto,Helvetica Neue,Helvetica,PingFangSC-Regular,Hiragino Sans GB,Microsoft YaHei,SimSun,sans-serif;
    background-color: #fff;
    -webkit-font-smoothing: antialiased;
}
.ai-platform{
    display: block;
}
.ai-module-banner{
    box-sizing: border-box;
    position: relative;
    height: 450px;
    background: no-repeat 50%;
    overflow: hidden;
    background-image: url(../image/banner.jpg);
}   
.ai-module-banner-content{
    overflow: hidden;
    width: 730px;
    padding-right: 450px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%,-50%);
    z-index: 1;
}
.ai-module-banner-title{
    /* height: 67px; */
    font-size: 35px;
    /* letter-spacing: 2px; */
    font-family: 'bold';
    color: #fff;
    position: relative;
}
.ai-module-banner-info{
    margin: 20px 150px 0 0;
    font-size: 20px;
    line-height: 26px;
    font-family: 'regular';
    color: #fff;
}
#verInfo {
    margin: 10px 0 0 0;
    font-size: 16px;
    font-family: 'regular';
    color: #fff;
}
.ai-module-demo{
    margin: 0 auto;
    padding-top: 20px;
}
.ai-module-header{
    line-height: 25px;
    /* font-size: 32px; */
    min-height: 55px;
    color: #000;
    letter-spacing: 0;
    text-align: center;
}
.ai-module-header-master{
    font-family: 'regular';
    font-size: 20px;
    color: #333333;
}
.ai-module-header-slave{
    font-family: 'regular';
    font-size: 12px;
    color: #666666;
}

.ai-module-demo-container-line{
    text-align: center;
    margin-bottom: 5px;
}
.icon1{
    background-color: #3ad0fb;
    border-radius: 50%;
    margin-bottom: 5px;
    text-align: center;
}
.icon2{
    background-color: #06cac3;
    border-radius: 50%;
    margin-bottom: 5px;
}
.icon3{
    background-color: green;
    display: table-cell;
    vertical-align: middle;
    border-radius: 50%;
    margin-bottom: 5px;
}
.cmeraSDK{
    float: left;
    /*max-width: 130px;*/
}
.scannerSDK{
    float: left;
}
.idCardSDK{
    float: left;
}
