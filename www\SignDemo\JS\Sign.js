var _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
var _ws;
var _connected = false;
var _devInfos = null;
var _devNum = -1;
/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        alert("连接失败");
    };
}


/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}
function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.success != 1) {
        // success=1表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.functionName, jsonObj.errorMsg);
        var info = "";
        if (jsonObj.result) {
            info += "<br>维山错误代码: " + jsonObj.result + ", 错误信息: " + jsonObj.errorMsg;
        }
        displayOutputInfo(3, info);
    } else {
        switch (jsonObj.functionName) {
            case "ZCOutputImageBase64AB":
                DisplayImg(jsonObj);
                break;
            default:
                displayOutputInfo(1, msg);
                break;
        }
    }
}

// 处理连接错误
function onError(e, msg) {
    displayOutputInfo(3, msg);
}


function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}


//添加图片项
function DisplayImg(obj) {
    var imgBase64Str = obj.Base64;
    var h = '<div><img width="360" style="padding:5px;" src="' + 'data:jpg' + ';base64,' + imgBase64Str + '"></div>';
    $("#image_data").prepend(h);
    displayOutputInfo(3, "获取签字成功。");
}


// 启动签字笔设备
function ZCStartDeviceAB() {
    var command = JSON.stringify({function: 'ZCStartDeviceAB'});
    SendTxt(command);
}

// 退出签名模式
function ZCEndSignAB() {
    var command = JSON.stringify({function: 'ZCEndSignAB'});
    SendTxt(command);
}

// 初始化设备并进入签名模式
function ZCBeginSignAB() {
    var x = parseInt($("#signx").val());
    var y = parseInt($("#signy").val());
    var width = parseInt($("#signwidth").val());
    var height = parseInt($("#signheight").val());
    var command = JSON.stringify({function: 'ZCBeginSignAB', x:x, y:y, width:width, height:height});
    SendTxt(command);
}

// 停止设备
function ZCStopDeviceAB() {
    var command = JSON.stringify({function: 'ZCStopDeviceAB'});
    SendTxt(command);
}


// 将当前笔迹保存到 Base64 串中，并自动裁剪掉白边
function ZCOutputImageBase64AB() {
    var command = JSON.stringify({function: 'ZCOutputImageBase64AB'});
    SendTxt(command);
}


// 签批板屏幕网页推送
function ZCShowHtmlAB() {
    var signURL = document.getElementById("signURL");
    var url = signURL.value;
    var command = JSON.stringify({function: 'ZCShowHtmlAB',url:url});
    SendTxt(command);
}


// 获取签名板序列号
function ZCGetSerialNumAB() {
    var model = 0;
    var command = JSON.stringify({function: 'ZCGetSerialNumAB',mode:model});
    SendTxt(command);
}


// 设置笔的模式
function ZCSetModeAB() {
    var myselect = document.getElementById("penMode");
    var select=myselect.selectedIndex
    var mode=myselect[select].value;
    var command = JSON.stringify({function: 'ZCSetModeAB',mode:mode});
    SendTxt(command);
}



//=============================================================================
//=============================================================================
//=============================================================================
//=============================================================================


// 删除全部拍照文件
function clearImageFile() {
    $("#tabBody").empty();
    $("#image_data").empty();
}


$(window).unload(function () {
    // 关闭页面之前,关闭摄像头,关闭连接
    if (_ws && _ws.connected) {
        _ws.Close();
        _ws = null;
    }
});
