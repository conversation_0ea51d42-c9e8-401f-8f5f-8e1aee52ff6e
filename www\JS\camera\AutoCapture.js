var _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
var _ws;
var _connected = false;
var _devNum = -1;
var _detectionType = -1; // 连拍的类型
var _timer = 0;

Date.prototype.toLocaleString = function () {
    var y = this.getFullYear(), m = this.getMonth() + 1, d = this.getDate(), h = this.getHours(),
        mi = this.getMinutes(), s = this.getSeconds(), ms = this.getMilliseconds();
    return y + '-' + (m < 10 ? ('0' + m) : m) + '-' + (d < 10 ? ('0' + d) : d) + ' ' + (h < 10 ? ('0' + h) : h) + ':' + (mi < 10 ? ('0' + mi) : mi) + ":" + (s < 10 ? ('0' + s) : s) + '.' + ((ms + 1000) + "").substr(1, 3);
};

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        alert(Lang.WS.disConn);
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.func, jsonObj.errorMsg);
        alert(jsonObj.func + "\r\n" + jsonObj.errorMsg);
    } else {
        switch (jsonObj.func) {
            case "GetCameraVideoBuff":
                DisplayVideo(jsonObj);
                break;
            case "GetCameraInfo":
                DisplayDevInfo(jsonObj.devInfo);
                break;
            case "CameraCapture":
                DisplayCaptureImg(jsonObj);
                break;
            case "Notify":
                // 收到通知
                if (jsonObj.event == "OnUsbKeyPress") {
                    // 收到USB按键按下的通知
                    // {"func":"Notify","event":"OnUsbKeyPress","reqId":1636099207839.0,"result":0,"time":"1636099214"}
                    // DisplayUsbKeyPress(jsonObj);
                } else if (jsonObj.event == "OnDeviceChanged") {
                    // 设备有变化,格式如下:
                    // {"func":"Notify","event":"OnDeviceChanged","devName":"\\\\?\\USB#VID_0C45&PID_6366...","func":"OnDeviceChanged","result":0,"type":1}
                    // type: 1设备插入; 2设备已拔出
                    GetCameraInfo();
                }
                break;
            case "CameraAutoCapture":
                DisplayStatus(jsonObj);
                break;
            default:
                console.log(msg);
                break;
        }
    }
}

// 获取设备信息
function GetCameraInfo() {
    SendTxt('{"func":"GetCameraInfo","reqId":"' + new Date().getTime() + '"}');
}

//显示视频
function DisplayVideo(v) {
    document.getElementById("video").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
}

function DisplayDevInfo(devInfos) {
    if (devInfos == null || devInfos.length == 0) {
        alert(Lang.cam.doc);
        return;
    }
    if (_devNum < 0) {
        openCamera();
    }
}

function DisplayCaptureImg(json) {
    if (json.imgBase64 && json.imgBase64.length > 0) {
        document.getElementById("imgCapture").src = "data:" + json.mime + ";base64," + json.imgBase64[0];
        document.getElementById("captureInfo").innerText = "自动拍照成功, " + new Date().toLocaleString();
        progress0.value = progress0.max;
        audioCapture.play();
    }
}

// 打开摄像头
function openCamera() {
    var reqId = new Date().getTime();
    if (_devNum >= 0) {
        //关闭之前的摄像头
        SendTxt('{"func":"CloseCamera","reqId":"' + reqId + '","devNum":' + _devNum + '}');
        reqId++;
    }

    //开启摄像头
    _devNum = 0;
    SendTxt('{"func":"OpenCamera","reqId":"' + reqId + '","devNum":' + _devNum + ',"mediaNum":0,"resolutionNum":0,"fps":5}');
    reqId++;

    // 设置单图裁切（裁切方式：0-不裁切;1-单图裁切; 2-多图裁切）
    SendTxt('{"func":"SetCameraImageInfo","reqId":"' + reqId + '","devNum":' + _devNum + ',"cropType":1}');
    reqId++;

    // 获取预览视频
    SendTxt('{"func":"GetCameraVideoBuff","reqId":"' + reqId + '","devNum":' + _devNum + ',"enable":"true"}');
    reqId++;
}

// 自动连拍控制
function autoCaptureControl(type) {
    _detectionType = type;
    var reqId = new Date().getTime();
    var cmd;
    if (type < 0) {
        // 停止自动连拍
        cmd = '{"func":"CameraAutoCapture","reqId":"' + reqId + '","devNum":' + _devNum + ',"enable":false}';
    } else {
        // 自动连拍CameraAutoCapture命令在启动（enable为true）时是个一问多答的命令，除此命令的回应命令（func为CameraAutoCapture）外，
        // 在自动拍成功后，还会有func为CameraCapture或CameraCaptureBook的回应消息。
        // 属性isBook为true,则连拍时会按拍书来处理，回应消息是CameraCaptureBook
        // 检测类型: 0监测模式，1定时模式
        var paramVal = document.getElementById("speed").value;
        cmd = '{"func":"CameraAutoCapture","reqId":"' + reqId + '","devNum":' + _devNum + ',"enable":true,"isBook":false,"detectionType":' +
                type + ',"parma":' + paramVal + ',"mode":"base64;path"}';
    }
    SendTxt(cmd);

    // 定时连拍倒计时（仅仅是显示作用）控制
    if (_detectionType == 1) {
        progress0.max = paramVal;
        progress0.value = 0;
        _timer = window.setInterval(function () {
            var v = progress0.value + 1;
            if (v > progress0.max) {
                v = 0;
            }
            progress0.value = v;
        }, 1000);
        document.getElementById("divTimer").style.display = '';
    } else {
        clearInterval(_timer);
        _timer = 0;
        document.getElementById("divTimer").style.display = 'none';
    }
}

function onSpeedChange() {
    var v = document.getElementById("speed").value;
    document.getElementById("speedNum").innerText = v;
    if (_detectionType >= 0)  {
        autoCaptureControl(_detectionType);
    }
}

function DisplayStatus(v) {
    if (v.enable) {
        $('#status').text(Lang.autoCapture.started + (v.detectionType == 1 ? Lang.autoCapture.timeLapse : Lang.autoCapture.auto));
    } else {
        $('#status').text(Lang.autoCapture.stopped);
    }
}

//Init info
$(function () {
    $('#status').text(Lang.autoCapture.notStart);
    //获取摄像头设备列表
    GetCameraInfo();
});
