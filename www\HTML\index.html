<!DOCTYPE html>
<html lang="zh-cmn-Hans-<PERSON><PERSON>">
<head>
    <title>图像录入</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../langs/zhCN.js"></script>
    <script src="../JS/index.js"></script>
    <link rel="stylesheet" href="../CSS/index.css"/>
</head>
<body>
<div class="ai-platform">
    <div id="apiModuleBanner" class="ai-module-banner">
        <div class="ai-module-banner-content">
            <div class="ai-module-banner-title">图像录入SDK演示</div>
            <div class="ai-module-banner-info">支持CameraSDK 、ScannerSDK 、身份证读卡器</div>
            <div id="verInfo">版本信息</div>
        </div>
    </div>
    <div class="ai-module-demo" id="aiModuleContent">
        <div class="ai-module-demo-container" id="aiModuleContainer">
            <div class="ai-module-demo-container-line">
                <div class="cmeraSDK">
                    <div id='icon1' class="icon1">
                        <img id="cmeraSDKIcon" src="../image/CameraSDK.png" >
                    </div>
                    <span>CameraSDK</span>
                    <p><a href="camera/VisualCamera.html" title="演示高拍仪拍照、裁切、图像处理等功能">高拍仪拍照</a></p>
                    <p><a href="camera/RecogBarCode.html" title="演示通过高拍仪进行条码识别">条码识别</a></p>
                    <p><a href="camera/CaptureBook.html" title="演示通过高拍仪进行书本拍摄、自动连拍、合并PDF文件">书本拍摄</a></p>
                    <p><a href="ocr/ocr.html" title="演示OCR识别(支持区域识别)，以及合并PDF文件">OCR、合并PDF</a></p>
                    <p><a href="ocr/2txt.html" title="演示PDF、tif、word、excel等文档识别功转换为txt文件">文档识别转换</a></p>
                    <p><a href="camera/AutoCapture.html" title="演示通过高拍仪进行自动连拍">自动连拍</a></p>
                    <p><a href="camera/AutoCaptureCustom.html" title="演示通过高拍仪进行自动连拍检测，在收到触发通知后进行自定义的处理">自动连拍(自定义)</a></p>
                    <p><a href="camera/CameraVideoSetting.html" title="演示对视频亮度、对比度等视频参数进行调整，以及相机的控制">相机视频设置</a></p>
                    <p><a href="camera/LaunchExeH.html" title="演示启动Exe进行拍照">启动Exe拍照</a></p>
                    <p><a href="camera/HttpFtpUpload.html" title="演示HTTP/FTP文件上传">HTTP/FTP文件上传</a></p>
                    <p><a href="/Http-Req/Camera.html" title="按照东软集团(NeuSoft)提出的HTTP交互协议，演示高拍仪的基本功能">高拍仪演示(东软协议)</a></p>
                </div>
                <div class="scannerSDK">
                    <div id='icon2' class="icon2">
                        <img id="scannerSDKIcon" src="../image/ScannerSDK.png">
                    </div>
                    <span>扫描仪及其他设备</span>
                    <p><a href="../Ext-Dev/W7/HS/StartAPP.html" title="演示安卓多功能终端的拍照、PFD合并、OCR识别、指纹读取、身份证读取、人证比对等功能">多功能终端功能演示</a></p>
                    <p><a href="../Ext-Dev/W7/HSTC/StartAPP.html" title="演示安卓多功能终端W7的签署诚信授权、救助资格认证等功能">多功能终端 诚信授权演示</a></p>
                    <p><a href="otherDev/IdFinger.html" title="演示从身份证读卡器读取身份证信息以及指纹信息。从指纹设备读取指纹，并进行指纹比对。">身份证与指纹</a></p>
                </div>
                <div class="idCardSDK">
                    <div id='icon3' class="icon3">
                        <img id="idCardSDKIcon" src="../image/idCard.png">
                    </div>
                    <span>IdCardSSDK</span>
                    <p><a href="camera/IdFace.html" title="演示通过高拍仪、身份证读卡器、双目摄像头进行人证比对">人证比对</a></p>
                    <p><a href="ocx/ocxDemo.html" title="演示在IE浏览器(IE11)中通过OCX控件操作摄像头进行人像比对">OCX照片比对</a></p>
                    <p><a href="ocx/ocxIdFace.html" title="演示在IE浏览器(IE11)中通过OCX控件使用高拍仪、身份证读卡器、双目摄像头进行人证比对">OCX人证比对</a></p>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>

