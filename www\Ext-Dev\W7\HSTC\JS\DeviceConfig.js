var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    //_url = "ws://************:10086";
    console.log(_url);
}

initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e.data);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

function SendBlob(blob) {
    _connected ? _ws.send(blob) : ConnectServer(SendTxt, blob)
}


/**
 * 处理服务器二进制消息
 */
function onBinMessage(data) {
    //console.log(typeof (data));
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "GetConfigInfo":
            if (jsonObj.result == 0) {
                displayOutputInfo(2, "查询设备配置信息成功.");
                $("#deviceNumber").val(jsonObj.deviceNumber);
                $("#address").val(jsonObj.address);
                $("#technologyCompany").val(jsonObj.technologyCompany);
                $("#technologyPhone").val(jsonObj.technologyPhone);
            } else {
                displayOutputInfo(3, "查询设备配置信息失败.<br>result = " + jsonObj.result);
            }
            break;
        case "SetConfigInfo":
            if (jsonObj.result == 0) {
                displayOutputInfo(2, "修改设备配置信息成功.");
            } else {
                displayOutputInfo(3, "修改设备配置信息失败.<br>result = " + jsonObj.result);
            }
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                var info = jsonObj.func + "<br>";
                if (jsonObj.errorMsg) {
                    info += jsonObj.errorMsg;
                }
                displayOutputInfo(3, info);
            }
            break;
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

function clearInput() {
    $("#deviceNumber").val("");
    $("#address").val("");
    $("#technologyCompany").val("");
    $("#technologyPhone").val("");
}

function defaultInput() {
    $("#deviceNumber").val("000168");
    $("#address").val("北京市海淀区西北旺东路10号院东区23号楼");
    $("#technologyCompany").val("北京华胜天成科技股份有限公司");
    $("#technologyPhone").val("010-80986698");
}

// 查询设备配置信息
function queryDevConfig() {
    displayOutputInfo(1, "设备配置查询中......");
    SendJson({func: "GetConfigInfo", reqId: new Date().getTime().toString(10), to: 1});
}

// 修改设备配置信息
function configDev() {
    displayOutputInfo(1, "设备配置中......");
    SendJson({func: "SetConfigInfo", reqId: new Date().getTime().toString(10), to: 1,
        deviceNumber: $("#deviceNumber").val(),
        address: $("#address").val(),
        technologyCompany: $("#technologyCompany").val(),
        technologyPhone: $("#technologyPhone").val()});
}

$(function () {
    queryDevConfig();
});