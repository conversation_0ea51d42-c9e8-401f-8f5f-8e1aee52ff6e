<template>
  <div class="app-container" style="background-color:white;">
    <h4 class="form-header h4">基本信息</h4>
    <el-form ref="form" :model="form" label-width="80px">
      <el-row>
        <el-col :span="8" :offset="2">
          <el-form-item label="姓名" prop="workerName">
            <el-input v-model="form.workerName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="2">
          <el-form-item label="工号" prop="workerCode">
            <el-input  v-model="form.workerCode" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <h4 class="form-header h4">部门职责信息</h4>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" >新增</el-button>
      </el-col>

      
      <right-toolbar  @queryTable="getList" ></right-toolbar>
    </el-row>

    <el-table v-loading="loading"  ref="table" @selection-change="handleSelectionChange" :data="userOrgList">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center"  key="organName" prop="organization.organName" />
      <el-table-column label="工种" align="center"  prop="worktypeId" >
        <template slot-scope="scope">
          <dict-tag :options="dict.type.system_worktype" :value="scope.row.worktypeId"/>
        </template>
      </el-table-column>
      <el-table-column label="职务" align="center"  prop="jobId">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.system_job" :value="scope.row.jobId"/>
        </template>
      </el-table-column>
      <el-table-column label="是否主职" align="center"  prop="ismainjob">
        <template slot-scope="scope">
          <span v-if="scope.row.ismainjob==true">是</span>
          <span v-if="scope.row.ismainjob==false">否</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center"  key="creationtime"  prop="creationtime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
           <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" >修改</el-button>
           <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"  @pagination="getList"/>

     <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="workorgform" :model="workorgform" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="部门" prop="organId">
                <el-select v-model="initValue"   :clearable="true"  placeholder="请选择部门"
                   :popper-append-to-body="false"  @change="changeOption" style="width: 200px">
              <el-option :value="optionValue" style="height: auto">
                <el-tree  ref="tree" node-key="id" empty-text="暂无数据" :data="deptOptions"   @node-click="handleCheckChange"/>
                </el-option>
              </el-select>

            </el-form-item>
          </el-col>
           <el-col :span="12">
            <el-form-item label="工种" prop="worktypeId">
              <el-select v-model="workorgform.worktypeId" placeholder="请选择工种" clearable>
                  <el-option v-for="dict in dict.type.system_worktype" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="职务" prop="jobId">
              <el-select v-model="workorgform.jobId" placeholder="请选择职务" clearable>
                  <el-option v-for="dict in dict.type.system_job" :key="dict.value" :label="dict.label" :value="dict.value"/>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否主职" prop="ismainjob">
                <el-radio-group v-model="workorgform.ismainjob">
                  <el-radio v-for="dict in options" :key="dict.value" :label="dict.value">{{ dict.label }}</el-radio>
                </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { getUser ,getWorkerOrg,deptTreeSelect,addWorkorg,updateWorkorg,delWorkorg,getWorkorg} from "@/api/system/user";

export default {
  name: "AuthRole",
  dicts: ['system_worktype','system_job'],
  data() {
    return {
       // 遮罩层
      loading: true,
      // 分页信息
      total: 0,
      pageNum: 1,
      pageSize: 10,
      // 选中角色编号
      roleIds:[],
      // 角色信息
      roles: [],
      // 用户信息
      form: {},
      //用户部门信息
      userOrgList:[],
       // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workerId: undefined
      },
      //用户部门信息
      workorgform:{},
      // 是否显示弹出层
      open:false,
      //是否主职
      options:[{
          value: true,
          label: '是'
        }, {
          value: false,
          label: '否'
      }],
      //弹出标题
      title:undefined,
       // 部门树选项
      deptOptions: undefined,
       optionValue: undefined,
       initValue:undefined,
      // 表单校验
      rules: {
        organId: [
          { required: true, message: "部门不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    const userId = this.$route.params && this.$route.params.userId;
    console.log(userId)
    this.queryParams.workerId=userId
    this.workorgform.workerId=userId
    if (userId) {
      this.getUser();
      this.getList();
      this.getDeptTree();
    }
  },
  methods: {

    getList(){
      this.loading = true;
      getWorkerOrg(this.queryParams).then((response)=>{
          this.userOrgList = response.data;
          this.total = response.totalCount;
          this.loading = false;
      })
    },
    getUser(){
      getUser(this.queryParams.workerId).then((response) => {
        this.form = response.data;
        
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 表单重置
    reset() {
      this.workorgform = {
        organId: undefined,
        corp: undefined,
        worktypeId: undefined,
        jobId: undefined,
        ismainjob: undefined,
        organization:undefined
      };
      this.resetForm("workorgform");
    },
    //新增用户部门
    handleAdd(){
        this.reset();
        this.open = true;
        this.title = "新增部门";
    },
    handleUpdate(row){
      this.reset();
      const workerOrgId = row.workerOrgId || this.ids;
      getWorkorg(workerOrgId).then(response => {
        this.workorgform = response.data;
        this.optionValue = response.data.organId
        this.initValue = response.data.organization.organName
        this.open = true;
        this.title = "修改部门";

      });
    },
     /** 提交按钮 */
    submitForm(){
      this.$refs["workorgform"].validate(valid => {
        if (valid) {
          
          this.workorgform.workerId= this.queryParams.workerId
          if (this.workorgform.workerOrgId != undefined) {
            updateWorkorg(this.workorgform).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addWorkorg(this.workorgform).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const workerOrgId = row.workerOrgId || this.ids;
      this.$modal.confirm('是否确认删除部门?').then(function() {
        return delWorkorg(workerOrgId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    changeOption(item) {
            console.log('select======', this.initValue,this.optionValue);
    },
    /**单击部门树 */
     handleCheckChange(data) {
      this.workorgform.organId=data.id
      this.optionValue = data.id
      this.initValue = data.label
     },
    /** 单击选中行数据 */
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.roleIds = selection.map((item) => item.roleId);
    },
    // 保存选中的数据编号
    getRowKey(row) {
      return row.roleId;
    },

    /** 关闭按钮 */
    close() {
      const obj = { path: "/system/user" };
      this.$tab.closeOpenPage(obj);
    },
       // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
  },
};
</script>