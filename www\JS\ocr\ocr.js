var _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
var _ws;
var _connected = false;
var _devInfos = null;
var _devNum = -1;
var _supportOcr = 0;
var _cropBox = null;

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        alert(Lang.WS.disConn)
    };
}

/**
 * 向服务器发送信息
 */
function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.func, jsonObj.errorMsg);
        displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
    } else {
        switch (jsonObj.func) {
            case "GetCameraVideoBuff":
                DisplayVideo(jsonObj);
                break;
            case "GetCameraInfo":
                DisplayDevInfo(jsonObj.devInfo);
                //获取OCR支持信息(语言列表)
                GetOcrInfo();
                //获取曝光
                GetVideoParam();
                break;
            case "GetOcrSupportInfo":
                DisplayOcrSupportInfo(jsonObj);
                break;
            case "CameraCapture":
                audioCapture.play();
                for (var i = 0; i < jsonObj.imagePath.length; i++) {
                    displayFile(jsonObj.imagePath[i]);
                }
                break;
            case "FileToBase64":
                DisplayFileView(jsonObj);
                break;
            case "MergeFile":
                DisplayMergeFile(jsonObj);
                break;
            case "FileOCR":
                DisplayConvertToFile(jsonObj);
                break;

            default:
                console.log(msg);
                break;
        }
    }
}

//显示识别成功的文件
function DisplayConvertToFile(jsonObj) {
    if (jsonObj.filePath) {
        displayOutputInfo(2, Lang.MSG.ocrOk + "<br>" + jsonObj.filePath);
        viewFile(jsonObj.filePath);
    }
}

//显示合并成功的PDF文件
function DisplayMergeFile(jsonObj) {
    if (jsonObj.filePath) {
        displayOutputInfo(2, Lang.MSG.mergePdfOk + "<br>" + jsonObj.filePath);
        viewFile(jsonObj.filePath);
    }
}

function viewFile(filePath) {
    if (location.protocol.substr(0,4) == 'http') {
        var pos = filePath.lastIndexOf('\\');
        pos = filePath.lastIndexOf('\\', pos - 1);
        var url = location.origin + '/tmp/' + filePath.substr(pos + 1).replace('\\','/');
        window.open(url);
    }
}

var _path = null;
var _th = '<tr><th>文件名</th><th>区域</th><th>操作</th></tr>';
var _tdOpration = '<td><a onclick="vf()">查看</a> <a onclick="df()">删除</a></td>';
function displayFile(file) {
    var pos = file.lastIndexOf('\\');
    if (_path == null) {
        _path = file.substring(0, pos);
    }
    var html = '<tr><td>' + file.substring(pos + 1) + '</td><td></td>' + _tdOpration + '</tr>';
    $("#tbFile").append(html);
}

// 清空图片展示列表
function clearImageFile() {
    $("#image_data").empty();
    $("#tbFile").empty().append(_th);
}

function DisplayFileView(v) {
    var img = document.getElementById("imgFile");
    img.src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    // 显示裁切框
    _cropBox.enable();
}

function vf() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    $("#tbFile .active").removeClass("active");
    $(target).parent().parent().addClass("active");
    var file = _path + '\\' + $(target).parent().prev().prev().text();
    // 把文件转为base64,用于显示查看
    SendJson({func:'FileToBase64', reqId: new Date().getTime(), filePath:file});
}

// 删除拍照文件
function df() {
    var e = e || window.event;
    var target = e.target || e.srcElement;
    $(target).parent().parent().remove();
}

function displayOutputInfo(disp, s, add) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        var h = "<b>" + s + "</b>";
        if (add) {
            var h0 = $('#outInfo').html();
            if (h0.length > 0) {
                h = h0 + "<br>" + h;
            }
        }
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html(h);
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

var _w = 0, _h = 0;

function DisplayVideo(v) {
    if (v.imgBase64Str) {
        if (_w != v.width || _h != v.height) {
            //设置视频显示的宽高（宽度控制为400px）
            if (v.width > v.height) {
                $("#pic").css('width', _widthDivDev + 'px').css('height', Math.ceil(_widthDivDev * v.height / v.width) + 'px');
            } else {
                $("#pic").css('height', _widthDivDev + 'px').css('width', Math.ceil(_widthDivDev * v.width / v.height) + 'px');
            }
            _w = v.width, _h = v.height;
        }
        //显示视频
        document.getElementById("pic").src = "data:" + v.mime + ";base64," + v.imgBase64Str;
    }
}

function DisplayDevInfo(devInfos) {
    _devInfos = devInfos;
    if (_devInfos == null || _devInfos.length == 0) {
        alert(Lang.cam.doc);
        return;
    }
    //初始化设备信息
    displayCamera();
    displayMediaType();
    displayResolution();

    switchCameraInfo_Fun();
    setCameraImageInfo_Fun();
}

// 刷新显示 视频格式 下拉列表
function displayMediaType() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var mediaTypes = _devInfos[index1].mediaTypes;
    var obj = document.getElementById("mediaType");
    obj.options.length = 0;
    if (_devInfos[index1].stillPin != undefined) {
        obj.options.add(new Option(_devInfos[index1].stillPin.mediaType, 0));
    } else {
        for (var i = 0; i < mediaTypes.length; ++i) {
            obj.options.add(new Option(mediaTypes[i].mediaType, i));
        }
    }
}

// 刷新显示 分辨率下拉列表
function displayResolution() {
    var index1 = document.getElementById("cameraInfo").options.selectedIndex;
    var index2 = document.getElementById("mediaType").options.selectedIndex;
    var obj = document.getElementById("resolution");
    //清空resolution:select数据
    obj.options.length = 0;
    if (_devInfos[index1].stillPin != undefined) {
        _isStillPin = true;
        var SPresolutions = _devInfos[index1].stillPin.resolutions;
        for (var i = 0; i < SPresolutions.length; i++) {
            obj.options.add(new Option(SPresolutions[i], i));
        }
    } else {
        _isStillPin = false;
        var resolutions = _devInfos[index1].mediaTypes[index2].resolutions;
        for (var i = 0; i < resolutions.length; i++) {
            obj.options.add(new Option(resolutions[i], i));
        }
    }
}

//设备信息的onchange事件
function switchCameraInfo_Fun() {
    var reqId = new Date().getTime();
    if (_devNum >= 0) {
        //关闭之前的摄像头
        SendJson({func:"CloseCamera", reqId: reqId, devNum: _devNum});
    }

    //刷新显示 视频格式 下拉列表
    displayMediaType();

    //刷新显示 分辨率下拉列表
    displayResolution();

    //开启摄像头
    _devNum = $("#cameraInfo").val() == null ? 0 : parseInt($("#cameraInfo").val());
    reqId++;
    SendJson({func:"OpenCamera", reqId: reqId, devNum: _devNum, mediaNum: 0, resolutionNum: 0, fps: 5});

    // 获取预览视频
    reqId++;
    SendJson({func:"GetCameraVideoBuff", reqId: reqId, devNum: _devNum, enable: true});
}

//设置设备信息
function setCameraInfo_Fun() {
    //分辨率的值
    var resolutionNum = $("#resolution").val() == null ? 0 : parseInt($("#resolution").val());
    var mediaNum = $("#mediaType").val() == null ? 0 : parseInt($("#mediaType").val());
    var reqId = new Date().getTime();
    if (_isStillPin) {
        SendJson({func: 'SetStillPinInfo', reqId: reqId, devNum: _devNum, resolutionNum: resolutionNum});
    } else {
        SendJson({func: 'SetCameraInfo', reqId: reqId, devNum: _devNum, mediaNum: mediaNum, resolutionNum: resolutionNum});
    }
}

// 刷新显示 设备名称 下拉列表
function displayCamera() {
    var obj = document.getElementById("cameraInfo");
    var lastSelectedIndex = obj.options.selectedIndex;
    obj.options.length = 0;
    for (var i = 0; i < _devInfos.length; i++) {
        obj.options.add(new Option(_devInfos[i].devName, _devInfos[i].id));
    }
    if (lastSelectedIndex >= 0 && lastSelectedIndex < _devInfos.length) {
        obj.options.selectedIndex = lastSelectedIndex;
    }
}

function DisplayOcrSupportInfo(v) {
    if (v.languages) {
        // 支持orc, 允许‘自动文字方向’
        _supportOcr = 1;
        document.getElementById('opOcrDirection').disabled = false;

        var obj = document.getElementById("language");
        obj.options.length = 0;
        for (var i = 0; i < v.languages.length; i++) {
            // 语言ID是从1开始
            obj.options.add(new Option(v.languages[i], i + 1));
        }
        // 默认选中'Simplified chinese+English'
        obj.selectedIndex = 98;

        if (v.fileTypes) {
            obj = document.getElementById("docType");
            obj.options.length = 0;
            for (var i = 0; i < v.fileTypes.length; i++) {
                obj.options.add(new Option(v.fileTypes[i], i));
            }
            obj.selectedIndex = 0;
        }
    } else {
        displayOutputInfo(3, "系统不支持OCR功能，可能没有安装OCR组件。");
    }
}

// 获取设备信息
function GetCameraInfo() {
    SendJson({func: "GetCameraInfo", reqId: new Date().getTime()});
}

// 获取OCR支持信息
function GetOcrInfo() {
    SendJson({func: "GetOcrSupportInfo", reqId: new Date().getTime()});
}

function GetVideoParam() {
    SendJson({func: 'GetVideoParameter', reqId: new Date().getTime(), devNum: _devNum});
}

function cameraCapture_Fun() {
    //设置图像算法
    setCameraImageInfo_Fun();
    //高拍仪拍照接口
    SendJson({func: 'CameraCapture', reqId: new Date().getTime(), devNum: _devNum, mode: 'path'});
}

/**设置图像算法
 CropType 裁切：0-不裁切  1-单图裁切　２－多图裁切　３－曲面矫正
 ImageType 图像效果：0-彩色原色 1-灰度图片 2-黑白文档　３－　彩色文档　４－红印文档　５－蓝印文档　６－彩色照片　７－票据增强
 FillBorderType 补边：0-不填充 1-自动填充白色　２－自动填充周边色
 RemovalForeign 异物去除　０-无　1-去除手指　２－去除装订孔
 **/
function setCameraImageInfo_Fun() {
    var cropType = parseInt($("#crop_type").val());
    var imageType = parseInt($("#image_type").val());
    var rotateAngle = parseInt($("#rotateType").val());
    var langId = parseInt($("#language").val());
    var reqId = new Date().getTime();
    var dpi = parseInt($("#dpi").val());
    var cmd = {func:'SetCameraImageInfo', reqId:reqId, devNum:_devNum, cropType:cropType, imageType:imageType,
        rotate:rotateAngle, textOrientationLangId:langId, dpi:dpi};
    SendJson(cmd);
}

function OnRotateChange() {
    setCameraImageInfo_Fun();
}

function mergePDF() {
    var reqId = new Date().getTime();
    var fileArray = new Array();
    var trs = $('#tbFile tr');
    for (i = 1; i < trs.length; i++) {
        fileArray.push(_path + '\\' + trs[i].children[0].innerText);
    }
    // fileType: 0 PDF; 1-TIFF(暂不支持)
    SendJson({func: 'MergeFile',reqId:reqId, fileType:0, imagePath:fileArray, mode:'path'});
}

function convertToFile() {
    var lang = $("#language option:selected").text();
    var docType = $("#docType option:selected").text();
    var reqId = new Date().getTime();
    var aryFile = new Array();
    var aryZones = new Array();
    var trs = $('#tbFile tr');
    for (i = 1; i < trs.length; i++) {
        aryFile.push(_path + '\\' + trs[i].children[0].innerText);

        var zones = new Array();
        var str = trs[i].children[1].innerText;
        var ary1 = str.split(";");
        for (var j = 0; j < ary1.length; j++) {
            var ary2 = ary1[j].split(",");
            if (ary2.length >= 6) {
                zones.push({left: ary2[0], top: ary2[1], right: ary2[2], bottom: ary2[3], absolute: ary2[4], type: ary2[5]});
            }
        }
        aryZones.push(zones);
    }
    SendJson({func: 'FileOCR', reqId: reqId, language: lang, extName: docType, detectTextOrientation: true,
        imagePath: aryFile, zones: aryZones});
}

function cropBox_OnChange() {
    // 获取CropBox的数组:[视频框的宽, 视频框的高, 框1的left, 框1的top, 框1的right, 框1的bottom, 框2的left, 框2的top, ....]
    var cropBoxArray = _cropBox.getCropBoxArray();
    var strVal = "";
    if (cropBoxArray.length >= 6) {
        var img = document.getElementById("imgFile");
        // 图片真实的尺寸
        var imgW = img.naturalWidth, imgH = img.naturalHeight;
        var ratio = imgW / cropBoxArray[0];
        var i = 2;
        while (i < cropBoxArray.length) {
            var left = Math.round(cropBoxArray[i++] * ratio);
            var top = Math.round(cropBoxArray[i++] * ratio);
            var right = Math.round(cropBoxArray[i++] * ratio);
            var bottom = Math.round(cropBoxArray[i++] * ratio);
            var rect = left + "," + top + "," + right + "," + bottom;
            if (strVal.length > 0) {
                strVal += ";";
            }
            var absolute = 1;
            var type = cropBoxArray[i++];
            strVal += (rect + "," + absolute + "," + type);
        }
    }
    $("#tbFile .active td:nth-child(2)").text(strVal);
    // console.log("cropBox_OnChange", cropBoxArray, img.width, img.height, strVal);
}

function cropBox_OnBuildInnerHtml(html) {
    var divHtml = '<div class="divZoneType"><label>区域类型:</label><select class="zoneType">' +
        '<option value="0">文字区</option><option value="1">插图照片区</option><option value="2">表格区</option>' +
        '<option value="3">条码区</option><option value="4">手写文字区</option></select></div>';
    return divHtml + html;
}

function cropBox_OnGetBoxVal(box, ary) {
    if (box) {
        var objSelects = box.getElementsByClassName("zoneType");
        if (objSelects.length > 0) {
            ary.push(parseInt(objSelects[0].value));
        }
    }
}

var _widthDivDev = 0, _widthMiddle = 0;
function onWinResize() {
    // 调整页面高度
    var h = document.documentElement.clientHeight - document.getElementById("divTitle").offsetHeight;
    $("#divDev").css('height', h + 'px');
    $("#divTab").css('height', h + 'px');

    _widthDivDev = $("#divDev")[0].offsetWidth;
    _widthMiddle = document.documentElement.clientWidth - _widthDivDev - $("#divTab")[0].offsetWidth - 20;
    $("#pic").css('width', _widthDivDev + 'px').css("min-height", Math.ceil(_widthDivDev * 9/16) + 'px');
    $("#imgFile").css('width', _widthMiddle + 'px');

    if (_cropBox) {
        _cropBox.adjustPos();
    }
}

$(function () {
    //获取摄像头设备列表
    GetCameraInfo();

    window.onresize = onWinResize;
    onWinResize();

    _cropBox = new CropBox("imgFile");
    _cropBox.onChange = cropBox_OnChange;
    _cropBox.onBuildInnerHtml = cropBox_OnBuildInnerHtml;
    _cropBox.onGetBoxVal = cropBox_OnGetBoxVal;
})