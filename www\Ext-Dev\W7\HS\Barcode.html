<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>启动APP演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li><a href="FileDisplay.html">文件播放</a></li>
                <li><a href="IdCard.html">身份证读取</a></li>
                <li><a href="ReadFinger.html">指纹读取</a></li>
                <li><a href="IdCardMatch.html">人证比对</a></li>
                <li><a href="DocOCR.html">OCR识别与水印</a></li>
                <li><a href="ReadSign.html">手写签名</a></li>
                <li><a href="ReadKeyboard.html">评价按键读取</a></li>
                <li class="active"><a href="#">条码扫描与识别</a></li>
                <li><a href="LayoutRecognition.html">版面识别</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/W7-0911.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>条码识别 演示</h1>
        <p>这个页面演示了使用智能终端设备的条码枪扫描条码的功能，以及使用智能终端设备的文档摄像头拍照进行条码识别的功能。</p>
    </div>

    <div class="page-header">
    </div>

    <div class="col-md-8" style="text-align: center">
        <p>
            <label>设备名称:</label>
            <select id="cameraInfo" style="width:160px;" onchange="ReOpenCamera()"></select>
            <label style="margin-left:20px">分辨率:</label>
            <select id="resolution" style="width:120px;" onchange="ReOpenCamera()"></select>
            <label style="margin-left:20px">旋转:</label>
            <select id="rotateType" style="width:110px;" onchange="SetCameraInfo()">
                <option value="0">不旋转</option>
                <option value="90">旋转90度</option>
                <option value="180">旋转180度</option>
                <option value="270">旋转270度</option>
            </select>
        </p>
        <img id="video" src="" style="width:640px;height:480px;">
        <p>摄像头视频</p>
    </div>

    <div class="col-md-4" style="text-align:left; padding-left:0; padding-right:0;">
        <div class="col-md-12" style="text-align:center; margin-bottom: 20px; padding: 0;">
            <button type="button" class="btn btn-primary" onclick="BarCodeRecognize()" style="margin-right:20px">拍照条码识别</button>
<!--            <label style="margin-right:20px" title="根据影像变化, 自动拍照进行条码识别">-->
<!--                <input id="chkAuto" type="checkbox" class="switch" onchange="switchAuto()">自动识别-->
<!--            </label>-->
            <button type="button" class="btn btn-primary" onclick="BarCodeScan()">条码枪扫描条码</button>
        </div>
        <p>条码扫描/识别结果:</p>
        <pre id="strResult" style="width: 100%;height: 430px"></pre>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/BarCode.js"></script>
</body>
</html>