<template>
  <div class="app-container" style="background-color:white;">
    <h4 class="form-header h4">制度化信息</h4>
    <el-form ref="form" :model="form" label-width="80px">
      <el-row>
        <el-col :span="8" :offset="2">
          <el-form-item label="文档编号" prop="insCode">
            <el-input v-model="form.insCode" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="2">
          <el-form-item label="文档名称" prop="insName">
            <el-input  v-model="form.insName" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <h4 class="form-header h4">分享信息</h4>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" >生成</el-button>
      </el-col>

      
      <right-toolbar  @queryTable="getList" ></right-toolbar>
    </el-row>

    <el-table v-loading="loading"  ref="table" @selection-change="handleSelectionChange" :data="shareList">

      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="密码" align="center"  key="sharePwd" prop="sharePwd" />
      <el-table-column label="分享时间" align="center"  key="creationtime"  prop="creationtime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="有效时间" align="center" prop="hours" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.hours==0">永久</span>
          <span v-if="scope.row.hours==24">1天</span>
          <span v-if="scope.row.hours==72">3天</span>
          <span v-if="scope.row.hours==168">7天</span>
          <span v-if="scope.row.hours==360">15天</span>
          <span v-if="scope.row.hours==720">30天</span>
        </template>
      </el-table-column>
      <el-table-column label="分享地址" align="center"  key="sharefileurl" prop="sharefileurl" />
     
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
           <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"  @pagination="getList"/>

     <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="workorgform" :model="workorgform" :rules="rules" label-width="80px">
       
            
        
            <el-form-item label="分享有效期" prop="hours">
               <el-select v-model="workorgform.hours" placeholder="请选择时间" clearable>
                    <el-option
                    v-for="item in timeoptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                    </el-option>
                </el-select>


            </el-form-item>
          
       
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">分享</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listWorkorg,deptTreeSelect} from "@/api/system/user";
import { getInstitution } from "@/api/institution/institution";
import {listShare,addShare,delShare} from "@/api/institution/share";

export default {
  name: "AuthRole",
  dicts: ['system_worktype','system_job'],
  data() {
    return {
       // 遮罩层
      loading: true,
      // 分页信息
      total: 0,
      pageNum: 1,
      pageSize: 10,
      //制度化管理
      institutionId:undefined,
      // 选中角色编号
      roleIds:[],
      // 角色信息
      roles: [],
      // 制度化信息
      form: {},
      //分享列表
      shareList:[],
       // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      //用户部门信息
      workorgform:{},
      // 是否显示弹出层
      open:false,
      //有效期
      timeoptions:[{
          value: 0,
          label: '永久'
        }, {
          value: 24,
          label: '1天'
      }, {
          value: 72,
          label: '3天'
      }, {
          value: 168,
          label: '7天'
      }, {
          value: 360,
          label: '15天'
      }, {
          value: 720,
          label: '30天'
      }],
      //弹出标题
      title:undefined,

      // 表单校验
      rules: {

      }
    };
  },
  created() {
    const institutionId = this.$route.params && this.$route.params.institutionId;

    if (institutionId) {
      this.institutionId=institutionId
      this.getInstitution();
      this.getList();
    }
  },
  methods: {

    getList(){
      this.loading = true;
      this.queryParams.institutionId=this.institutionId
      listShare(this.queryParams).then((response)=>{
          this.shareList = response.data;
          for(let i=0; i<this.shareList.length; i++){
            let sharefileurl =  "http://www.xdbg.cn/xdemui/xj/share/file/"+this.shareList[i].shareUrl
            this.shareList[i].sharefileurl=sharefileurl
          }
          this.total = response.totalCount;
          this.loading = false;
      })
    },
    getInstitution(){
      getInstitution(this.institutionId).then((response) => {
        this.form = response.data;
        
      });
    },




    // 表单重置
    reset() {
      this.workorgform = {
        shareId: undefined,
        institutionId: undefined,
        shareTime: undefined,
        hours: undefined,
      };
      this.resetForm("workorgform");
    },
    //新增用户部门
    handleAdd(){
        this.reset();
        this.open = true;
        this.title = "新增分享";
    },

     /** 提交按钮 */
    submitForm(){
      console.log(this.workorgform.hours)
      if(this.workorgform.hours != undefined ){
            this.workorgform.institutionId=this.institutionId
            if(this.form.filepaths != undefined){
              let fileshares = []
              for(let i=0; i<this.form.filepaths.length; i++){
                  let fileshare = {};
                  fileshare.fileId=this.form.filepaths[i].fileId
                  fileshares.push(fileshare)
              }
              this.workorgform.fileshares=fileshares
            }
            
          
            addShare(this.workorgform).then(response => {
              this.$modal.msgSuccess("分享成功");
              this.open = false;
              this.getList();
            });
      }else{
           this.$modal.msgError("时间没有选择");
      }

    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const shareId = row.shareId || this.ids;
      this.$modal.confirm('是否确认删除分享?').then(function() {
        return delShare(shareId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },


    /** 单击选中行数据 */
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.roleIds = selection.map((item) => item.roleId);
    },
    // 保存选中的数据编号
    getRowKey(row) {
      return row.roleId;
    },


  },
};
</script>