html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
}
.fullTable {
    width: 100%;
    height: 100%;
    table-layout: fixed;
}
.left {
    width: 400px;
    border: 1px solid #000;
}
.middle {
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    display: -webkit-box;
    -webkit-box-pack: center;
    -webkit-box-orient: vertical;
    text-align: center;
}
.right {
    width: 350px;
    padding: 10px;
    border: 1px solid #000;
}
.bottom {
    width: 100%;
    min-height: 100px;
    font-size: 12px;
    border-bottom: 1px solid #000;
}

.left-one {
    height: 85px;
}

.left-one-left {
    float: left;
    width: 30%;
    margin: 5px 5px 0 15px;
}

.left-one-left img {
    width: 80%;
}

.left-one-right {
    float: left;
    width: 45%;
    margin: 30px 0 0 0;
    min-height: 67px;
    font-size: 24px;
}

.left-one-right select {
    width: 160px;
    height: 25px;
    margin-top: 3px;
}

.left-two {
    height: 300px;
}

.left-two-lable {
    width: 30%;
    height: 30px;
    margin: 3px 3px 0px 3px;
    display: inline-block;
}

.left-two-lable-set {
    width: 35%;
    height: 30px;
    text-align: center;
    margin: 3px 3px 0px 3px;
    display: inline-block;
}

.left-two-select {
    width: 220px;
    height: 30px;
    margin: 3px 3px 3px 3px;
}

#bottom-div {
    display: flex;
    flex-direction: row;
    align-items: center;
    overflow-x: scroll;
    height: 100%;
}

#fileList {
    width: 290px;
    height: 255px;
}

#autoCaptureProgress {
    width: 100px;
}

#clearImage {
    float: right;
    margin-top: 10px;
}

#td2 {
    height: 100%;
}
#td4 {
    height: 80px;
}
#td4 div {
    float: left;
    width: 50%;
    vertical-align: top;
    text-align: center;
}

#tbFile, #tbFile tr th, #tbFile tr td {
    border: 1px solid #959b9e;
}
#tbFile a {
    background-color: #2cc368;
    color: #fff;
    border-color: #4cae4c;
    font-size: 12px;
    border-radius: 4px;
    padding: 3px 6px;
    text-decoration: none;
    cursor: pointer;
}
#tbFile {
    width: 340px;
    min-height: 25px;
    line-height: 25px;
    text-align: center;
    border-collapse: collapse;
    padding: 2px;
    font-size: 14px;
}

#divView {
    position: absolute;
    height: 100%;
    width: 100%;
    left: 0px;
    top: 0px;
    z-index: 1999;
    background-color: lightgray;
}
#divView div {
    float: right;
}

#divVideoCtrl, #divCameraCtrl {
    margin: 10px;
    padding: 10px 5px;
    border: 1px solid #9d9d9d;
}
.valAdj {
    width: 140px;
}
.valDisp {
    width: 45px;
}

.ctrlTable tr td:first-child {
    width:100px;
    text-align: right;
}
.ctrlTable tr td:nth-child(2) {
    width:180px;
}
.ctrlTable tr td:nth-child(2) {
    width:45px;
}
.ctrlTable tr td:nth-child(2) {
    width:45px;
}
.divAuto {
    float: right;
    width: 28px;
    font-size: 14px;
    margin-top: 2px;
}

label[disabled], input[disabled] {
    color: gray;
}
#btnDefaultVideo, #btnDefaultCamera {
    margin-top: 6px;
}