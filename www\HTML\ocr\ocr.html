<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>OCR与合并PDF</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../CSS/cropBox.css"/>
    <link rel="stylesheet" href="../../CSS/ocr/ocr.css"/>

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../JS/common/html5shiv.min.js"></script>
    <script src="../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<div id="divTitle" style="padding:10px; background-color: #dddddd;">
    <h4>OCR及区域OCR 演示</h4>
    <p style="font-size:14px; margin:0;">这个页面演示了OCR识别（支持指定识别区域）、以及合并PDF文件。<br>
首先进行“拍照”，然后在“拍照文件列表”中点击“查看”，在页面中间会显示相应的图片文件。在显示的图片中，可用鼠标框选要识别的区域，一个图片允许有多个识别区域。
识别区域会显示在表格中，显示的格式为：left,top,right,bottom,absolute,type。 其中absolute表示left、top、right、bottom四个数字表示的是否为图片的绝对位置。
type表示识别区域的类型：0文字区；1插图照片区；2表格区；3条码区；4手写文字区。</p>
</div>

<div id="divDev" class="col-md-3" style="background-color:#eaeaea; min-height:720px; padding:10px;">
    <div>
        <label style="width:70px;">设备名称:</label>
        <select id="cameraInfo" style="width:160px;" onchange="switchCameraInfo_Fun()"></select>
        <br>
        <label style="width:70px;">视频格式:</label>
        <select id="mediaType"  style="width:160px;" onchange="setCameraInfo_Fun()"></select>
        <br>
        <label style="width:70px;">分 辨 率:</label>
        <select id="resolution" style="width:160px;" onchange="setCameraInfo_Fun()"></select>
        <br>
        <label style="width:70px;">旋转方式:</label>
        <select id="rotateType" style="width:160px;" onchange="OnRotateChange()">
            <option value="0">不旋转</option>
            <option value="90">旋转90度</option>
            <option value="180">旋转180度</option>
            <option value="270">旋转270度</option>
            <option value="361" id="opOcrDirection" disabled>自动文字方向</option>
        </select>
        <br>
        <label style="width:70px;">裁切方式:</label>
        <select id="crop_type" style="width:160px;" onchange="setCameraImageInfo_Fun()">
            <option value="0">不裁切</option>
            <option value="1" selected>单图裁切</option>
            <option value="2">多图裁切</option>
            <option value="6">桌面裁切</option>
        </select>
        <br>
    </div>
    <br>
    <div class="row text-center">
        <label style="width:70px;">视频:</label><br>
        <img id="pic"/>
    </div>
    <br>
    <div class="row text-center">
        <button id="btnCapture" type="button" class="btn btn-primary" onclick="cameraCapture_Fun()">拍 照</button>
    </div>
    <br><br>
    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none;margin-bottom:0; word-break: break-all;"></div>
</div>
<div class="col-md-6">
    <div class="row text-center" style="padding: 10px;">
        <label>图片展示，可用鼠标框选出要识别的区域:</label><br>
        <img id="imgFile"/>
    </div>
</div>
<div id="divTab" class="col-md-3" style="background-color:#eaeaea; min-height:720px; padding:10px;">
    <label style="width:70px;">OCR语言:</label>
    <select id="language" style="width:250px;"></select><br>
    <label style="width:123px;">OCR输出文件格式:</label>
    <select id="docType" style="width:198px;"></select><br>
    <label style="width:100px;">拍照文件列表:</label>
    <button id="btnOCR" type="button" class="btn btn-primary" onclick="convertToFile()" style="margin-left:10px">文字识别</button>
    <button id="btnMergePDF" type="button" class="btn btn-primary" onclick="mergePDF()" style="margin-left:10px">合并PDF</button>
    <button id="btnClearFile" type="button" class="btn btn-primary" onclick="clearImageFile()" style="margin-left:10px">清空文件</button>
    <table id="tbFile"><tr><th>文件名</th><th>识别区域</th><th>操作</th></tr></table>
</div>

<audio id="audioCapture">
    <source src="../../audio/Capture.wav" type="audio/wav">
</audio>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../langs/zhCN.js"></script>
<script src="../../JS/cropBox.js"></script>
<script src="../../JS/ocr/ocr.js"></script>
</body>
</html>