import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询设备列表
export function listDevice(query) {
    return request({
      url: '/inspectdevice/doQuery',
      method: 'post',
      data: query
    })
}


// 查询设备详细
export function getDevice(deviceId) {
    return request({
      url: '/inspectdevice/' + parseStrEmpty(deviceId),
      method: 'get'
    })
}


// 新增设备
export function addDevice(data) {
    return request({
      url: '/inspectdevice/doAdd',
      method: 'post',
      data: data
    })
}

// 修改设备
export function updateDevice(data) {
  return request({
    url: '/inspectdevice/doEdit',
    method: 'post',
    data: data
  })
}

// 删除设备
export function delDevice(deviceId) {
    return request({
      url: '/inspectdevice/doDelete/' + deviceId,
      method: 'post'
    })
}
  