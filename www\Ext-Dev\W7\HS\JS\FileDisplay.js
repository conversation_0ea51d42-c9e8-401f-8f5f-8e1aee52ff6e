var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}


/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "GetLocationDirFile":
            if (typeof(jsonObj.dirName) == "undefined" || jsonObj.dirName == "") {
                // 显示目录
                displayFileType(jsonObj);
            } else {
                // 显示文件列表
                displayFiles(jsonObj);
            }
            break;
        case "DisplayLocalFile":
            if (jsonObj.result == 0)
                displayOutputInfo(2, "展示成功");
            else
                displayOutputInfo(3, "展示失败");
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function GetFileType(type) {
    var json = {func:"GetLocationDirFile",to:1, reqId: new Date().getTime()};
    if (type.length > 0) {
        json.dirName = type;
    }
    SendJson(json);
}

function displayFileType(v) {
    if (v.result == 0) {
        var ul = $('#fileTypes');
        ul.empty();
        for (var i = 0; i < v.files.length; i++) {
            var h = '<li id="li_';
            h += v.files[i];
            h += '" role="presentation"><a href="javascript:void(0)" onclick="switchType()">';
            h += v.files[i];
            h += '</a></li>';
            ul.append(h);
        }
        $('#fileTypes').children(":first").attr("class", "active");
        GetFileType(v.files[0]);
    } else {
        displayOutputInfo(3, v.func + "<br>" + v.errorMsg);
    }
}

function displayFiles(v) {
    var tb = $('#tabBody');
    tb.empty();
    for (var i = 0; i < v.files.length; i++) {
        var h = '<tr><td>';
        h += v.files[i];
        h += '</td><td><button type="button" class="btn btn-sm btn-primary" onclick="playFile()">播放</button></td></tr>';
        tb.append(h);
    }
    $('#fileTypes .active').removeClass('active');
    $('#li_' + v.dirName).addClass('active');
}

// 在终端设备上展示文件
function playFile(e) {
    e = e || window.event;
    var btn = e.target || e.srcElement;
    // console.log(btn.innerText);
    displayOutputInfo(0);
    var fileName = $(btn).parent().prev().text();
    SendJson({func:"DisplayLocalFile", to:1, fileName:fileName, reqId: new Date().getTime()});
}

function switchType(e) {
    e = e || window.event;
    var tab = e.target || e.srcElement;
    displayOutputInfo(0);
    console.log(tab.innerText);
    GetFileType(tab.innerText);
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

$(function () {
    // 打开多功能终端上的人像摄像头
    GetFileType("");
});

var _uploadBtn = document.getElementById('uploadBtn');
var _uploadBox = document.getElementById('forexIO_file');
var _fileData = "";
//读取文件并转换成base64
document.getElementById('forexIO_file').onchange = function(){
    _fileData = "";
    _uploadBtn.disabled = true;
    reads(this.files[0],function(base64Data){   //获取图片的base64格式，显示
        var dataImages = base64Data.split(",");
        _fileData = dataImages[1];
        _uploadBtn.disabled = false;
    });
}
function reads(_file,callback){
    var reader = new FileReader();
    $(_uploadBox).parent().prev().prop("placeholder", _file.name);
    reader.readAsDataURL(_file);
    reader.onload = function(){
        callback(reader.result);
    };
}

function uploadFile (){
    if(_uploadBox.files.length == 0 || _fileData.length == 0) {
        _uploadBtn.disabled = true;
        return;
    }
    SendJson({func:"UploadBase64File", to:1, name:_uploadBox.files[0].name, fileBase64:_fileData, reqId:new Date().getTime()});
}