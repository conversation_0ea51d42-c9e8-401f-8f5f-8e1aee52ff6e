var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}
initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "ReadFingerInfo":
            DisplayFingerInfo(jsonObj);
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                displayOutputInfo(3, jsonObj.func + "<br>" + jsonObj.errorMsg);
            }
            break;
    }
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}


function DisplayFingerInfo(v) {
    // json数据格式
    // {"functionName": "ReadFingerInfo", "reqId": "", "result": 0, "fingerFeature": "...", "fingerImageBase64": "...", "mime": "image/bmp"}
    if (v.result == 0) {
        document.getElementById("pic").src = "data:" + v.mime + ";base64," + v.fingerImageBase64;
        displayOutputInfo(2, "读取指纹成功");
    } else {
        displayOutputInfo(3, "读取指纹失败：" + v.errorMsg);
    }
}

function ReadFingerInfo() {
    // 提示信息显示
    displayOutputInfo(1, "指纹读取中...... 请在多功能终端上录入指纹并点击“确认”按钮");
    // 读取指纹, to属性为1，表示经"ws://127.0.0.1:9000"转发给"安卓多功能终端"上的服务来处理
    SendJson({func:"ReadFingerInfo", to:1, reqId: new Date().getTime()});
}
