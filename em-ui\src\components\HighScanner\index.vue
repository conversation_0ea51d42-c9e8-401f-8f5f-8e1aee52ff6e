<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="90%" top="5vh" append-to-body @close="handleClose" class="scanner-dialog">
    <div class="doc-scanner-container">
      <!-- 状态信息显示 - 移到最上面 -->
      <div v-if="outputInfo.show" :class="['status-alert', 'status-' + outputInfo.type]">
        <i :class="getStatusIcon(outputInfo.type)"></i>
        <span v-html="outputInfo.message"></span>
        <i class="el-icon-close close-btn" @click="outputInfo.show = false"></i>
      </div>

      <!-- 设备控制区域 -->
      <div class="control-panel">
        <div class="panel-header">
          <i class="el-icon-camera"></i>
          <span>设备控制</span>
        </div>

        <div class="control-grid">
          <!-- 第一行：基础设备设置 -->
          <div class="control-group">
            <div class="control-item">
              <label class="control-label">设备名称</label>
              <el-select v-model="selectedCamera" @change="switchCamera" size="small" class="control-select">
                <el-option v-for="(device, index) in cameraDevices" :key="index" :label="device.devName" :value="device.id" />
              </el-select>
            </div>

            <div class="control-item">
              <label class="control-label">视频格式</label>
              <el-select v-model="selectedMediaType" @change="reOpenCamera" size="small" class="control-select">
                <el-option v-for="(type, index) in mediaTypes" :key="index" :label="type.mediaType" :value="index" />
              </el-select>
            </div>

            <div class="control-item">
              <label class="control-label">分辨率</label>
              <el-select v-model="selectedResolution" @change="reOpenCamera" size="small" class="control-select">
                <el-option v-for="(resolution, index) in resolutions" :key="index" :label="resolution" :value="index" />
              </el-select>
            </div>

            <div class="control-item">
              <el-button :type="cameraOpened ? 'danger' : 'primary'" @click="openCloseCamera" size="small" :icon="cameraOpened ? 'el-icon-video-camera-solid' : 'el-icon-video-camera'">
                {{ cameraOpened ? "关闭摄像头" : "打开摄像头" }}
              </el-button>
            </div>
          </div>

          <!-- 第二行：图像处理设置 -->
          <div class="control-group">
            <div class="control-item">
              <label class="control-label">旋转</label>
              <el-select v-model="rotateType" @change="setCameraInfo" size="small" class="control-select">
                <el-option label="不旋转" :value="0" />
                <el-option label="旋转90度" :value="90" />
                <el-option label="旋转180度" :value="180" />
                <el-option label="旋转270度" :value="270" />
              </el-select>
            </div>

            <div class="control-item">
              <label class="control-label">裁切</label>
              <el-select v-model="cropType" @change="setCameraInfo" size="small" class="control-select">
                <el-option label="不裁切" :value="0" />
                <el-option label="单图裁切" :value="1" />
                <el-option label="多图裁切" :value="2" />
              </el-select>
            </div>

            <div class="control-item">
              <label class="control-label">图像效果</label>
              <el-select v-model="imageType" @change="setCameraImageInfo" size="small" class="control-select">
                <el-option label="彩色原色" :value="0" />
                <el-option label="灰度图片" :value="1" />
                <el-option label="黑白文档" :value="2" />
                <el-option label="彩色文档" :value="3" />
                <el-option label="红印文档" :value="4" />
                <el-option label="蓝印文档" :value="5" />
                <el-option label="彩色照片" :value="6" />
                <el-option label="票据增强" :value="7" />
              </el-select>
            </div>

            <div class="control-item">
              <label class="control-label">文件格式</label>
              <el-select v-model="imgFileType" @change="setCameraImageInfo" size="small" class="control-select">
                <el-option label="JPG" :value="0" />
                <el-option label="PNG" :value="1" />
                <el-option label="TIF" :value="2" />
              </el-select>
            </div>

            <div class="control-item">
              <el-button type="success" @click="captureDoc" size="small" icon="el-icon-camera"> 拍照 </el-button>
            </div>
          </div>

          <!-- 第三行：摄像头参数调节 -->
          <div class="control-group slider-group">
            <div class="slider-item">
              <label class="slider-label">亮度</label>
              <el-slider v-model="brightness" :min="1" :max="100" @change="changePUValue('9963776', $event)" class="param-slider" show-input :show-input-controls="false" />
            </div>

            <div class="slider-item">
              <label class="slider-label">对比度</label>
              <el-slider v-model="contrast" :min="1" :max="100" @change="changePUValue('9963777', $event)" class="param-slider" show-input :show-input-controls="false" />
            </div>

            <div class="slider-item">
              <label class="slider-label">饱和度</label>
              <el-slider v-model="saturation" :min="1" :max="100" @change="changePUValue('9963778', $event)" class="param-slider" show-input :show-input-controls="false" />
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 视频显示区域 -->
        <div class="video-area">
          <!-- 自动拍照和水印设置合并 -->
          <div class="setting-card">
            <div class="card-header">
              <i class="el-icon-setting"></i>
              <span>拍照设置</span>
            </div>
            <div class="card-content">
              <div class="setting-group">
                <div class="setting-row">
                  <label class="setting-label">拍照模式</label>
                  <el-select v-model="autoCapture" @change="onChangeAutoCapture" size="small" class="setting-select">
                    <el-option label="不开启" :value="-1" />
                    <el-option label="自动检测图像变化" :value="0" />
                    <el-option label="定时拍摄" :value="1" />
                  </el-select>
                  <el-input-number v-model="autoParam" :min="2" :max="50" size="small" controls-position="right" class="setting-number" style="margin-left: 8px" />
                  <span class="unit-text">秒</span>
                </div>
                <div class="setting-row">
                  <el-checkbox v-model="watermarkEnabled" @change="onChangeWatermark" class="watermark-checkbox"> 启用水印 </el-checkbox>
                  <el-color-picker v-model="watermarkColor" size="small" :disabled="!watermarkEnabled" />
                  <el-select v-model="watermarkLayout" size="small" class="setting-select-small" :disabled="!watermarkEnabled">
                    <el-option label="左上角" :value="0" />
                    <el-option label="中间" :value="1" />
                    <el-option label="斜对角线" :value="2" />
                    <el-option label="右下角" :value="3" />
                  </el-select>
                </div>
                <div class="setting-row" v-if="watermarkEnabled" style="display: flex">
                  <el-input v-model="watermarkText" size="small" placeholder="水印文本" />
                  <el-input-number v-model="watermarkSize" :min="20" :max="200" size="small" controls-position="right" class="setting-number" style="width: 200px; margin-left: 8px" />
                </div>
              </div>
            </div>
          </div>
          <div class="video-container">
            <img ref="videoImg" :src="videoSrc" class="video-display" alt="文档摄像头视频" @error="handleVideoError" />
            <div class="video-overlay" v-if="!videoSrc">
              <i class="el-icon-video-camera"></i>
              <p>等待摄像头连接...</p>
            </div>
          </div>
        </div>

        <!-- 右侧控制面板 -->
        <div class="right-panel">
          <!-- 文件操作 -->
          <div class="setting-card">
            <div class="card-header">
              <i class="el-icon-document"></i>
              <span>文件操作</span>
            </div>
            <div class="card-content">
              <div class="setting-group">
                <div class="action-buttons">
                  <el-button icon="el-icon-delete" @click="clearImageFile">清空文件</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 文件列表 -->
          <div class="file-card">
            <div class="card-header">
              <i class="el-icon-folder"></i>
              <span>已拍摄文件 ({{ capturedFiles.length }})</span>
            </div>
            <div class="file-list">
              <el-table :data="capturedFiles" style="width: 100%" max-height="200" size="small" :header-cell-style="{ background: '#f5f7fa', color: '#606266' }">
                <el-table-column prop="fileName" label="文件名" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <i class="el-icon-document"></i>
                    <span style="margin-left: 5px">{{ scope.row.fileName }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center">
                  <template slot-scope="scope">
                    <el-button @click="viewFile(scope.row)" type="text" size="mini" icon="el-icon-view">查看</el-button>
                    <el-button @click="deleteFile(scope.$index)" type="text" size="mini" icon="el-icon-delete" style="color: #f56c6c">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div v-if="capturedFiles.length === 0" class="empty-state">
                <i class="el-icon-picture-outline"></i>
                <p>暂无拍摄文件</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片查看对话框 -->
    <el-dialog title="文件预览" :visible.sync="imageViewVisible" width="80%" center>
      <div class="image-preview">
        <img :src="viewImageSrc" alt="查看图片" />
      </div>
    </el-dialog>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel" size="medium">
        <i class="el-icon-close"></i>
        取 消
      </el-button>
      <el-button type="primary" @click="handleConfirm" :loading="uploading" size="medium">
        <i class="el-icon-check"></i>
        {{ uploading ? "上传中..." : "确 定" }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "HighScanner",
  props: {
    showScanner: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      title: "文档扫描",
      dialogVisible: false,

      // WebSocket相关
      ws: null,
      connected: false,
      wsUrl: "",

      // 设备信息
      cameraDevices: [],
      selectedCamera: null,
      mediaTypes: [],
      selectedMediaType: 0,
      resolutions: [],
      selectedResolution: 0,
      devNum: -1,
      cameraOpened: false,

      // 摄像头参数
      rotateType: 0,
      cropType: 0,
      imageType: 0,
      imgFileType: 0,
      brightness: 50,
      contrast: 50,
      saturation: 50,

      // 视频显示
      videoSrc: "",
      videoWidth: 0,
      videoHeight: 0,

      // 自动拍照
      autoCapture: -1,
      autoParam: 5,
      autoTimer: null,

      // 水印设置
      watermarkEnabled: false,
      watermarkColor: "#7A7A7A",
      watermarkLayout: 0,
      watermarkSize: 60,
      watermarkText: "机密文件",

      // 文件管理
      capturedFiles: [],
      filePath: null,

      // 状态信息
      outputInfo: {
        show: false,
        type: "info",
        message: "",
      },

      // 图片查看
      imageViewVisible: false,
      viewImageSrc: "",

      // 上传相关
      uploading: false,
      finalFileUrl: null,
      uploadRequestMap: new Map(), // 跟踪上传请求
    };
  },

  computed: {},

  watch: {
    showScanner: {
      handler(newVal) {
        this.dialogVisible = newVal;
        if (newVal) {
          this.initWebSocket();
        } else {
          this.closeWebSocket();
        }
      },
      immediate: true,
    },

    dialogVisible(newVal) {
      if (!newVal) {
        this.$emit("update:showScanner", false);
      }
    },
  },

  mounted() {
    this.initWsUrl();
  },

  beforeDestroy() {
    this.closeWebSocket();
    if (this.autoTimer) {
      clearInterval(this.autoTimer);
    }
  },

  methods: {
    // 初始化WebSocket URL
    initWsUrl() {
      const protocol = location.protocol === "https:" ? "wss://" : "ws://";
      const hostname = location.hostname === "" ? "127.0.0.1" : location.hostname;
      const port = location.port === "" ? "9000" : location.port;
      this.wsUrl = `${protocol}${hostname}:${port}`;
      console.log("WebSocket URL:", this.wsUrl);
    },

    // 初始化WebSocket连接
    initWebSocket() {
      if (this.connected) return;

      try {
        this.ws = new WebSocket(this.wsUrl);

        this.ws.onopen = () => {
          this.connected = true;
          console.log("WebSocket连接成功");
          // 获取摄像头信息
          this.sendJson({ func: "GetCameraInfo", to: 1, reqId: new Date().getTime() });
        };

        this.ws.onclose = () => {
          this.connected = false;
          console.log("WebSocket连接关闭");
        };

        this.ws.onmessage = (event) => {
          if (typeof event.data === "string") {
            this.onTextMessage(event.data);
          } else {
            this.onBinaryMessage(event);
          }
        };

        this.ws.onerror = (error) => {
          console.error("WebSocket错误:", error);
          this.displayOutputInfo("error", "未连接websocket服务器，请确保已运行服务端!");
        };
      } catch (error) {
        console.error("WebSocket初始化失败:", error);
        this.displayOutputInfo("error", "WebSocket初始化失败");
      }
    },

    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.ws) {
        // 关闭摄像头
        if (this.devNum >= 0) {
          this.openCloseCamera(false);
        }
        this.ws.close();
        this.ws = null;
        this.connected = false;
      }
    },

    // 发送JSON消息
    sendJson(json) {
      if (this.connected && this.ws) {
        this.ws.send(JSON.stringify(json));
      } else {
        console.warn("WebSocket未连接，尝试重新连接...");
        this.initWebSocket();
        // 延迟发送
        setTimeout(() => {
          if (this.connected && this.ws) {
            this.ws.send(JSON.stringify(json));
          }
        }, 1000);
      }
    },

    // 处理文本消息
    onTextMessage(msg) {
      try {
        const jsonObj = JSON.parse(msg);
        console.log("收到消息:", jsonObj);

        switch (jsonObj.func) {
          case "GetCameraInfo":
            this.handleCameraInfo(jsonObj);
            break;
          case "OpenCamera":
            this.handleOpenCamera(jsonObj);
            break;
          case "CloseCamera":
            this.handleCloseCamera(jsonObj);
            break;
          case "GetCameraVideoBuff":
            this.handleCameraVideo(jsonObj);
            break;
          case "GetCameraPuValue":
            this.handlePuValue(jsonObj);
            break;
          case "CameraCapture":
            this.handleCameraCapture(jsonObj);
            break;
          case "FileToBase64":
            this.handleFileView(jsonObj);
            break;
          default:
            console.log("未处理的消息:", msg);
            if (jsonObj.result !== 0) {
              this.displayOutputInfo("error", `${jsonObj.func}<br>${jsonObj.errorMsg}`);
            }
            break;
        }
      } catch (error) {
        console.error("解析消息失败:", error);
      }
    },

    // 处理二进制消息
    onBinaryMessage(event) {
      console.log("收到二进制消息:", event);
    },

    // 处理摄像头信息
    handleCameraInfo(data) {
      if (data.result === 0) {
        this.cameraDevices = data.devInfo;
        if (this.cameraDevices.length === 0) {
          this.displayOutputInfo("error", "获取设备信息时，返回的devInfos为空.");
          return;
        }
        this.updateCameraOptions();
        this.switchCamera();
      } else {
        this.displayOutputInfo("error", `${data.func}<br>${data.errorMsg}`);
      }
    },

    // 更新摄像头选项
    updateCameraOptions() {
      if (this.cameraDevices.length > 0) {
        this.selectedCamera = this.cameraDevices[0].id;
        this.updateMediaTypes();
      }
    },

    // 更新媒体类型
    updateMediaTypes() {
      const selectedDevice = this.cameraDevices.find((device) => device.id === this.selectedCamera);
      if (selectedDevice) {
        this.mediaTypes = selectedDevice.mediaTypes;
        this.selectedMediaType = 0;
        this.updateResolutions();
      }
    },

    // 更新分辨率
    updateResolutions() {
      if (this.mediaTypes.length > this.selectedMediaType) {
        this.resolutions = this.mediaTypes[this.selectedMediaType].resolutions;
        this.selectedResolution = 0;
      }
    },

    // 处理打开摄像头
    handleOpenCamera(data) {
      if (data.result === 0 || data.result === 9 || data.result === -2) {
        this.devNum = 0;
        this.cameraOpened = true;
        let reqId = new Date().getTime();
        // 获取视频流
        this.sendJson({ func: "GetCameraVideoBuff", to: 1, devNum: this.devNum, enable: "true", reqId: reqId++ });
        // 获取摄像头参数
        this.sendJson({ func: "GetCameraPuValue", to: 1, devNum: this.devNum, reqId: reqId });
        this.setCameraInfo();
      } else {
        this.displayOutputInfo("error", `${data.func}<br>${data.errorMsg}`);
      }
    },

    // 处理关闭摄像头
    handleCloseCamera(data) {
      this.cameraOpened = false;
      this.devNum = -1;
    },

    // 处理摄像头视频
    handleCameraVideo(data) {
      if (this.videoWidth !== data.width || this.videoHeight !== data.height) {
        this.videoWidth = data.width;
        this.videoHeight = data.height;
      }
      this.videoSrc = `data:${data.mime};base64,${data.imageBase64Str}`;
    },

    // 处理摄像头参数值
    handlePuValue(data) {
      if (data.result === 0 && data.puValues) {
        data.puValues.forEach((pu) => {
          switch (pu.id) {
            case 9963776: // 亮度
              this.brightness = pu.value;
              break;
            case 9963777: // 对比度
              this.contrast = pu.value;
              break;
            case 9963778: // 饱和度
              this.saturation = pu.value;
              break;
          }
        });
      } else {
        this.displayOutputInfo("error", `获取摄像头参数失败<br>${data.errorMsg}`);
      }
    },

    // 处理拍照结果
    handleCameraCapture(data) {
      if (data.result === 0) {
        // 播放拍照音效（如果需要）
        data.imagePath.forEach((imagePath) => {
          this.addCapturedFile(imagePath);
        });
        this.displayOutputInfo("success", "拍照成功");
      } else {
        this.displayOutputInfo("error", `拍照失败: ${data.errorMsg}`);
      }
    },

    // 添加拍照文件
    addCapturedFile(filePath) {
      const pos = filePath.lastIndexOf("\\");
      if (this.filePath === null) {
        this.filePath = filePath.substring(0, pos);
      }
      const fileName = filePath.substring(pos + 1);
      this.capturedFiles.push({
        fileName: fileName,
        filePath: filePath,
      });
    },

    // 处理文件查看
    handleFileView(data) {
      // 检查是否是上传请求
      const uploadRequest = this.uploadRequestMap.get(data.reqId);
      if (uploadRequest && uploadRequest.isUpload) {
        this.uploadRequestMap.delete(data.reqId);
        this.uploadBase64ToServer(data.imgBase64Str, uploadRequest.filePath);
      } else {
        this.viewImageSrc = `data:${data.mime};base64,${data.imgBase64Str}`;
        this.imageViewVisible = true;
      }
    },

    // 切换摄像头
    switchCamera() {
      const reqId = new Date().getTime();
      if (this.devNum >= 0) {
        // 关闭之前的摄像头
        this.sendJson({ func: "CloseCamera", reqId: reqId, devNum: this.devNum });
      }

      this.updateMediaTypes();
      this.updateResolutions();

      // 开启新摄像头
      this.devNum = this.selectedCamera || 0;
      this.sendJson({
        func: "OpenCamera",
        to: 1,
        devNum: this.devNum,
        mediaNum: this.selectedMediaType,
        resolutionNum: this.selectedResolution,
        reqId: reqId + 1,
      });
    },

    // 重新打开摄像头
    reOpenCamera() {
      this.openCloseCamera(true);
    },

    // 打开/关闭摄像头
    openCloseCamera(open) {
      const shouldOpen = typeof open !== "undefined" ? open : !this.cameraOpened;
      let reqId = new Date().getTime();

      if (this.devNum >= 0) {
        // 先关闭当前摄像头
        this.sendJson({ func: "GetCameraVideoBuff", to: 1, devNum: this.devNum, enable: "false", reqId: reqId++ });
        this.sendJson({ func: "CameraAutoCapture", to: 1, devNum: this.devNum, enable: false, reqId: reqId++ });
        this.sendJson({ func: "CloseCamera", to: 1, devNum: this.devNum, reqId: reqId++ });
        this.devNum = -1;
      }

      if (shouldOpen) {
        this.sendJson({
          func: "OpenCamera",
          to: 1,
          devNum: 0,
          mediaNum: this.selectedMediaType,
          resolutionNum: this.selectedResolution,
          reqId: reqId++,
        });
      }
    },

    // 设置摄像头信息
    setCameraInfo() {
      this.displayOutputInfo("info", "");
      this.sendJson({
        func: "SetCameraImageInfo",
        to: 1,
        devNum: this.devNum,
        cropType: this.cropType,
        rotate: this.rotateType,
        reqId: new Date().getTime(),
      });
    },
    // 设置摄像头图像信息
    setCameraImageInfo() {
      this.sendJson({
        func: "SetCameraImageInfo",
        to: 1,
        devNum: 1,
        imageType: this.imageType,
        reqId: new Date().getTime(),
      });
    },

    // 改变摄像头参数值
    changePUValue(id, value) {
      this.sendJson({
        func: "SetCameraPuValue",
        to: 1,
        devNum: this.devNum,
        puValue: { id: parseInt(id), value: parseInt(value) },
        reqId: new Date().getTime(),
      });
    },

    // 拍照
    captureDoc() {
      this.displayOutputInfo("info", "");
      const cmd = {
        func: "CameraCapture",
        to: 1,
        devNum: this.devNum,
        mode: "base64",
        imgFileType: this.imgFileType,
        reqId: new Date().getTime(),
      };

      if (this.watermarkEnabled) {
        cmd.watermark = {
          layout: this.watermarkLayout,
          fontSize: this.watermarkSize,
          color: this.watermarkColor,
          text: this.watermarkText,
        };
      }

      this.sendJson(cmd);
    },

    // 自动拍照设置变化
    onChangeAutoCapture() {
      if (this.autoTimer) {
        clearInterval(this.autoTimer);
        this.autoTimer = null;
      }

      if (this.autoCapture >= 0) {
        this.autoTimer = setInterval(() => {
          this.captureDoc();
        }, this.autoParam * 1000);
      }
    },

    // 水印设置变化
    onChangeWatermark() {
      // 水印设置已通过v-model和disabled属性处理
    },

    // 获取图片文件数组
    getImageFileArray() {
      return this.capturedFiles.map((file) => file.filePath);
    },

    // 清空文件
    clearImageFile() {
      this.capturedFiles = [];
      this.filePath = null;
    },

    // 查看文件
    viewFile(file) {
      this.sendJson({
        func: "FileToBase64",
        filePath: file.filePath,
        reqId: new Date().getTime(),
      });
    },

    // 删除文件
    deleteFile(index) {
      this.capturedFiles.splice(index, 1);
    },

    // 显示输出信息
    displayOutputInfo(type, message) {
      if (type && message) {
        this.outputInfo = {
          show: true,
          type: type,
          message: message,
        };

        // 3秒后自动隐藏成功和信息提示
        if (type === "success" || type === "info") {
          setTimeout(() => {
            this.outputInfo.show = false;
          }, 3000);
        }
      } else {
        this.outputInfo.show = false;
      }
    },

    // 上传文件到服务器
    async uploadFileToServer(filePath) {
      try {
        this.uploading = true;

        // 通过WebSocket获取文件的base64内容然后上传
        const reqId = new Date().getTime();
        this.uploadRequestMap.set(reqId, { filePath, isUpload: true });

        this.sendJson({
          func: "FileToBase64",
          filePath: filePath,
          reqId: reqId,
        });
      } catch (error) {
        console.error("上传文件失败:", error);
        this.displayOutputInfo("error", "上传文件失败");
        this.uploading = false;
      }
    },

    // 上传base64文件到服务器
    async uploadBase64ToServer(base64Data, filePath) {
      try {
        // 从文件路径中提取文件名和扩展名
        const fileName = filePath.substring(filePath.lastIndexOf("\\") + 1);
        const fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

        // 将base64转换为Blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);

        // 根据文件扩展名设置MIME类型
        let mimeType = "application/octet-stream";
        if (fileExtension === "pdf") {
          mimeType = "application/pdf";
        } else if (["jpg", "jpeg"].includes(fileExtension)) {
          mimeType = "image/jpeg";
        } else if (fileExtension === "png") {
          mimeType = "image/png";
        } else if (fileExtension === "tif" || fileExtension === "tiff") {
          mimeType = "image/tiff";
        }

        const blob = new Blob([byteArray], { type: mimeType });

        // 创建FormData
        const formData = new FormData();
        formData.append("file", blob, fileName);

        // 使用项目中的文件上传API
        const uploadUrl = process.env.VUE_APP_BASE_API + "/file/upload";
        const token = this.$store.getters.token;

        const response = await fetch(uploadUrl, {
          method: "POST",
          headers: {
            Authorization: "Bearer " + token,
          },
          body: formData,
        });

        const result = await response.json();

        if (result.code === 200) {
          this.finalFileUrl = result.data.filename;
          this.displayOutputInfo("success", "文件上传成功");
          this.uploading = false;
        } else {
          throw new Error(result.msg || "上传失败");
        }
      } catch (error) {
        console.error("上传文件失败:", error);
        this.displayOutputInfo("error", "上传文件失败: " + error.message);
        this.uploading = false;
      }
    },

    // 处理关闭
    handleClose() {
      this.closeWebSocket();
    },

    // 处理取消
    handleCancel() {
      this.$emit("cancel");
      this.dialogVisible = false;
    },

    // 处理确认
    handleConfirm() {
      if (this.capturedFiles.length > 0) {
        // 如果有拍摄的文件，上传第一个文件
        const firstFile = this.capturedFiles[0];
        this.uploadFileToServer(firstFile.filePath);
      } else {
        this.displayOutputInfo("warning", "请先进行拍照操作");
      }
    },

    // 获取状态图标
    getStatusIcon(type) {
      const iconMap = {
        info: "el-icon-info",
        success: "el-icon-success",
        warning: "el-icon-warning",
        error: "el-icon-error",
      };
      return iconMap[type] || "el-icon-info";
    },

    // 处理视频错误
    handleVideoError() {
      console.log("视频加载错误");
    },
  },
};
</script>

<style lang="scss" scoped>
// 对话框样式
::v-deep .scanner-dialog {
  .el-dialog {
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .el-dialog__header {
    background: #409eff;
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 16px 20px;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
    }

    .el-dialog__close {
      color: white;
      font-size: 18px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 16px 20px;
    background: #f8f9fa;
    border-radius: 0 0 8px 8px;
    text-align: right;
  }
}

.doc-scanner-container {
  background: #f8f9fa;
  min-height: 70vh;

  // 状态提示样式
  .status-alert {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    margin: 12px 20px;
    border-radius: 6px;
    font-size: 14px;
    position: relative;
    animation: slideDown 0.3s ease-out;

    i:first-child {
      margin-right: 8px;
      font-size: 16px;
    }

    .close-btn {
      position: absolute;
      right: 12px;
      cursor: pointer;
      opacity: 0.7;

      &:hover {
        opacity: 1;
      }
    }

    &.status-info {
      background: #e1f5fe;
      border-left: 4px solid #409eff;
      color: #1976d2;
    }

    &.status-success {
      background: #f0f9ff;
      border-left: 4px solid #409eff;
      color: #1976d2;
    }

    &.status-warning {
      background: #fff7e6;
      border-left: 4px solid #409eff;
      color: #1976d2;
    }

    &.status-error {
      background: #fef2f2;
      border-left: 4px solid #f56c6c;
      color: #c62828;
    }
  }

  // 控制面板样式
  .control-panel {
    margin: 0 20px 16px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    overflow: hidden;

    .panel-header {
      background: #409eff;
      color: white;
      padding: 12px 16px;
      display: flex;
      align-items: center;
      font-weight: 600;
      font-size: 14px;

      i {
        margin-right: 8px;
        font-size: 16px;
      }
    }

    .control-grid {
      padding: 16px;

      .control-group {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }

        &.slider-group {
          background: #f8f9fa;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid #e9ecef;
        }

        .control-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .control-label {
            font-size: 13px;
            color: #606266;
            font-weight: 500;
            white-space: nowrap;
            min-width: 60px;
          }

          .control-select {
            min-width: 120px;
          }
        }

        .slider-item {
          flex: 1;
          min-width: 200px;

          .slider-label {
            font-size: 13px;
            color: #606266;
            font-weight: 500;
            margin-bottom: 8px;
            display: block;
          }

          .param-slider {
            ::v-deep .el-slider__runway {
              background: #e4e7ed;
              height: 6px;
            }

            ::v-deep .el-slider__bar {
              background: #409eff;
            }

            ::v-deep .el-slider__button {
              border: 2px solid #409eff;
              width: 16px;
              height: 16px;
            }

            ::v-deep .el-input__inner {
              width: 60px;
              height: 28px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    gap: 20px;
    margin: 0 20px 16px;

    .video-area {
      flex: 1;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .video-container {
        margin-top: 16px;
        position: relative;
        background: #000;
        border-radius: 6px;
        overflow: hidden;
        width: 100%;
        aspect-ratio: 16/9;
        display: flex;
        align-items: center;
        justify-content: center;

        .video-display {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          border-radius: 6px;
        }

        .video-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          color: #909399;

          i {
            font-size: 36px;
            margin-bottom: 8px;
            display: block;
          }

          p {
            font-size: 13px;
            margin: 0;
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      min-width: 320px;
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .setting-card,
    .action-card,
    .file-card {
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      overflow: hidden;

      .card-header {
        background: #409eff;
        color: white;
        padding: 10px 14px;
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 13px;

        i {
          margin-right: 6px;
          font-size: 14px;
        }
      }

      .card-content {
        padding: 10px;

        .setting-group {
          .setting-row {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            gap: 6px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .action-buttons {
          margin-top: 8px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 6px;

          .el-button {
            margin: 0;
            border-radius: 4px;
            font-weight: 500;
            font-size: 12px;
            padding: 15px 8px;

            &:hover {
              opacity: 0.9;
            }
          }
        }

        .setting-label {
          font-size: 13px;
          color: #606266;
          font-weight: 500;
          white-space: nowrap;
          min-width: 60px;
        }

        .setting-select {
          min-width: 120px;
        }

        .setting-select-small {
          min-width: 90px;
        }

        .setting-input {
          min-width: 140px;
        }

        .setting-number {
          width: 200px;

          ::v-deep .el-input__inner {
            height: 28px;
            font-size: 12px;
          }
        }

        .setting-checkbox {
          margin-left: 12px;
        }

        .watermark-checkbox {
          font-weight: 500;

          ::v-deep .el-checkbox__label {
            color: #303133;
          }
        }

        .unit-text {
          font-size: 12px;
          color: #909399;
          margin-left: 4px;
        }
      }
    }

    .file-card {
      flex: 1;

      .file-list {
        overflow-y: auto;

        ::v-deep .el-table {
          border: none;

          .el-table__header {
            th {
              border: none;
              background: #f8f9fa;
            }
          }

          .el-table__body {
            tr {
              &:hover {
                background: #f8f9fa;
              }

              td {
                border: none;
                padding: 8px 12px;
              }
            }
          }
        }

        .empty-state {
          text-align: center;
          padding: 30px 16px;
          color: #909399;

          i {
            font-size: 36px;
            margin-bottom: 8px;
            display: block;
            opacity: 0.5;
          }

          p {
            margin: 0;
            font-size: 13px;
          }
        }
      }
    }
  }
}

// 图片预览样式
.image-preview {
  text-align: center;
  padding: 16px;

  img {
    max-width: 100%;
    max-height: 60vh;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}

// 对话框底部按钮样式
.dialog-footer {
  .el-button {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;

    i {
      margin-right: 4px;
    }

    &.el-button--primary {
      background: #409eff;
      border-color: #409eff;

      &:hover {
        background: #66b1ff;
        border-color: #66b1ff;
      }
    }
  }
}

// 动画效果
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 1400px) {
  .doc-scanner-container {
    .main-content {
      .right-panel {
        min-width: 300px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .doc-scanner-container {
    .main-content {
      flex-direction: column;

      .video-area {
        .video-container {
          max-width: 500px;
          max-height: 200px;
          margin: 0 auto;
        }
      }

      .right-panel {
        min-width: auto;

        .action-card .action-buttons {
          grid-template-columns: repeat(4, 1fr);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  ::v-deep .scanner-dialog .el-dialog {
    width: 95% !important;
    margin: 3vh auto !important;
  }

  .doc-scanner-container {
    .control-panel {
      margin: 0 10px 12px;

      .control-grid {
        padding: 10px;

        .control-group {
          flex-direction: column;
          align-items: stretch;
          gap: 8px;
          margin-bottom: 12px;

          .control-item {
            flex-direction: column;
            align-items: stretch;
            gap: 4px;

            .control-label {
              min-width: auto;
              font-size: 12px;
            }

            .control-select {
              min-width: auto;
            }
          }

          &.slider-group {
            padding: 8px;

            .slider-item {
              min-width: auto;

              .param-slider {
                ::v-deep .el-input__inner {
                  width: 45px;
                }
              }
            }
          }
        }
      }
    }

    .main-content {
      margin: 0 10px 12px;
      gap: 12px;

      .video-area {
        .video-container {
          max-height: 160px;
        }
      }

      .right-panel {
        .setting-card,
        .action-card,
        .file-card {
          .card-content {
            padding: 10px;

            .setting-row {
              flex-direction: column;
              align-items: stretch;
              gap: 4px;
              margin-bottom: 8px;

              .setting-label {
                min-width: auto;
                font-size: 12px;
              }

              .setting-select,
              .setting-select-small,
              .setting-input {
                min-width: auto;
              }

              .setting-checkbox {
                margin-left: 0;
                margin-top: 4px;
              }
            }
          }
        }

        .action-card .action-buttons {
          grid-template-columns: 1fr;
          gap: 6px;
        }

        .file-card .file-list {
          max-height: 200px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  ::v-deep .scanner-dialog {
    .el-dialog__header {
      padding: 12px 16px;

      .el-dialog__title {
        font-size: 15px;
      }
    }

    .el-dialog__footer {
      padding: 12px 16px;

      .el-button {
        padding: 6px 12px;
        font-size: 12px;
      }
    }
  }

  .doc-scanner-container {
    .status-alert {
      margin: 8px 12px;
      padding: 8px 10px;
      font-size: 12px;
    }

    .control-panel,
    .main-content {
      margin-left: 6px;
      margin-right: 6px;
    }

    .main-content .video-area .video-container {
      max-height: 140px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .doc-ocr-container {
    .main-content {
      flex-direction: column;

      .video-area {
        img {
          max-width: 100%;
          height: auto;
        }
      }

      .right-panel {
        min-width: auto;
      }
    }
  }
}

@media (max-width: 768px) {
  .doc-ocr-container {
    padding: 10px;

    .control-panel {
      .control-row {
        flex-direction: column;
        align-items: flex-start;

        label {
          margin-bottom: 5px;
        }

        .el-select,
        .el-input-number,
        .el-button {
          margin-bottom: 10px;
          width: 100%;
        }
      }
    }

    .main-content {
      .right-panel {
        .setting-group {
          padding: 10px;

          .el-select,
          .el-input,
          .el-input-number {
            width: 100%;
          }
        }

        .action-buttons {
          .el-button {
            width: 48%;
            margin-right: 2%;
          }
        }
      }
    }
  }
}
</style>
