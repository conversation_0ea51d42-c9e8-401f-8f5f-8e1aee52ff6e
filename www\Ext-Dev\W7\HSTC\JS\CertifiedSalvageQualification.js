var _url, _ws;
var _connected = false;

function initWsUrl() {
    _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    console.log(_url);
}

initWsUrl();

/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e.data);
        }
    }
    _ws.onerror = function (e) {
        displayOutputInfo(3, '未连接websocket服务器，请确保已运行服务端!');
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}

function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}

function SendBlob(blob) {
    _connected ? _ws.send(blob) : ConnectServer(SendTxt, blob)
}

function SendHalfBinMsg(json, ...blobs) {
    // 消息息为“半二进制”方式。消息字节格式如下：
    // [协议头,固定字节 0x22                       1个字节]
    // [文本区(JSON字符串)的字节数,                 4个字节, 高位在前]
    // [二进制数据区 的字节数,                      4个字节, 高位在前]
    // [文本区(JSON字符串)的内容, ascii编码 ......]
    // [二进制数据区(签署后的pdf等相关信息)......]

    var strJson = JSON.stringify(json);
    //var lenJson = strJson.length;
    var blobStrJson = new Blob([strJson]);
    var lenJson = blobStrJson.size;
    var lenBlob = 0;
    var blobCount = blobs.length;
    for (var i = 0; i < blobCount; ++i) {
        lenBlob += blobs[i].size;
    }
    console.log(lenJson, lenBlob, strJson);

    var headAndLength = new Uint8Array(9);
    headAndLength[0] = 0x22;
    headAndLength[1] = (lenJson >> 24) & 0xFF;
    headAndLength[2] = (lenJson >> 16) & 0xFF;
    headAndLength[3] = (lenJson >> 8) & 0xFF;
    headAndLength[4] = (lenJson) & 0xFF;
    headAndLength[5] = (lenBlob >> 24) & 0xFF;
    headAndLength[6] = (lenBlob >> 16) & 0xFF;
    headAndLength[7] = (lenBlob >> 8) & 0xFF;
    headAndLength[8] = (lenBlob) & 0xFF;

    var blobMsg = null;
    switch (blobCount) {
        case 1:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0]]);
            break;
        case 2:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1]]);
            break;
        case 3:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1], blobs[2]]);
            break;
        case 4:
            blobMsg = new Blob([headAndLength, blobStrJson, blobs[0], blobs[1], blobs[2], blobs[3]]);
            break;
    }
    SendBlob(blobMsg);
}

// 用UploadFile发送文件
function sendFile(file, type) {
    var SEC_MAX_SIZE = 4194304;
    var fileSize = file.size;
    var sectionCount = Math.ceil(fileSize / SEC_MAX_SIZE);
    var json = {
        func: "UploadFile",
        to: 1,
        type: type,
        reqId: new Date().getTime().toString(10),
        fileInfo: {fileCount: 1, fileNum: 0, fileSize: fileSize, sectionCount: sectionCount, fileName: file.name},
        content: [0, 0]
    }

    for (var i = 0; i < sectionCount; ++i) {
        json.fileInfo.fileOffset = i * SEC_MAX_SIZE;
        json.fileInfo.sectionNum = i;
        if (sectionCount == 1) {
            json.content[1] = fileSize;
            SendHalfBinMsg(json, file);
        } else {
            var start = i * SEC_MAX_SIZE;
            var end = min(start + SEC_MAX_SIZE, fileSize);
            json.content[1] = end - start;
            var fileSection = file.slice(start, end);
            SendHalfBinMsg(json, fileSection);
        }
    }
}

/**
 * 处理服务器二进制消息
 */
function onBinMessage(data) {
    var head = data.slice(0, 9);
    var reader = new FileReader();
    reader.readAsArrayBuffer(head);
    reader.onload = function (e) {
        var buf = new Uint8Array(reader.result);
        if (buf[0] == 0x22 && buf[1] == 0x0) {
            // 是半二进制信息，拆分json串与bin内容
            var lenJson = buf[1] << 24 | buf[2] << 16 | buf[3] << 8 | buf[4];
            var lenBin = buf[5] << 24 | buf[6] << 16 | buf[7] << 8 | buf[8];
            reader.readAsText(data.slice(9, 9 + lenJson), 'utf-8');
            reader.onload = function (e) {
                var jsonObj = JSON.parse(reader.result);
                console.log(jsonObj);
                var binData = data.slice(9 + lenJson, 9 + lenJson + lenBin);
                switch (jsonObj.func) {
                    case "CertifiedSalvageQualification":
                        displayQualificationResult(jsonObj, binData);
                        break;
                }
            }
        }
    };
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    switch (jsonObj.func) {
        case "CertifiedSalvageQualification":
            if (jsonObj.result == 0) {
                displayOutputInfo(2, "救助资格认证成功.");
            } else {
                displayOutputInfo(3, "救助资格认证失败.<br>result = " + jsonObj.result);
            }
            break;
        default:
            console.log(msg);
            if (jsonObj.result != 0) {
                var info = jsonObj.func + "<br>";
                if (jsonObj.errorMsg) {
                    info += jsonObj.errorMsg;
                }
                displayOutputInfo(3, info);
            }
            break;
    }
}

function displayQualificationResult(v, binData) {
    if (v.result == 0) {
        // {
        //     "func": "CertifiedSalvageQualification",
        //     "reqId": 164098,
        //     "result": 0,
        //     "isCertify":true //是否通过认证
        //     "certifyErrorMsg":"人脸比对不通过"//如果比对未通过.不通过的原因
        //     "startIndex":0,       //char* 数据起点 包含
        //     "endIndex":10000,     //终点 不包含
        //     "infoName":"detect.png" //人脸检测的图片
        //     "idCard": {
        //     "name":"jean",   //姓名
        //         "sex":"",        //性别
        //         "nation":"",     //名族
        //         "birthDate":"",  //生日
        //         "address":"",  //地址
        //         "idCardNum":"", //身份证卡号
        //         "signOffice":"", //签发机关
        //         "certType":"",   //证件类型
        //         "headPic":"",  //base64编码图片
        //         "usefulStartDate":"", //起始日期
        //         "usefulEndDate":""   //有效期结束
        //     }
        // }

        displayOutputInfo(2, "救助资格认证成功.");
        $('#divResult').css("display", "");
        var idCard = v.idCard;
        $('#idName').text(idCard.name);
        $('#idSex').text(idCard.sex);
        $('#idNation').text(idCard.nation);
        $('#idNum').text(idCard.idCardNum);
        $('#idBirthDate').text(idCard.birthDate);
        $('#idAddress').text(idCard.address);
        $('#idSignOffice').text(idCard.signOffice);
        $('#idValidityDate').text(idCard.usefulStartDate + " - " + idCard.usefulEndDate);
        document.getElementById("idHead").src = "data:bmp;base64," + idCard.headPic;
        if (v.isCertify) {
            $('#certifyInfo').text("认证通过").css("color", "green");
        } else  {
            $('#certifyInfo').html("认证不通过<br>错误代码：" + v.certifyErrorCode + "<br>错误信息：" +  v.certifyErrorMsg).css("color", "red");
        }

        var obj = document.getElementById("faceDetectImg");
        if (obj) {
            if (obj.src) {
                window.URL.revokeObjectURL(obj.src);
                obj.src = "";
            }
            obj.src = getObjectURL(binData);
        }
    } else {
        $('#divResult').css("display", "none");
        var info = "救助资格认证成功 失败<br>result = " + v.result;
        if (v.errorMsg) {
            info += "<br>" + v.errorMsg;
        }
        displayOutputInfo(3, info);
    }
}

//建立一个可存取到该file的url
function getObjectURL(file) {
    var url = null;
    // 下面函数执行的效果是一样的，只是需要针对不同的浏览器执行不同的 js 函数而已
    if (window.createObjectURL != undefined) {   // basic
        url = window.createObjectURL(file);
    } else if (window.URL != undefined) {        // mozilla(firefox)
        url = window.URL.createObjectURL(file);
    } else if (window.webkitURL != undefined) {  // webkit or chrome
        url = window.webkitURL.createObjectURL(file) ;
    }
    return url;
}

function displayOutputInfo(disp, s) {
    if (disp > 0 && disp < 4) {
        var ary = ["info", "success", "warning"];
        $('#outInfo').css("display", "").attr("class", "col-md-12 alert alert-" + ary[disp - 1]).html("<b>" + s + "</b>");
    } else {
        $('#outInfo').css("display", "none").html("");
    }
}

// 开始[救助资格认证]
function startCertification() {
    SendJson({func: "CertifiedSalvageQualification", reqId: new Date().getTime().toString(10), to:1});
}
