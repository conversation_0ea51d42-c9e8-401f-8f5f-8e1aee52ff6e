<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>CamSDK auto continuous capture demo</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/en.js"></script>
    <script src="../../JS/camera/AutoCapture_en.js"></script>
</head>
<body>
<p>This example demonstrates how to automatic turn on the document camera, set automatic cropping and auto capture.Live video on the left and photos on the right。</p>
<div>
    <div style="float:left;margin-right:20px;">
        <div id="video1">
            <img id="video" style="width:506px;height:380px">
        </div>
        <div style="margin-top: 20px; margin-left: 20px; ">
            <button onclick="autoCaptureControl(0)">Auto capture</button>
            <button onclick="autoCaptureControl(1)">Time-lapse capture</button>
            <button onclick="autoCaptureControl(-1)">Stop capture</button>
            <label style="padding-left:20px;">Capture speed: <span id="speedNum">6</span>
                <input id="speed" type="range" min="4" max="12" value="6" onchange="onSpeedChange()"/>
            </label>
            <br/>
            <br/>
            <div id="divTimer" style="display:none;">
                The countdown of time-lapse capture：
                <progress id="progress0" value="0" max="100"></progress>
                <br/>
                <br/>
            </div>
            <span id="status"></span>
            <audio id="audioCapture">
                <source src="../../audio/Capture.wav" type="audio/wav">
            </audio>
        </div>
    </div>

    <div style="float:left;margin-right:20px;">
        <img id="imgCapture" style="width:506px;height:380px">
        <div style="margin-top: 20px; margin-left: 20px; ">
            <div id="captureInfo"></div>
        </div>
    </div>
</div>

</body>
</html>