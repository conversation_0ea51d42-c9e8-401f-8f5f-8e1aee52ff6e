<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryParams" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="责任单位" prop="taskOrg">
          <treeselect v-model="queryParams.taskOrg" :options="deptOptions" :normalizer="normalizer" placeholder="选择责任单位"  style="width:200px" @input="orgchange"/>
      </el-form-item>
      <el-form-item label="路线" prop="routeId">
        <el-select v-model="queryParams.routeId" placeholder="请选择路线" clearable filterable>
            <el-option
              v-for="item in routes"
              :key="item.routeId"
              :label="item.routeName"
              :value="item.routeId">
            </el-option>
          </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="gtbegintime1">
           <el-date-picker v-model="queryParams.gtbegintime1" type="datetime" placeholder="选择时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
      </el-form-item> 
      <el-form-item label="结束时间" prop="ltendtime1">
           <el-date-picker v-model="queryParams.ltendtime1" type="datetime" placeholder="选择时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
      </el-form-item> 
       <el-form-item label="创建时间" >
         <el-date-picker v-model="queryParams.gtdate" type="datetime" placeholder="选择时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>——<el-date-picker v-model="queryParams.ltdate" type="datetime" placeholder="选择时间" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
       </el-form-item>
      <el-form-item label="状态" prop="state">
        <el-select v-model="querystate" placeholder="状态" clearable>
          <el-option
            v-for="dict in options"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
       
      </el-form-item>
    </el-form>

  

    <el-table v-loading="loading" :data="taskList" >
      <el-table-column label="责任单位" align="center" prop="organization.organName" :show-overflow-tooltip="true" />
      <el-table-column label="路线" align="center" prop="inspectroute.routeName" :show-overflow-tooltip="true" />
      <el-table-column prop="planId" label="任务类型" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.planId!=undefined">计划任务</span>
          <span v-if="scope.row.planId==undefined">临时任务</span>
        </template>
      </el-table-column>
      <el-table-column label="计划开始时间" align="center" prop="begintime" :show-overflow-tooltip="true" />
      <el-table-column label="计划结束时间" align="center" prop="endtime" :show-overflow-tooltip="true" />
      <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">待巡检</span>
          <span v-if="scope.row.state==2">停止</span>
          <span v-if="scope.row.state==3">巡检中</span>
          <span v-if="scope.row.state==4">巡检完成</span>
        </template>
      </el-table-column>

      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          
          <el-button 
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >巡检详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


     <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body>
      <el-row style="margin-bottom:6px">
        <el-col :span="8"><span style="font-size:18px;font-weight:bold">巡检人:</span><span style="font-size:18px;">{{recorddata.worker.workerName}}</span></el-col>
        <el-col :span="8"><span style="font-size:18px;font-weight:bold">开始时间:</span><span style="font-size:18px;">{{recorddata.begindate}}</span></el-col>
        <el-col :span="8"><span style="font-size:18px;font-weight:bold">结束时间:</span><span style="font-size:18px;">{{recorddata.enddate}}</span></el-col>
      </el-row>
        <el-divider></el-divider>
        <span v-for="siterecord in siterecords" :key="siterecord.siterecordId">
          <el-row style="margin-bottom:6px">
              <el-col :span="24"><span style="font-size:20px;font-weight:bold">检查区域:</span><span style="font-size:20px;">{{siterecord.inspectsite.siteName}}</span></el-col>
          </el-row>
          <span v-for="inspectrecorditem in siterecord.inspectrecorditemList" :key="inspectrecorditem.recorditemId" >
            <el-row style="margin-bottom:3px">
              <el-col :span="6"><span style="font-size:15px;font-weight:bold">检查项:</span><span style="font-size:15px;">{{inspectrecorditem.inspectitem.itemName}}</span></el-col>
              <el-col :span="18"><span style="font-size:15px;font-weight:bold">检查内容:</span><span style="font-size:15px;">{{inspectrecorditem.inspectitem.itemContent}}</span></el-col>
            </el-row>
            <el-row style="margin-bottom:3px">
              <el-col :span="6" ><span style="font-size:15px;font-weight:bold">检查方式:</span>
                <template v-if="inspectrecorditem.inspectType==1">
                    <span style="font-size:15px;">观察</span>
                </template>
                <template v-if="inspectrecorditem.inspectType==2">
                    <span style="font-size:15px;">抄表</span>
                </template>
              </el-col>
              <el-col :span="6" v-if="inspectrecorditem.inspectType==1"><span style="font-size:15px;font-weight:bold">检查结果:</span>
                <template v-if="inspectrecorditem.quality==1">
                    <span style="font-size:15px;">正常</span>
                </template>
                <template v-if="inspectrecorditem.quality==2">
                    <span style="font-size:15px;">异常</span>
                </template>
              </el-col>
              <el-col :span="6" v-if="inspectrecorditem.inspectType==2"><span style="font-size:15px;font-weight:bold">表值：</span>
                <span style="font-size:15px;">{{inspectrecorditem.inspectValue}}</span>
              </el-col>
              <el-col :span="12" ><span style="font-size:15px;font-weight:bold">情况描述:</span><span style="font-size:15px;">{{inspectrecorditem.result}}</span></el-col>
              
            </el-row>
            <el-row style="margin-bottom:6px">
              <el-col :span="24">
                <span style="font-size:15px;font-weight:bold">现场照片:</span>
                <span v-for="(imgpath1,index) in inspectrecorditem.imgPaths" :key="index" style="paddingRight :5px">
                 <image-preview :src="'http://em.xdbg.cn/xdemserv'+imgpath1" :width="120" :heigth="120"/>
                </span>
              </el-col>
            </el-row>
          </span>
          <el-divider></el-divider>
        </span>
        
     </el-dialog>
  </div>
</template>

<script>
import { listtask, gettask,loadrecord} from "@/api/inspect/task";
import {  listDeptExcludeChild } from "@/api/system/dept";
import { listline} from "@/api/inspect/line";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "Inspectreport",
  components: { Treeselect },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 分类数据
      classList: [],
      //任务列表数据
      taskList:[],
      //路线列表
      routes:[],
      //部门
      deptOptions:[],
      //巡检
      siterecords:[],
      //巡检
      recorddata:{
        "worker":{}
      },
      //问题列表
      problems:[],
      //图片地址
      imgPath:undefined,
      //整改图片
      amendimgPath:undefined,
      //验收图片
      verifyimgPath:undefined,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      //是否显示弹出显示层
      viewopen:false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      querystate:undefined,
      // 表单参数
      form: {},
      verifyform:{},
      amendimgs:undefined,
      //详情
      blackspostvo:{
        organization:{organName:''},
        orgarea:{areaName:''},
        worker:{workerName:''},
        problemclass:{className:''},
        problem:{perform:''}
      },

         //状态
      options: [{
          value: 1,
          label: '待巡检'
        }, {
          value: 2,
          label: '停止'
      }, {
          value: 3,
          label: '巡检中'
      }, {
          value: 4,
          label: '巡检完成'
      }],
      //整改方式
      amendTypes:[{
          value: 1,
          label: '立刻整改'
        },{
          value: 2,
          label: '停业停产整改'
        }]
    };
  },
  created() {
    this.getDeptTree();
    this.getList();
  },
  methods: {
    /** 查询部门下拉树结构 */
    getDeptTree() {
      let queryparams1 = {};
      listDeptExcludeChild(queryparams1).then(response => {
        let datas = response.data;
        for(let i=0; i<datas.length; i++){
          datas[i].children=[];
        }
        this.deptOptions = this.handleTree(datas, "organId");
      });
    },
       /** 转换部门数据结构 */
    normalizer(node) {
      if (node.children && !node.children.length) {
        delete node.children;
      }
      return {
        id: node.organId,
        label: node.organName,
        children: node.children
      };
    },
    //任务列表
    getList(){
      this.loading = true;
      if(this.querystate== undefined || this.querystate==="" || this.queryParams.state == undefined){
        this.queryParams.state = [1,2,3,4]
      }
      if(this.querystate != undefined &&  this.querystate !=""){
        this.queryParams.state = this.querystate
      }
      
      this.queryParams.orderBy="creationtime desc"
      listtask(this.queryParams).then(response => {
          this.taskList = response.data;
          this.total = response.totalCount;
          this.loading = false;
        }
      );
    },


    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      if(this.queryParams.taskOrg == ''){
        this.queryParams.taskOrg=undefined
      }
      if(this.queryParams.routeId == ''){
        this.queryParams.routeId=undefined
      }
      if(this.queryParams.gtbegintime1 == '' || this.queryParams.gtbegintime1===null){
        this.queryParams.gtbegintime1=undefined
      }
      if(this.queryParams.ltendtime1 == '' || this.queryParams.ltendtime1===null){
        this.queryParams.ltendtime1=undefined
      }
      if(this.queryParams.gtdate == '' || this.queryParams.gtdate===null){
        this.queryParams.gtdate=undefined
      }
      if(this.queryParams.ltdate == '' || this.queryParams.ltdate===null){
        this.queryParams.ltdate=undefined
      }
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
    },




    /**整改按钮操作 */
    handleView(row){
      this.reset();
      const taskId = row.taskId || this.ids
      gettask(taskId).then(response => {
        const task = response.data
        if(task.state===2 || task.state===1){
           this.$modal.msgError("未有巡检记录");
        }else{
          var recordparam = {"taskId":taskId,"not state":0}
          loadrecord(recordparam).then(res => {
            this.recorddata = res.data;
            this.siterecords = this.recorddata.siterecords;
            this.open = true;
            this.title = "巡检记录";
          })
         
        }
       
        
      });

    
      
      
    },


    //选择责任单位
    orgchange(value){
        this.routes=[]
        if(typeof(value) != "undefined"){
          let paramorg = {"routeOrgId":value}
          
          listline(paramorg).then(response=>{
            if(response.code==200){
              this.routes=response.data;
            }
          })
        }
       
    }


  }
};
</script>