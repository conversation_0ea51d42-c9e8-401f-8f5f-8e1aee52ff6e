<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>拍书演示</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/zhCN.js"></script>
    <script src="../../JS/camera/CaptureBook.js"></script>
    <script src="../../JS/camera/Message.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/CaptureBook.css"/>
    <link rel="stylesheet" href="../../CSS/camera/Message.css"/>
</head>
<body>
    <table class="fullTable">
        <tr height="500">
            <td id="td1" class="left" valign="top" align="center" rowspan="2">
                <div class="left-one">
                    <div class="left-one-left">
                        <img id="cameraImage" src="../../image/camera.png"/>
                    </div>
                    <div class="left-one-right">
                        <span>拍书演示</span>
                    </div>
                </div>
                <div class="left-two">
                    <div>
                        <label class="left-two-lable">设备名称:</label>
                        <select id="cameraInfo" class="left-two-select" onchange="switchCameraInfo_Fun()"></select>
                        <br>
                        <label class="left-two-lable">视频格式:</label>
                        <select id="mediaType" class="left-two-select" onchange="setCameraInfo_Fun()"></select>
                        <br>
                        <label class="left-two-lable">分辨率:</label>
                        <select id="resolution" class="left-two-select" onchange="setCameraInfo_Fun()"></select>
                    </div>
                    <br><br>
                    <button id="btnCaptureBook" onclick="captureBook()">拍 书</button>
                    <br><br>
                    <label title="检测图像变化,自动进行连拍">
                        <input id="autoCapture" type="checkbox" value="0" onchange="OnAutoCaptureChange()"/>自动连拍
                    </label>
                    <br><br>
                    <label>连拍速度: <span id="speedNum">6</span>
                        <input id="speed" type="range" min="4" max="12" value="6" onchange="onSpeedChange()"/>
                    </label>
                </div>
                <p>拍书操作指导 </p>
                <p>把书本的中缝对齐绿色中分线</p>
                <img id="tutorial" src="../../image/BookAnimatedTutorial.gif" width="250"/>
                <audio id="audioCapture">
                    <source src="../../audio/Capture.wav" type="audio/wav">
                </audio>
            </td>
            <td class="middle">
                <img id="pic"/>
                <div id="vertical-line" style="display: none" ></div>
            </td>
            <td class="right" valign="top" rowspan="2">
                <label class="left-two-lable">拍照文件列表:</label>
                <button id="OCR" onclick="convertToFile_Fun()">文字识别</button>
                <button id="btnMergePDF" onclick="mergePDF()">合并PDF</button>
                <button id="btnClearFile" onclick="clearImageFile()">清空文件</button>
                <table id="tbFile"><tr><th>文件名</th><th>操作</th></tr></table>
            </td>
        </tr>
        <tr>
            <td id="td4" class="bottom" valign="top" >
                <div>左页<br><img id="imgL"></div>
                <div>右页<br><img id="imgR"></div>
            </td>
        </tr>
    </table>
    <div id="divView" style="display:none;">
        <div>
            <button onclick="closeView()">关闭查看</button>
        </div>
        <img id="imgView">
    </div>
</body>
</html>











