<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Compare face and ID card</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/en.js"></script>
    <script src="../../JS/MzWsConnect.js"></script>
    <script src="../../JS/camera/idFace.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/idFace.css"/>
</head>
<body>
<h1>Compare face and ID card</h1>
<p>This demo demonstrates the scene of witness comparison. It is best to have an ID card reader and a binocular camera, and at least one Doc Camera with a vice head is required. The accuracy of binocular camera in living body detection is much higher than that of general camera.</p>
<br>
<table cellspacing="0" cellpadding="12" class="tb1" border="1">
    <tr>
        <td align="center" valign="middle" width="36%"><b>Select or obtain standard face images</b></td>
        <td align="center" valign="middle" width="32%"><b>Video and comparison parameters</b></td>
        <td align="center" valign="middle" width="32%"><b>Comparison results</b></td>
    </tr>
    <tr>
        <td height="120">
            Direct <button onclick="openHeadImage()">Open face image file</button>
            <input id="imgFile" type="file" onchange="chooseImgFile()" accept="image/jpg, image/jpeg"
                   style="display: none">
            <br>
            or have Doc Camera <select id="selDev1" style="width:160px;"></select>
            <button onclick="openCamera1()">Open</button>
            then
            <button onclick="capterImg()">Shot</button>
            <br>
            or have ID card reader
            <button onclick="readIdCard()">Read ID card</button>
            <br>ID message: <br><textarea id="idCardInfo" readonly="true"></textarea>
        </td>
        <td>
            <input type="checkbox" checked id="chkLiveCheck"><label for="chkLiveCheck">Turn on live detection</label><br>
            Matching minimum similarity(0-100)<input id="inputSimilarity" type="number" value="80" style="width:50px"/><br>
            Stop by：
            <select id="selStopMode">
                <option value="0">Manual</option>
                <option value="1" selected>Auto</option>
                <option value="2">Timing</option>
            </select><br>
            Stop after<input id="inputRunSeconds" type="number" value="60" style="width:50px"/>seconds<br>
            <input type="checkbox" checked id="chkSendMatchedImg"><label
                for="chkSendMatchedImg">The returned result contains the matching image</label><br>
            <input type="checkbox" checked id="chkPlayWav"><label for="chkPlayWav">Play sound when matching is successful</label><br>
            Face Camera:<select id="selDev2" style="width:160px;"></select>
            <button onclick="openCamera2()">Open the face camera</button>
            <button onclick="startStopMatch()" id="btnStartStop">Start Compare</button>

        </td>
        <td class="right" align="center" valign="middle" style="line-height: 56px; font-size: 32px;">
            <span id="state1" style="color:darkgrey;font-size:24px">Face detected in color video</span><br>
            <span id="state2" style="color:darkgrey;font-size:24px">Face comparison results:</span>
            <span id="state5" style="color:green;font-size:24px"></span>
            <span id="state7" style="color:darkgrey;font-size:24px"></span><br>

            <span id="state3" style="color:darkgrey;font-size:24px">Face detected in infrared video</span><br>

            <span id="state4" style="color:darkgrey;font-size:24px">Vivo detection results:</span>
            <span id="state6" style="color:green;font-size:24px"></span>
            <span id="state8" style="color:darkgrey;font-size:24px"></span><br>
        </td>
    </tr>
    <tr>
        <td>
            Video or face image:<br>
            <img id="imgHead" style="width:384px;height:288px;"/>
        </td>
        <td>
            Face video:<br>
            <img id="imgVideo" style="width:384px;height:288px;"/>
        </td>
        <td>
            Matching face:<br>
            <img id="imgMatched" style="width:384px;height:288px;"/>
        </td>
    </tr>
</table>
</body>
</html>
