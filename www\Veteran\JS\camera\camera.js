var _url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":8802/pc";
var _ws;
var _connected = false;
var _devInfos = null;
var _devNum = 'A';
var _isStillPin = false;
/**
 * 初始化webSocket连接
 */
function ConnectServer(callback, value) {
    if ('WebSocket' in window) {
        _ws = new WebSocket(_url);
    } else if (window.WebSocket) {
        _ws = new WebSocket(_url);
    } else if ('MozWebSocket' in window) {
        _ws = new MozWebSocket(_url);
    } else {
        alert(Lang.WS.lowVer);
    }
    _ws.onopen = function () {
        _connected = true;
        callback(value);
    }
    _ws.onclose = function (e) {
        _connected = false;
    }
    _ws.onmessage = function (e) {
        if (typeof e.data === "string") {
            onTxtMessage(e.data);
        } else {
            onBinMessage(e);
        }
    }
    _ws.onerror = function (e) {
        alert(Lang.WS.disConn)
    };
}

/**
 * 向服务器发送信息
 */
function SendTxt(jsonStr) {
    _connected ? _ws.send(jsonStr) : ConnectServer(SendTxt, jsonStr)
}
function SendJson(json) {
    _connected ? _ws.send(JSON.stringify(json)) : ConnectServer(SendJson, json)
}


/**
 * 处理服务器二进制消息
 */
function onBinMessage(e) {
    console.log('onBinMessage', e);
}

/**
 * 处理接收到的服务器文本消息
 */
function onTxtMessage(msg) {
    var jsonObj = JSON.parse(msg);
    if (jsonObj.result != undefined && jsonObj.result != 0 && jsonObj.result != 9) {
        // 返回result=0表示执行成功,其他值表示异常。9表示：设备使用中，打开摄像头时忽略这个异常
        console.log(jsonObj.typeName, jsonObj.errorMsg);
        message.run(jsonObj.typeName + "\t" + jsonObj.errorMsg,"warning",1700);
    } else {
        switch (jsonObj.typeName) {
            case "FrameBase64":
                DisplayVideo(jsonObj);
                break;
            case "PhotographBase64":
                DisplayImg(jsonObj.message);
                break;
            case "IDCardInfo":
                DisplayIDCardInfo(jsonObj.message);
                break;
            default:
                console.log(msg);
                break;
        }
    }
}

//拍照
function cameraCapture_Fun() {
    //高拍仪拍照接口
    var com = document.getElementById("chkLiveCheck").checked ? 1 : 0;
    var cmd = JSON.stringify({typeName: 'Photograph', message: com});
    SendTxt(cmd);
}
//读卡
function readIdCard() {
    SendJson({typeName:"StartIDCardInfo", message:""});
}

//设备信息的onchange事件
function switchCameraInfo_Fun() {

    var devtype = $("#cameraInfo").val() == 0 ? 'A' : 'B';
    if (_devNum != devtype) {
        //关闭之前的摄像头
        var closeCmd = JSON.stringify({typeName: 'CloseDevice', message: _devNum});
        SendTxt(closeCmd);
    }

    //开启摄像头
    var cmd = JSON.stringify({typeName: 'OpenDevice', message: devtype});
    _devNum = devtype;
    SendTxt(cmd);
}

function DisplayVideo(v) {
    if (v.message) {
        //显示视频
        document.getElementById("pic").src = "data:" + "image/jpeg" + ";base64," + v.message;
    }
}


//初始化信息
$(function () {
    document.getElementById("chkLiveCheck").checked = 0;
    window.onresize = onWinResize;
    onWinResize();

    switchCameraInfo_Fun();
})

function onWinResize() {
    //初始化图片列表的高度
    $("#right_div").css('height', (document.body.clientHeight - 30) + 'px');
    //初始化'结果显示'的高度
    var p = $("#resultArea").offset();
    $("#resultArea").css('height', (document.body.clientHeight - p.top - 16) + 'px');

}
function DisplayIDCardInfo(v){
    var idInfo;
    var Content = v[0].resultContent;
    idInfo = Lang.IDC.name + Content.partyName;
    idInfo += '\n' + Lang.IDC.gender + Content.gender;
    idInfo += '\n' + Lang.IDC.folk + Content.nation;
    idInfo += '\n' + Lang.IDC.birthday + Content.bornDay;
    idInfo += '\n' + Lang.IDC.address + Content.certAddress;
    idInfo += '\n' + Lang.IDC.id + Content.certNumber;
    idInfo += '\n' + Lang.IDC.issue + Content.certOrg;
    idInfo += '\n' + Lang.IDC.usefulLife + Content.effDate + '-' + Content.expDate;
    idInfo += '\n' + Lang.IDC.path + Content.picAddress;

    $("#resultArea").val(idInfo);

    if(v[0].cardaimg) {
        DisplayImg(v[0].cardaimg);
    }
    if(v[0].cardfimg) {
        DisplayImg(v[0].cardfimg);
    }
    if(v[0].cardbimg) {
        DisplayImg(v[0].cardbimg);
    }
    if(v[0].cardhimg) {
        DisplayImg(v[0].cardhimg);
    }

}
//添加图片项
function DisplayImg(imgBase64Str) {
    var h = '<div><img width="360" style="padding:5px;" src="' + 'data:' + "image/jpeg" + ';base64,' + imgBase64Str + '"></div>';
    $("#image_data").prepend(h);
}

// 清空图片展示列表
function clearImageFile() {
    $("#image_data").empty();
}

