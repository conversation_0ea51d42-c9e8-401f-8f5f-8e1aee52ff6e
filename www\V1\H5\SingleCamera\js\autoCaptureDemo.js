var ws = null;
var m_isConnectWS = false;
var m_splitTag = "$*$";
var m_closed = false; //是否被关闭了
var m_autoCaptureTime = 0;
var m_autoCaptureType = 0;

function isIE() {
    if (!!window.ActiveXObject || "ActiveXObject" in window)
        return true;
    else
        return false;
}

Date.prototype.toLocaleString = function () {
    var y = this.getFullYear(), m = this.getMonth() + 1, d = this.getDate(), h = this.getHours(),
        mi = this.getMinutes(), s = this.getSeconds(), ms = this.getMilliseconds();
    return y + '-' + (m < 10 ? ('0' + m) : m) + '-' + (d < 10 ? ('0' + d) : d) + ' ' + (h < 10 ? ('0' + h) : h) + ':' + (mi < 10 ? ('0' + mi) : mi) + ":" + (s < 10 ? ('0' + s) : s) + '.' + ((ms + 1000) + "").substr(1, 3);
};

//加载页面完成
window.onload = function () {
    if (ws == null) {
        StartWebSocket();
    }
}

//页面卸载
window.onunload = function () {
    if (m_closed) {
        return;
    }
    //反初始化
    sendWsMessage({FuncName: 'camUnInitCameraLib'});
}

//开启webSocket
function StartWebSocket() {
    var url = ((location.protocol==="https:") ? "wss://" : "ws://") + ((location.hostname === "") ? '127.0.0.1' : location.hostname) + ":" + ((location.port === "") ? '9000' : location.port);
    if ('WebSocket' in window) {
        ws = new WebSocket(url);
    } else if ('MozWebSocket' in window) {
        ws = new MozWebSocket(url);
    } else {
        alert("浏览器版本过低，请升级您的浏览器。\r\n浏览器要求：IE10+/Chrome14+/FireFox7+/Opera11+");
    }

    ws.onopen = function () {
        m_isConnectWS = true;
        sendWsMessage({FuncName: 'camRegCallBackDeviceChange'});
        sendWsMessage({FuncName: 'camInitCameraLib'});
        m_closed = false;
    };

    ws.onmessage = function (evt) {
        if (typeof (evt.data) == "string") {
            var str = evt.data;
            if (str.length <= 0) {
                return;
            }
            if (str.indexOf(m_splitTag) >= 0) {
                //视频的每一帧
                var strs = new Array();
                strs = str.split(m_splitTag);
                setImageWithBase64(strs[1]);
            } else {
                //处理其他请求
                console.log(str);
                handleJsonStrMessage(str);
            }
        }
    };

    ws.onclose = function () {
        m_isConnectWS = false;
        var myimg = document.getElementById("video");
        myimg.src = "images/load1.gif";
        StartWebSocket();
    };
}

function sendWsMessage(jsonObj) {
    var jsonStr = JSON.stringify(jsonObj);
    ws.send(jsonStr);
}

// 处理命令返回的结果
function handleJsonStrMessage(str) {
    var json = JSON.parse(str);
    var re = json.result;
    switch (json.FuncName) {
        case "camInitCameraLib":
            //打开摄像头
            sendWsMessage({FuncName: 'camOpenDev', argument: {devIndex: 0, subtype: 0, width: 0, height: 0}});
            //开始预览
            sendWsMessage({FuncName: 'camStartPreview'});
            break;

        case "camOpenDev":
            if (re == 0) {
                // 设置自动裁切，参数: 0：不自动 1：单图裁切 2：多图裁切
                sendWsMessage({FuncName: 'camSetImageAutoCrop', argument: {CropType: 1}});
                // 设置自动连拍，type:0自动监测模式，1定时模式  param:回调中数值上限
                m_autoCaptureTime = 4;
                m_autoCaptureType = 0;
                sendWsMessage({FuncName: "camStartAutoCapture", argument: {type: m_autoCaptureType, param: m_autoCaptureTime}});
            } else {
                alert("打开失败：" + re);
            }
            break;

        case "camSetImageAutoCrop":
            if (re != 0) {
                alert("自动裁切失败");
            }
            break;

        case "camStartAutoCapture":
            if (re == "0") {
                if (m_autoCaptureType == 0) {
                    alert("图像自动检测 连拍开启成功，请适时调整要拍照的物品。");
                } else {
                    alert("定时连拍开启成功");
                }
            }
            break;

        case "camStopAutoCapture":
            if (re == "0") {
                alert("停止连拍成功");
                AutoCaptureBack("-1000");
            }
            break;

        case "AutoCaptureBack":
            //连拍回调
            AutoCaptureBack(re);
            break;
    }
}

//连拍
function continuCapture() {
    // 连拍时不指定filePath参数，则返回base64的照片内容,而不是文件名
    m_autoCaptureTime = 4;
    m_autoCaptureType = 0;
    sendWsMessage({FuncName: 'camStartAutoCapture', argument: {type: m_autoCaptureType, param: m_autoCaptureTime}});
}

//定时连拍
function timeCapture() {
    m_autoCaptureTime = 4;
    m_autoCaptureType = 1;
    sendWsMessage({FuncName: 'camStartAutoCapture', argument: {type: m_autoCaptureType, param: m_autoCaptureTime}});
}

//停止连拍
function StopAutoCapture() {
    sendWsMessage({FuncName: 'camStopAutoCapture', argument: {}});
}

//处理回调
function AutoCaptureBack(re) {
    var progress0 = document.getElementById("autoCaptureProgress");
    if (re == "-1000") {
        progress0.value = "0";
    } else {
        var myimg = document.getElementById("imgCapture");
        myimg.src = "data:image/jpg;base64," + re;

        document.getElementById("captureInfo").innerText = "自动拍照成功, " + new Date().toLocaleString();

        progress0.value = "100";
        setTimeout(function () {
            progress0.value = "0";
        }, m_autoCaptureTime / 2 * 1000);
    }
}

//显示每一帧
function setImageWithBase64(str) {
    var myimg = document.getElementById("video");
    myimg.src = "data:image/jpg;base64," + str;
}
