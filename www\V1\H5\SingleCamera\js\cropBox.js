var _instanceCB = null;
function CropBox(imgId){
    this.imgObj = document.getElementById(imgId);
    this.imgObj.draggable = false;
    // startX, startY 为鼠标点击时初始坐标
    this.startX = -1, this.startY = -1;
    // diffX, diffY 为鼠标初始坐标与 cropBox 左上角坐标之差，用于拖动
    this.diffX = -1, this.diffY = -1;
    // 是否拖动，初始为 false
    this.dragging = false;
    // 视频显示区rect
    this.rect = {left:0, top:0, right:0, bottom:0};
    // 视频显示区的上次位置
    this.lastPos = {x:0, y:0};
    this.overBox = null;
    _instanceCB = this;

    document.onselectstart = function() {
        return false;
    };

    this.enable = function(){
        // 鼠标按下
        document.onmousedown = function (e) {
            _instanceCB.getAreaRect();
            if (_instanceCB.inAreaRect(e)) {
                //console.log('imgObj.onmousedown inAreaRect');
                _instanceCB.startX = e.pageX;
                _instanceCB.startY = e.pageY;

                // 如果鼠标在 cropBox 上被按下
                if (e.target.className.match(/cropBox/)) {
                    // 允许拖动
                    _instanceCB.dragging = true;
                    //console.log('this.dragging = true');

                    // 设置当前 cropBox 的 id 为 moving_box
                    if (document.getElementById("moving_box") !== null) {
                        document.getElementById("moving_box").removeAttribute("id");
                    }
                    e.target.id = "moving_box";

                    // 计算坐标差值
                    _instanceCB.diffX = _instanceCB.startX - e.target.offsetLeft;
                    _instanceCB.diffY = _instanceCB.startY - e.target.offsetTop;
                } else {
                    // 在页面创建 cropBox
                    let active_box = document.createElement("div");
                    active_box.id = "active_box";
                    active_box.className = "cropBox";
                    active_box.style.top = _instanceCB.startY + 'px';
                    active_box.style.left = _instanceCB.startX + 'px';
                    active_box.innerHTML = '<a href="javascript:void(0)" onclick="_closeCropBox()" class="cropBox-close"></a>';
                    document.body.appendChild(active_box);
                    active_box = null;
                }
                e.stopPropagation();
            }
        };

        // 鼠标移动
        document.onmousemove = function (e) {
            // console.log('onmousemove _instanceCB.dragging=', _instanceCB.dragging);
            if (_instanceCB.dragging) {
                // 移动，更新 cropBox 坐标
                let mb = document.getElementById("moving_box");
                if (mb) {
                    //console.log('moving_box == _instanceCB.overBox', _instanceCB.overBox == mb);
                    //console.log(mb.style.cursor);
                    let l, t, w, h;
                    switch (mb.style.cursor) {
                        case 'n-resize': //北(上)
                            t = e.pageY, h = mb.clientHeight + (mb.offsetTop - e.pageY);
                            if (h > 5 && t >= _instanceCB.rect.top && t < _instanceCB.rect.bottom - 5) {
                                mb.style.top = t + 'px';
                                mb.style.height = h + 'px';
                            }
                            break;
                        case 'ne-resize': //东北(右上)
                            t = e.pageY, w = e.pageX - mb.offsetLeft, h = mb.clientHeight + (mb.offsetTop - e.pageY);
                            if (h > 5 && (mb.offsetTop + h) < _instanceCB.rect.bottom - 5 &&
                                t >= _instanceCB.rect.top && t < _instanceCB.rect.bottom - 5) {
                                mb.style.top = t + 'px';
                                mb.style.height = h + 'px';
                            }
                            if (w > 5 && (mb.offsetLeft + w) < _instanceCB.rect.right - 5) {
                                mb.style.width = w + 'px';
                            }
                            break;
                        case 'e-resize': //东(右)
                            w = e.pageX - mb.offsetLeft;
                            if (w > 5 && (mb.offsetLeft + w) < _instanceCB.rect.right - 5) {
                                mb.style.width = w + 'px';
                            }
                            break;
                        case 'se-resize': //东南(右下)
                            w = e.pageX - mb.offsetLeft, h = e.pageY - mb.offsetTop;
                            if (w > 5 && (mb.offsetLeft + w) < _instanceCB.rect.right - 5) {
                                mb.style.width = w + 'px';
                            }
                            if (h > 5 && (mb.offsetTop + h) < _instanceCB.rect.bottom - 5) {
                                mb.style.height = h + 'px';
                            }
                            break;
                        case 's-resize': //南(下)
                            h = e.pageY - mb.offsetTop;
                            if (h > 5 && (mb.offsetTop + h) < _instanceCB.rect.bottom - 5) {
                                mb.style.height = h + 'px';
                            }
                            break;
                        case 'sw-resize': //西南(左下)
                            w = mb.clientWidth + (mb.offsetLeft - e.pageX), l = e.pageX, h = e.pageY - mb.offsetTop;
                            if (l >= _instanceCB.rect.left && (l + w) < _instanceCB.rect.right - 5) {
                                mb.style.width = w + 'px';
                                mb.style.left = l + 'px';
                            }
                            if (h > 5 && (mb.offsetTop + h) < _instanceCB.rect.bottom - 5) {
                                mb.style.height = h + 'px';
                            }
                            break;
                        case 'w-resize': //西(左)
                            w = mb.clientWidth + (mb.offsetLeft - e.pageX), l = e.pageX;
                            if (w > 5 && l >= _instanceCB.rect.left && (l + w) < _instanceCB.rect.right - 5) {
                                mb.style.width = w + 'px';
                                mb.style.left = l + 'px';
                            }
                            break;
                        case 'nw-resize': //西北(左上)
                            if (e.pageY >= _instanceCB.rect.top && e.pageY < _instanceCB.rect.bottom - 5) {
                                mb.style.height = mb.clientHeight + (mb.offsetTop - e.pageY) + 'px';
                                mb.style.top = e.pageY + 'px';
                            }
                            if (e.pageX >= _instanceCB.rect.left && e.pageX < _instanceCB.rect.right - 5) {
                                mb.style.width = mb.clientWidth + (mb.offsetLeft - e.pageX) + 'px';
                                mb.style.left = e.pageX + 'px';
                            }
                            break;
                        case '': //移动(中间)
                            l = e.pageX - _instanceCB.diffX, t = e.pageY - _instanceCB.diffY;
                            if (t >= _instanceCB.rect.top && (t + mb.offsetHeight) <= _instanceCB.rect.bottom) {
                                mb.style.top = t + 'px';
                            }
                            if (l >= _instanceCB.rect.left && (l + mb.offsetWidth) <= _instanceCB.rect.right) {
                                mb.style.left = l + 'px';
                            }
                            break;
                    }
                    e.stopPropagation();
                }
            } else {
                // 更新 cropBox 尺寸
                let ab = document.getElementById("active_box");
                if (ab) {
                    if (e.pageX >= _instanceCB.rect.left && e.pageX < _instanceCB.rect.right - 5) {
                        ab.style.width = e.pageX - _instanceCB.startX + 'px';
                    }
                    if (e.pageY >= _instanceCB.rect.top && e.pageY < _instanceCB.rect.bottom - 5) {
                        ab.style.height = e.pageY - _instanceCB.startY + 'px';
                    }
                } else {
                    if (_instanceCB.overBox && _instanceCB.overBox.style.cursor != '' && _instanceCB.inOverBox(e)) {
                        _instanceCB.overBox.style.cursor = '';
                    }
                }
            }
        };

        // 鼠标抬起
        document.onmouseup = function (e) {
            //console.log('imgObj.onmouseup');
            // 禁止拖动
            _instanceCB.dragging = false;
            if (document.getElementById("active_box") !== null) {
                let ab = document.getElementById("active_box");
                ab.removeAttribute("id");
                // 如果长宽均小于 5px，移除 cropBox
                if (ab.offsetWidth < 10 || ab.offsetHeight < 10) {
                    document.body.removeChild(ab);
                }
            }
        };

        document.onmouseover = function(e){
            e = e || window.event;
            let n = e.target || e.srcElement;
            if (window._lastNode != n && n.className == 'cropBox') {
                //console.log( n );
                _instanceCB.overBox = n;
                let x = e.pageX - n.offsetLeft, y = e.pageY - n.offsetTop, dire = '';
                if (y >= 0 && y < 8) {
                    dire += 'n';//北
                } else if (y > n.offsetHeight - 8 && y < n.offsetHeight + 8) {
                    dire += 's';//南
                }
                if (x >= 0 && x < 8) {
                    dire += 'w';//西
                } else if (x > n.offsetWidth - 8 && x < n.offsetWidth + 8) {
                    dire += 'e';//东
                }
                //console.log(dire + '-resize');
                n.style.cursor = dire + '-resize';
                n.className += ' hover';
            } else {
                //console.log('11111111');
                if (n.parentNode != _instanceCB.overBox && n != _instanceCB.overBox) {
                    if (_instanceCB.overBox) {
                        _instanceCB.overBox.className = 'cropBox';
                        _instanceCB.overBox.cursor = '';
                    }
                    _instanceCB.overBox = null;
                }
            }

        }
        this.displayCropBox('');
    };

    this.disable = function(){
        document.onmousedown = null;
        document.onmousemove = null;
        document.onmouseup = null;
        this.displayCropBox('none');
    };

    this.displayCropBox = function(val) {
        let ary = document.getElementsByClassName('cropBox');
        for (let i = 0; i < ary.length; i++) {
            ary[i].style.display = val;
        }
    };

    this.getAreaRect = function() {
        let e = this.imgObj, left = e.offsetLeft, top = e.offsetTop;
        while ((e = e.offsetParent) != null) {
            top += e.offsetTop;
            left += e.offsetLeft;
        }
        this.rect.left = left;
        this.rect.top = top;
        this.rect.right = left + this.imgObj.offsetWidth;
        this.rect.bottom = top + this.imgObj.offsetHeight;
    };

    this.inAreaRect = function(e) {
        return e.pageX >= this.rect.left && e.pageX <= this.rect.right && e.pageY >= this.rect.top && e.pageY <= this.rect.bottom;
    };

    this.inOverBox = function(e) {
        if (this.overBox) {
            let x = e.pageX - this.overBox.offsetLeft, y = e.pageY - this.overBox.offsetTop;
            return x > 8 && y > 8 && x < (this.overBox.offsetWidth - 8) && y < (this.overBox.offsetHeight - 8);
        }
        return false;
    };

    // 获取CropBox的数组:[视频框的宽, 视频框的高, 框1的left, 框1的top, 框1的right, 框1的bottom, 框2的left, 框2的top, ....]
    // 各框的位置,以视频框的左上角为原点(0,0)
    this.getCropBoxArray = function() {
        this.getAreaRect();
        let ary = new Array(), i = 0;
        ary[i++] = this.imgObj.offsetWidth;
        ary[i++] = this.imgObj.offsetHeight;

        let boxes = document.getElementsByClassName('cropBox'), box = null, left, top;
        for (let j = 0; j < boxes.length; j++) {
            box = boxes[j];
            left = box.offsetLeft - this.rect.left;
            top = box.offsetTop - this.rect.top;
            ary[i++] = left;
            ary[i++] = top;
            ary[i++] = left + box.clientWidth;
            ary[i++] = top + box.clientHeight;
        }
        return ary;
    };
}

function _closeCropBox(e) {
    e = e || window.event;
    let a = e.target || e.srcElement;
    document.body.removeChild(a.parentNode);
}