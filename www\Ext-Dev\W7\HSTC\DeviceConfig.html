<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>设备配置信息 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li class="active"><a href="#">设备配置信息</a></li>
                <li><a href="Slideshow.html">轮播图片设置</a></li>
                <li><a href="SignAuthorization.html">签署诚信授权</a></li>
                <li><a href="CertifiedSalvageQualification.html">救助资格认证</a></li>
<!--                <li><a href="TakePhoto.html">文档拍照</a></li>-->
            </ul>
        </div>
    </div>
</nav>

<div style="float: left;">
    <img id="product" src="image/W7_2.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>修改设备配置信息</h1>
        <p>这个页面演示了如何查询与修改修改安卓多功能终端设备上的配置信息。</p>
    </div>

    <div class="page-header"></div>

    <div class="col-sm-12" style="margin-bottom: 10px;text-align: center;">
        <p>
            <label style="margin-left:20px;">设备编号：
                <input type="text" style="width:400px;" value="SN000168" id="deviceNumber">
            </label><br>
            <label style="margin-left:20px;">公司地址：
                <input type="text" style="width:400px;" value="北京市海淀区西北旺东路10号院东区23号楼" id="address">
            </label><br>
            <label style="margin-left:20px;">公司名称：
                <input type="text" style="width:400px;" value="北京华胜天成科技股份有限公司" id="technologyCompany">
            </label><br>
            <label style="margin-left:20px;">支持电话：
                <input type="text" style="width:400px;" value="010-80986698" id="technologyPhone">
            </label><br><br>
            <button id="clearBtn" type="button" class="btn btn-primary" onclick="clearInput()">清  空</button>
            <button id="defBtn" type="button" class="btn btn-primary" onclick="defaultInput()" style="margin-left:35px">默认值</button>
            <button id="getBtn" type="button" class="btn btn-primary" onclick="queryDevConfig()" style="margin-left:35px">查  询</button>
            <button id="setBtn" type="button" class="btn btn-primary" onclick="configDev()" style="margin-left:35px">修  改</button>
        </p>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/DeviceConfig.js"></script>
</body>
</html>