<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>自动拍照-自定义处理</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../CSS/common.css">

    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../JS/common/html5shiv.min.js"></script>
    <script src="../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>自动检测图像自定义处理 演示</h1>
        <p>页面会自动打开高拍仪。按【开始】按钮，以自动检测图像变化（detectionType:0）、只发送通知不拍照（notify:1）的方式开启自动拍摄（CameraAutoCapture）。在收到触发通知后，调用RecogBarCode来识别条码或二维码，在RecogBarCode的返回结果中，若没有条码内容，则调用CameraCaptureBook进行拍书。页面中也可对拍照的照片进行OCR识别、PDF文件合并。</p>
    </div>

    <div class="col-md-8" style="text-align: center; width:66%;">
        <p>
            <label>设备:</label>
            <select id="cameraInfo" style="width:135px;" disabled onchange="switchCamera()"></select>
            <label style="margin-left:12px">视频格式:</label>
            <select id="mediaType" style="width:70px;" disabled onchange="ReOpenCamera()"></select>
            <label style="margin-left:12px">分辨率:</label>
            <select id="resolution" style="width:95px;" onchange="ReOpenCamera()"></select>
            <label style="margin-left:12px">参数:</label>
            <input type="number" id="param" min="2" max="20" value="5" style="width: 40px;" title="这个参数会影响CameraAutoCaptures检测图像变化的灵敏度，越小越灵敏">
            <button type="button" class="btn btn-primary" id="btnAuto" style="margin-left:12px" onclick="SwitchAuto()">开始</button>
        </p>
        <img id="video" src="" style="width:640px;height:480px;">
        <p>文档摄像头视频</p>
    </div>

    <div class="col-md-4" style="text-align:left; padding-left:0; padding-right:0;">
        <label>OCR语言:</label>
        <select id="language" style="width:260px"></select><br>
        <label>输出文件类型:</label>
        <select id="fileType" style="width:120px;margin-right:50px"></select>
        <label class="checkbox-inline" title="OCR识别时是否检测文字方向,检测文字方向会慢一些,如果图像文件的文字能保证是正向的,可以不检测文字方向">
            <input type="checkbox" id="detectTextOrie" checked><b>检测文字方向</b>
        </label>
        <br>
        <button type="button" class="btn btn-primary" id="btnMergePDF" onclick="mergePDF()" style="margin-right:56px">合并PDF</button>
        <button type="button" class="btn btn-primary" id="btnOCR" onclick="ocrFile()" style="margin-right:56px">OCR识别</button>
        <button type="button" class="btn btn-primary" id="btnClearFile" onclick="clearImageFile()">清空文件</button>
        <div class="col-sm-12" style="padding: 0;margin:4px 0;overflow-y: scroll;height:200px;">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th width="60%" style="text-align: center;">文件名</th>
                    <th style="text-align: center;">操作</th>
                </tr>
                </thead>
                <tbody id="tabBody">
                </tbody>
            </table>
        </div>
        <label>条码/二维码识别结果:</label>
        <button type="button" class="btn btn-xs btn-primary" id="btnClearBarcode" onclick="clearBarcode()">清空结果</button><br>
        <div class="col-sm-12" style="padding: 0;margin:4px 0;overflow-y: scroll;height:200px;">
            <table class="table table-bordered" style="table-layout: fixed;width:365px;">
                <thead>
                <tr>
                    <th style="width:280px;text-align: center;">条码/二维码</th>
                    <th style="width:80px;text-align: center;">类型</th>
                </tr>
                </thead>
                <tbody id="tabBody2">
                </tbody>
            </table>
        </div>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<audio id="audioCapture">
    <source src="../../audio/Capture.wav" type="audio/wav">
</audio>

<!-- Modal -->
<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">拍照文件查看</h4>
            </div>
            <div class="modal-body">
                <img id="imgView" src="" style="max-width:860px">
<!--                <img id="imgView2" src="" style="max-width:860px">-->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="vertical-line" style="display: none"></div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../langs/zhCN.js"></script>
<script src="../../JS/MzWsConnect.js"></script>
<script src="../../JS/camera/AutoCaptureCustom.js"></script>
</body>
</html>