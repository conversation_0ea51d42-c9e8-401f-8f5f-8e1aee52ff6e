import request from '@/utils/request'
import { parseStrEmpty } from "@/utils/ruoyi";

// 查询资质类型列表
export function listClass(query) {
    return request({
      url: '/credentclass/doQuery',
      method: 'post',
      data: query
    })
}

// 查询资质列表
export function listCredent(query) {
  return request({
    url: '/credent/doQuery',
    method: 'post',
    data: query
  })
}


// 查询类型详细
export function getClass(credentclassId) {
    return request({
      url: '/credentclass/' + parseStrEmpty(credentclassId),
      method: 'get'
    })
}

// 查询资质详细
export function getCredent(credentId) {
  return request({
    url: '/credent/' + parseStrEmpty(credentId),
    method: 'get'
  })
}

// 新增分类
export function addClass(data) {
    return request({
      url: '/credentclass/doAdd',
      method: 'post',
      data: data
    })
}

// 新增资质
export function addCredent(data) {
  return request({
    url: '/credent/doAdd',
    method: 'post',
    data: data
  })
}

// 修改分类
export function updateClass(data) {
    return request({
      url: '/credentclass/doEdit',
      method: 'post',
      data: data
    })
}

// 修改资质
export function updateCredent(data) {
  return request({
    url: '/credent/doEdit',
    method: 'post',
    data: data
  })
}

// 删除隐患
export function delCredent(credentId) {
  return request({
    url: '/credent/doDelete/' + credentId,
    method: 'post'
  })
}

// 检测日期
export function caleNextDate(credentId,checkDate) {
  return request({
    url: '/credent/' + parseStrEmpty(credentId)+'/caleNextDate',
    method: 'get',
    params:checkDate
  })
}
