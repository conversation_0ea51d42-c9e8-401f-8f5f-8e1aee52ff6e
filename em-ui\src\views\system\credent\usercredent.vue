<template>
  <div class="app-container" style="background-color:white;">
    <h4 class="form-header h4">基本信息</h4>
    <el-form ref="form" :model="form" label-width="80px">
      <el-row>
        <el-col :span="8" :offset="2">
          <el-form-item label="姓名" prop="workerName">
            <el-input v-model="form.workerName" disabled />
          </el-form-item>
        </el-col>
        <el-col :span="8" :offset="2">
          <el-form-item label="工号" prop="workerCode">
            <el-input  v-model="form.workerCode" disabled />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <h4 class="form-header h4">资质信息</h4>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" >新增</el-button>
      </el-col>

      
      <right-toolbar  @queryTable="getList" ></right-toolbar>
    </el-row>

    <el-table v-loading="loading"  ref="table" @selection-change="handleSelectionChange" :data="userCredentList">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="序号" type="index" align="center">
        <template slot-scope="scope">
          <span>{{(pageNum - 1) * pageSize + scope.$index + 1}}</span>
        </template>
      </el-table-column>
      <el-table-column label="资质类型" align="center"  prop="credentclass.className" />
      <el-table-column label="资质编号" align="center"  prop="credentNo" />
      <el-table-column label="资质名称" align="center"  prop="credentName" />
      <el-table-column label="本次检测时间" align="center"  prop="checkDate" />
      <el-table-column label="下次检测时间" align="center"  prop="checkNextdate" />
      
     <el-table-column prop="state" label="状态" width="100">
        <template slot-scope="scope">
          <span v-if="scope.row.state==1">正常</span>
          <span v-if="scope.row.state==2">停用</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center"  key="creationtime"  prop="creationtime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.creationtime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="160" class-name="small-padding fixed-width">
        <template slot-scope="scope" >
           <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" >修改</el-button>
           <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" >删除</el-button>
           <el-button size="mini" type="text" icon="el-icon-s-order" @click="handleCheck(scope.row)" >检测日期</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"  @pagination="getList"/>

     <!-- 添加或修改用户配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="usercredentform" :model="usercredentform" :rules="rules" label-width="80px">
        <el-row>
            <el-form-item label="类型" prop="credentclassId">
              <el-select v-model="usercredentform.credentclassId" placeholder="请选择类型" clearable>
                  <el-option v-for="dict in classList" :key="dict.credentclassId" :label="dict.className" :value="dict.credentclassId"/>
              </el-select>
            </el-form-item>
        </el-row>
        <el-form-item label="资质编号" prop="credentNo">
          <el-input v-model="usercredentform.credentNo" placeholder="请输入资质编号" />
        </el-form-item>
        <el-form-item label="资质名称" prop="credentName">
          <el-input v-model="usercredentform.credentName" placeholder="请输入资质名称" />
        </el-form-item>
        <el-form-item label="关键词" prop="keyword">
          <el-input v-model="usercredentform.keyword" placeholder="请输入关键词" />
        </el-form-item>
        <el-form-item label="内容">
              <editor v-model="usercredentform.content" :min-height="192"/>
         </el-form-item>
         <el-form-item label="备注" prop="notes">
          <el-input v-model="usercredentform.notes" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="状态">
            <el-radio-group v-model="usercredentform.state">
            <el-radio
                v-for="dict in options"
                :key="dict.value"
                :label="dict.value"
            >{{dict.label}}</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item label="文档" prop="filepaths">
            <file-upload v-model="filepaths" :limit="3"  />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog :title="title" :visible.sync="checkopen" width="600px" append-to-body>
      <el-form ref="checkform" :model="checkform" label-width="80px">
        <el-form-item label="检测日期" prop="checkDate">
          <el-date-picker v-model="checkform.checkDate" type="date" placeholder="选择日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCheck">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { listCredent,listClass,addCredent,updateCredent,getCredent,delCredent,caleNextDate} from "@/api/credent/credent";
import { getUser } from "@/api/system/user";

export default {
  name: "AuthRole",
  dicts: ['system_worktype','system_job'],
  data() {
    return {
       // 遮罩层
      loading: true,
      // 分页信息
      total: 0,
      pageNum: 1,
      pageSize: 10,
      // 选中角色编号
      roleIds:[],
      // 角色信息
      roles: [],
      // 用户信息
      form: {},
      //用户资质信息
      userCredentList:[],
      //资质类型
      classList:[],
       // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        workorId: undefined
      },
      //用户资质信息
      usercredentform:{},
      //检测日期
      checkform:{},
      // 是否显示弹出层
      open:false,
      //是否检测日期弹出层
      checkopen:false,
         //状态
      options: [{
          value: 1,
          label: '正常'
        }, {
          value: 2,
          label: '停用'
      }],
      //弹出标题
      title:undefined,
       // 部门树选项
      deptOptions: undefined,
       optionValue: undefined,
       initValue:undefined,
       //文档路径
      filepaths:[],
      // 表单校验
      rules: {
        credentclassId: [
          { required: true, message: "类型不能为空", trigger: "change" }
        ],
        credentNo: [
          { required: true, message: "编号不能为空", trigger: "blur" }
        ],
        credentName: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        state: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    const userId = this.$route.params && this.$route.params.userId;
    console.log(userId)
    this.queryParams.workorId=userId
    this.usercredentform.workorId=userId
    if (userId) {
      this.getUser();
      this.getList();
      this.getClass();
    }
  },
  methods: {

    getList(){
      this.loading = true;
      this.queryParams.orderBy="creationtime desc"
      listCredent(this.queryParams).then((response)=>{
          this.userCredentList = response.data;
          this.total = response.totalCount;
          this.loading = false;
      })
    },
    //查询类型
    getClass(){

        listClass({"state":1}).then(response => {
          this.classList = response.data;
        }
      );
    },
    getUser(){
      getUser(this.queryParams.workorId).then((response) => {
        this.form = response.data;
        
      });
    },
    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    // 表单重置
    reset() {
      this.usercredentform = {
        credentclassId: undefined,
        credentNo: undefined,
        credentName: undefined,
        keyword: undefined,
        content: undefined,
        checkDate:undefined,
        notes:undefined,
        state:1,
      };
      this.resetForm("usercredentform");
    },
    //新增用户部门
    handleAdd(){
        this.reset();
        this.open = true;
        this.title = "新增资质";
    },
    //检测日期
    handleCheck(row){
      const credentId = row.credentId || this.ids;
      this.checkform.credentId=credentId
      this.checkopen=true;
      this.title = "资质检测";
    },
    handleUpdate(row){
      this.reset();
      this.filepaths=[]
      const credentId = row.credentId || this.ids;
      getCredent(credentId).then(response => {
        this.usercredentform = response.data;
        if(typeof(this.usercredentform.filepaths)!="undefined"){
          for(let i=0; i<this.usercredentform.filepaths.length; i++){
            let filepath = {}
            filepath.name=this.usercredentform.filepaths[i].fileName
            filepath.url=this.usercredentform.filepaths[i].filePath
            this.filepaths.push(filepath)
          }
        }
        this.open = true;
        this.title = "修改资质";

      });
    },
     /** 提交按钮 */
    submitForm(){
      this.$refs["usercredentform"].validate(valid => {
        if (valid) {

          if(typeof(this.filepaths) !=="undefined" && this.filepaths.length > 0){
            this.usercredentform.filepaths=[]
            for(let i=0; i<this.filepaths.length; i++){
              let filepath = {};
              filepath.filePath=this.filepaths[i].url;
              filepath.fileName=this.filepaths[i].name;
              this.usercredentform.filepaths.push(filepath);
            }
          }
          this.usercredentform.workorId= this.queryParams.workorId
          if (this.usercredentform.credentId != undefined) {
            updateCredent(this.usercredentform).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addCredent(this.usercredentform).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const credentId = row.credentId || this.ids;
      this.$modal.confirm('是否确认删除资质?').then(function() {
        return delCredent(credentId);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    //提交检测日期
    submitCheck(){
      console.log(this.checkform.checkDate)
      if(typeof(this.checkform.checkDate)!==undefined && this.checkform.checkDate != ''){
        caleNextDate(this.checkform.credentId,{"checkDate":this.checkform.checkDate}).then(response=>{
           this.$modal.msgSuccess("新增成功");
           this.checkopen=false;
            this.getList();
        })
      }else{
        this.$modal.msgError("检测日期不能为空")
      }
    },
    changeOption(item) {
            console.log('select======', this.initValue,this.optionValue);
    },

    /** 单击选中行数据 */
    clickRow(row) {
      this.$refs.table.toggleRowSelection(row);
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.roleIds = selection.map((item) => item.roleId);
    },
    // 保存选中的数据编号
    getRowKey(row) {
      return row.roleId;
    },

    /** 关闭按钮 */
    close() {
      const obj = { path: "/system/user" };
      this.$tab.closeOpenPage(obj);
    },
       // 取消按钮
    cancel() {
      this.open = false;
      this.checkopen=false;
      this.checkform={}
      this.reset();
    },
  },
};
</script>