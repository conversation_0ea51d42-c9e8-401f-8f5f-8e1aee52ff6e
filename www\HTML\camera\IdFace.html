<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>人证比对</title>
    <script src="../../JS/common/jquery-2.0.3.min.js"></script>
    <script src="../../langs/zhCN.js"></script>
    <script src="../../JS/MzWsConnect.js"></script>
    <script src="../../JS/camera/idFace.js"></script>
    <link rel="stylesheet" href="../../CSS/camera/idFace.css"/>
</head>
<body>
<h2>人证比对</h2>
<p>此Demo演示人证比对的场景。最好是有身份证读卡器和双目摄像头，至少需要一台带副头的高拍仪。双目摄像头在活体检测的准确性方面，比一般摄像头高很多。</p>
<table cellspacing="0" cellpadding="6" class="tb1" border="1">
    <tr>
        <td align="center" valign="middle" width="36%"><b>选择或获取标准头像</b></td>
        <td align="center" valign="middle" width="32%"><b>视频及比对参数</b></td>
        <td align="center" valign="middle" width="32%"><b>比对结果及匹配头像</b></td>
    </tr>
    <tr>
        <td height="120">
            直接 <button onclick="openHeadImage()">打开头像文件</button>
            <input id="imgFile" type="file" onchange="chooseImgFile()" accept="image/jpg, image/jpeg"
                   style="display: none">
            <br>
            或者 有高拍仪 <select id="selDev1" style="width:145px;"></select>
            <button onclick="openCamera1()">打开高拍仪</button>
            然后
            <button onclick="capterImg()">拍身份证正面</button>
            <br>
            或者 有身份证读卡器
            <button onclick="readIdCard()">读取二代身份证</button>
            <br>身份证信息: <br><textarea id="idCardInfo" readonly="true"></textarea>
        </td>
        <td>
            <input type="checkbox" checked id="chkLiveCheck"><label for="chkLiveCheck">开启活体检测</label><br>
            人像匹配的最小相似度(0-100)<input id="inputSimilarity" type="number" value="80" style="width:50px"/><br>
            停止方式：
            <select id="selStopMode">
                <option value="0">手动停止</option>
                <option value="1" selected>匹配成功自动停止</option>
                <option value="2">定时自动停止</option>
            </select><br>
            运行<input id="inputRunSeconds" type="number" value="60" style="width:50px"/>秒后自动停止<br>
            <input type="checkbox" checked id="chkSendMatchedImg"><label
                for="chkSendMatchedImg">匹配成功时回发的结果中包含匹配的图像</label><br>
            <input type="checkbox" checked id="chkPlayWav"><label for="chkPlayWav">匹配成功时播放声音</label><br>
            人像摄像头:<select id="selDev2" style="width:145px;"></select>
            <button onclick="openCamera2()">打开人像摄像头</button>
            <button onclick="startStopMatch()" id="btnStartStop">开始比对</button>

        </td>
        <td class="right" align="center" valign="middle" style="line-height: 48px; font-size: 32px;">
            <span id="state1" style="color:darkgrey;font-size:28px">彩色视频中检测到人脸</span><br>
            <span id="state2" style="color:darkgrey;font-size:28px">人脸比对结果:</span>
            <span id="state5" style="color:green;font-size:28px"></span>
            <span id="state7" style="color:darkgrey;font-size:28px"></span><br>

            <span id="state3" style="color:darkgrey;font-size:28px">红外视频中检测到人脸</span><br>

            <span id="state4" style="color:darkgrey;font-size:28px">活体检测结果:</span>
            <span id="state6" style="color:green;font-size:28px"></span>
            <span id="state8" style="color:darkgrey;font-size:28px"></span><br>
        </td>
    </tr>
    <tr>
        <td>
            视频或头像:<br>
            <img id="imgHead" class="stdImg"/>
        </td>
        <td>
            人像视频:<br>
            <img id="imgVideo" class="stdImg"/>
        </td>
        <td>
            匹配的头像:<br>
            <img id="imgMatched" class="stdImg"/>
        </td>
    </tr>
</table>
<div id="outInfo" style="display:none">123</div>
</body>
</html>
