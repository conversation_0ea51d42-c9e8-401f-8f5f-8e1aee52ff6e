import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/organization/doQuery',
    method: 'post',
    data: query
  })
}

// 查询目标责任书
export function listLiability(query) {
  return request({
    url: '/responsibility/doQuery',
    method: 'post',
    data: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(query) {
  return request({
    url: '/organization/getList1',
    method: 'get',
    params: query
  })
}

// 查询部门详细
export function getDept(organId) {
  return request({
    url: '/organization/' + organId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/organization/doAdd',
    method: 'post',
    data: data
  })
}

// 新增目标
export function addDuty(data) {
  return request({
    url: '/responsibility/doAdd',
    method: 'post',
    data: data
  })
}


// 修改部门
export function updateDept(data) {
  return request({
    url: '/organization/doEdit',
    method: 'post',
    data: data
  })
}

// 删除部门
export function delDept(organId) {
  return request({
    url: '/organization/doDelete/' + organId,
    method: 'post'
  })
}

// 删除目标责任书
export function delDuty(responsibilityId) {
  return request({
    url: '/responsibility/doDelete/' + responsibilityId,
    method: 'post'
  })
}