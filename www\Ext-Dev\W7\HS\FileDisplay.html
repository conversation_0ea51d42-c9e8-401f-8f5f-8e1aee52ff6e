<!doctype html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>文件播放演示 - 多功能终端</title>

    <!-- Bootstrap -->
    <link rel="stylesheet" href="../../../bootstrap/css/bootstrap.min.css">
    <link rel="stylesheet" href="css/common.css">
    <!-- HTML5 shim 和 Respond.js 是为了让 IE8 支持 HTML5 元素和媒体查询（media queries）功能 -->
    <!-- 警告：通过 file:// 协议（就是直接将 html 页面拖拽到浏览器中）访问页面时 Respond.js 不起作用 -->
    <!--[if lt IE 9]>
    <script src="../../../JS/common/html5shiv.min.js"></script>
    <script src="../../../JS/common/respond.min.js"></script>
    <![endif]-->
</head>
<body>

<!-- Fixed navbar -->
<nav class="navbar navbar-inverse navbar-fixed-top">
    <div class="container">
        <div class="navbar-header">
            <a class="navbar-brand">多功能终端演示</a>
        </div>
        <div id="navbar" class="navbar-collapse collapse">
            <ul class="nav navbar-nav">
                <li><a href="StartAPP.html">启动APP</a></li>
                <li class="active"><a href="#">文件播放</a></li>
                <li><a href="IdCard.html">身份证读取</a></li>
                <li><a href="ReadFinger.html">指纹读取</a></li>
                <li><a href="ReadSign.html">手写签名</a></li>
                <li><a href="ReadKeyboard.html">评价按键读取</a></li>
                <li><a href="IdCardMatch.html">人证比对</a></li>
                <li><a href="DocOCR.html">OCR识别与水印</a></li>
                <li><a href="Barcode.html">条码扫描与识别</a></li>
                <li><a href="LayoutRecognition.html">版面识别</a></li>
            </ul>
        </div>
    </div>
</nav>

<div class="col-md-2">
    <img id="product" src="image/W7-0911.png">
</div>

<div class="container theme-showcase" role="main">
    <div class="jumbotron">
        <h1>文件播放 演示</h1>
        <p>这个页面会显示存放在安卓多功能终端上的媒体文件；点击“播放文件”近钮，会在安卓多功能终端上播放显示这个多媒体文件。</p>
    </div>

    <div class="page-header">
    </div>
    <ul id="fileTypes" class="nav nav-tabs" role="tablist">
    </ul>
    <div class="row">
        <div class="col-sm-12">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th width="70%">文件名</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody id="tabBody">
                </tbody>
            </table>
        </div>
        <div class="col-sm-12" style="margin-bottom: 10px;">
            <div class="row">
                <div class="input-group" style="margin-left: 1%;width:50%;float:left;">
                    <input type="text" class="form-control" placeholder="请选择文件" />
                    <span class="input-group-btn">
                        <label for="forexIO_file" class="form-control btn btn-primary">选择文件</label>
                        <input id="forexIO_file" type="file" style="display:none" accept=".jpg,.mp3,.mp4,.gif,.pdf,.html"/>
                </span>
                </div>
<!--                <input type="file" id="upload_file" class="file-input" accept=".jpg,.mp3,.mp4,.gif,.pdf,.html" style="margin-left: 1%;width:50%;float:left;">-->
                <p><button id="uploadBtn" type="button" class="btn btn-primary" onclick="uploadFile()" style="float:left; margin-left:5px" disabled>上传文件</button></p>
            </div>
        </div>
    </div>

    <div id="outInfo" class="alert alert-danger col-md-12" role="alert" style="display:none">
    </div>
</div>

<!-- jQuery (Bootstrap 的所有 JavaScript 插件都依赖 jQuery，所以必须放在前边) -->
<script src="../../../JS/common/jquery-2.0.3.min.js"></script>
<!-- 加载 Bootstrap 的所有 JavaScript 插件。你也可以根据需要只加载单个插件。 -->
<script src="../../../bootstrap/js/bootstrap.min.js"></script>
<script src="../../../langs/zhCN.js"></script>
<script src="JS/FileDisplay.js"></script>
</body>
</html>