﻿<!DOCTYPE html>
<html lang="zh-cmn-Hans-CN">
<head>
    <title>高拍仪多浏览器示例网页</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <script src="js/globalVar.js" type="text/javascript" charset="utf-8"></script>
    <script src="js/main.js" type="text/javascript" charset="utf-8"></script>
</head>   
<body onload="load();initdevcolor();" onunload="unload()">
<script LANGUAGE=JavaScript>
	function setjpgqual()
	{
		//设置压缩比, 第一个参数为摄像头索引(取值0, 1, 2...), 第二个参数为jpg质量即压缩比取值1-100
		setJpgQuality(0, 56);
	}

	//拍照并上传
	function cap2server()
	{
		cap2HttpServer(0, "http://yourserver.addr/demo.jsp");
	}

	function facecomp()
	{
		compareFace();
	}

	function readbarcode()
	{
		readBarcode();
	}

	function imagebase64()
	{
		image2Base64("d:\\temp\\image00000222.jpg");
	}

	function getcamindex()
	{
		getcamidx("1b17&0209");
	}

	function changeclr()
	{
		var obj1=document.getElementById("curDev"); 
		var idx = obj1.selectedIndex;    

		var obj=document.getElementById("curColor"); 
		var val = obj.selectedIndex; 
		setVideoColor(idx,  val);
	}

	function initdevcolor()
	{
		var obj = document.getElementById("curColor"); 
		var opt = new Option("彩色", obj.length );
		obj.options.add(opt); 
		opt = new Option("灰度", obj.length );
		obj.options.add(opt); 
		opt = new Option("黑白", obj.length );
		obj.options.add(opt); 
		document.getElementById("curColor").value=0;  
	}

	function capbase64()
	{
		var obj1=document.getElementById("curDev"); 
		var idx = obj1.selectedIndex;    

		cap2base64(idx);
	}

	function makePDF()
	{
		var pdfPath = "d:\\123.pdf";
		var imgPath = "d:\\1.jpg@d:\\2.jpg@d:\\3.jpg";
		createpdf(pdfPath, imgPath);
	}

	//删除指定目录下所有图片
                function deleleAllImages()
	{
		deleteImages("d:\\temp");
	}

    //显示指定图片
    function showImage()
	{
		openImage("d:\\temp\\123.jpg");
	}

    //关闭浏览器提示信息
	window.onbeforeunload = function (e) {
		unload(); 
	}
	
</script>
<img id="myCanvas" width='640' height='480' align="left"/>
<br>
当前设备：<select id="curDev" style="width: 90px" name="selDev"
										onchange="changedev()"></select>
<input id="rotatecrop" type="checkbox" value="" onclick="RotateCrop(this)" />纠偏裁边&nbsp;&nbsp;
   颜色：<select id="curColor" style="width: 90px" name="curRes"
										onchange="changeclr()"></select>
<br>
<br>
     <input   TYPE="button"   VALUE="  开始预览 "   onClick="start_preview()" > 
     <input   TYPE="button"   VALUE="停止预览"   onClick="stop_preview()"> 
     <input   TYPE="button"   VALUE="拍照"   onClick="capture()"> 
     <input   TYPE="button"   VALUE="读取身份证"   onClick="readidcard()">
     <input   TYPE="button"   VALUE="左转"   onClick="rotleft()"> 
     <input   TYPE="button"   VALUE="右转"   onClick="rotright()"> 
     <input   TYPE="button"   VALUE="放大"   onClick="zoomin()"> 
     <input   TYPE="button"   VALUE="缩小"   onClick="zoomout()"> 
     <input   TYPE="button"   VALUE="设置压缩比"   onClick="setjpgqual()">
<br><br>
     <input   TYPE="button"   VALUE="拍照上传"   onClick="cap2server()">
     <input   TYPE="button"   VALUE="条码识别"   onClick="readbarcode()">
     <input   TYPE="button"   VALUE="图片转base64"   onClick="imagebase64()">
     <input   TYPE="button"   VALUE="获取摄像头索引"   onClick="getcamindex()">
<input   TYPE="button"   VALUE="拍照为Base64"   onClick="capbase64()">
     <input TYPE="button"  VALUE="删除图片" onClick="deleleAllImages()">
     <input TYPE="button" VALUE="图片展示" onClick="showImage()">
     <input TYPE="button" VALUE="银行卡识别" onClick="ocrbankcard()">
</body>
</html>
