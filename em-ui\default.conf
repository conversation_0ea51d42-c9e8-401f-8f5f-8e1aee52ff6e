
server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;
    
    #access_log  /var/log/nginx/host.access.log  main;

    location /xdemui {
        root   /usr/share/nginx/html;
        index  index.html index.htm;
        try_files $uri $uri/ /xdemui/index.html;
    }

    location /xdemserv/ {
        client_max_body_size 600M;
        client_body_buffer_size 600M;
        proxy_read_timeout 1800;
        proxy_connect_timeout 1800;
        proxy_send_timeout 1800;
        proxy_pass http://127.0.0.1:8080/;
    }

    location /prod-api/ {
        client_max_body_size 600M;
        client_body_buffer_size 600M;
        proxy_read_timeout 1800;
        proxy_connect_timeout 1800;
        proxy_send_timeout 1800;
        proxy_pass http://127.0.0.1:8080/;
    }

    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    # error_page   500 502 503 504  /50x.html;
    # location = /50x.html {
    #     root   /usr/share/nginx/html;
    # }

}
